# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_artists
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-07 13:30+0000\n"
"PO-Revision-Date: 2024-09-25 18:03+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quotes_carousel_minimal
msgid ""
"\" A visionary partner for growth. <br/>Innovative, skilled, and always "
"pushing creative boundaries. \""
msgstr ""
"\"Um parceiro visionário para o crescimento. <br/>Inovador, habilidoso e "
"sempre extendendo os limites criativos.\""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quotes_carousel_minimal
msgid ""
"\" Outstanding artistry and service! <br/>They consistently exceed our "
"creative expectations. \""
msgstr ""
"\"Arte e serviço excepcionais! <br/>Eles sempre superam nossas expectativas "
"criativas.\""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quotes_carousel_minimal
msgid "\" Their creativity transformed our vision. Unique and truly inspiring. \""
msgstr ""
"\"A criatividade deles transformou nossa visão. Único e de fato "
"inspirador.\""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,200.00"
msgstr "US$ 1.200,00"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,300.00"
msgstr "US$ 1.300,00"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,350.00"
msgstr "US$ 1.350,00"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,450.00"
msgstr "US$ 1.450,00"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,500.00"
msgstr "US$ 1.500,00"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,600.00"
msgstr "US$ 1.600,00"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "02.08.2025"
msgstr "08.02.2025"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "03.01.2026"
msgstr "01.03.2026"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "09.09.2025"
msgstr "09.09.2025"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "12.11.2025"
msgstr "11.12.2025"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "12.12.2025"
msgstr "12.12.2025"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "<b>Painting concept / April 2021</b>"
msgstr "<b>Conceito de pintura/Abril de 2021</b>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "<b>Photoshoot / June 2021</b>"
msgstr "<b>Sessão de fotos/Junho de 2021</b>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "<b>Typographic collection / March 2021</b>"
msgstr "<b>Coleção tipográfica/Março de 2021</b>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_sidegrid
msgid ""
"<br/>Art is a journey of expression, where every stroke and color tells a "
"story waiting to be unveiled.<br/><br/>"
msgstr ""
"<br/>A arte é uma jornada de expressão, na qual cada traço e cada cor contam"
" uma história que está esperando para ser revelada.<br/><br/>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_text_cover
msgid ""
"<br/>Showcase your artwork and updates online with a user-friendly platform "
"that simplifies everything, making it easy for art lovers to discover and "
"appreciate your creations.<br/>"
msgstr ""
"<br/>Exiba suas obras de arte e dê atualizações on-line com uma plataforma "
"fácil de usar que simplifica tudo, trazendo amantes da arte para apreciarem "
"suas criações<br/>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_empowerment
msgid "<br/>The very last date of our World tour !<br/><br/>"
msgstr "<br/>A última data de nossa turnê mundial!<br/><br/>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_big_number
msgid ""
"<font class=\"text-gradient\" style=\"background-image: linear-gradient(0deg, rgb(165, 78, 223) 8%, rgb(222, 222, 222) 80%);\">\n"
"            250+\n"
"        </font>"
msgstr ""
"<font class=\"text-gradient\" style=\"background-image: linear-gradient(0deg, rgb(165, 78, 223) 8%, rgb(222, 222, 222) 80%);\">\n"
"            +250\n"
"        </font>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_empowerment
msgid ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/>  Don't "
"miss the final show !"
msgstr ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/>  Não "
"perca o último show!"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_image_title
msgid "A Deep Dive into Creativity and Excellence"
msgstr "Um mergulho profundo na criatividade e na excelência"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"A classical piece inspired by Renaissance techniques, featuring intricate "
"details and a rich color palette."
msgstr ""
"Uma peça clássica inspirada nas técnicas da Renascença, com detalhes "
"intrincados e uma rica paleta de cores."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"A tranquil garden scene that blends soft greens and pastel flowers, creating"
" a peaceful and calming atmosphere."
msgstr ""
"Uma cena de jardim tranquila que combina verdes suaves e flores em tons "
"pastéis, criando uma atmosfera de paz e tranquilidade."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"A vibrant abstract painting that blends bold colors and dynamic shapes, "
"creating a sense of harmony and movement."
msgstr ""
"Uma pintura abstrata vibrante que combina cores fortes e formas dinâmicas, "
"criando uma sensação de harmonia e movimento."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "About me"
msgstr "Sobre mim"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Abstract Harmony"
msgstr "Harmonia abstrata"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Abstract Vision"
msgstr "Visão abstrata"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Amsterdam"
msgstr "Amsterdã"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"An imaginative dreamscape in deep blues and purples, this piece invites "
"viewers to explore a world of night-time fantasy."
msgstr ""
"Uma paisagem de sonho imaginativa em azuis e roxos profundos, esta peça "
"convida os espectadores a explorar um mundo de fantasia noturna."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "Aquitani"
msgstr "Aquitani"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid "Art Restoration &amp; Repair"
msgstr "Restauração e reparo de arte"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_center_top
msgid "Art that speaks to your soul"
msgstr "Arte que toca a sua alma"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Artistic journey around the world"
msgstr "Viagem artística ao redor do mundo"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Berlin"
msgstr "Berlim"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Berlin Art Week"
msgstr "Semana de Arte de Berlim"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid "Bespoke Art Creations"
msgstr "Criações artísticas personalizadas"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid "Bold Use of Color and Texture"
msgstr "Uso arrojado de cores e texturas"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid ""
"Bring your vision to life with personalized artwork created by talented "
"artists, tailored to your unique preferences."
msgstr ""
"Dê vida à sua visão com obras de arte personalizadas criadas por artistas "
"talentosos, adaptadas às suas preferências exclusivas."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Brussels"
msgstr "Bruxelas"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"Capturing the serene beauty of a sunset, this painting uses warm tones and "
"soft brushstrokes to evoke a sense of peace."
msgstr ""
"Capturando a beleza serena de um pôr do sol, esta pintura usa tons quentes e"
" pinceladas suaves para evocar uma sensação de paz."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quadrant
msgid "Celebrating Artistic Expression"
msgstr "Celebrando a expressão artística"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_top
msgid ""
"Color trends are already waiting to spring into action for the next summer."
msgstr ""
"As tendências de cores já estão esperando para entrar em cena no próximo "
"verão."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Comput'Art"
msgstr "Comput'Art"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_shape_image
msgid "Contact Me"
msgstr "Entre em contato comigo"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "Contact me"
msgstr "Entre em contato"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_call_to_action
msgid "Contact me for a visit of my studio."
msgstr "Entre em contato comigo para visitar meu estúdio."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid "Custom Art Commissions"
msgstr "Comissões de arte personalizadas"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid "Custom Creations"
msgstr "Criações personalizadas"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid "Dedicated Artist Support"
msgstr "Suporte dedicado ao artista"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Digital Art Paris"
msgstr "Arte Digital Paris"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cta_box
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "Discover"
msgstr "Descobrir"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_image_text
msgid "Discover <br/>my works"
msgstr "Conheça <br/>meus trabalhos"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_card_offset
msgid "Discover Art that Inspires Your Soul"
msgstr "Descubra a arte que inspira sua alma"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid ""
"Discover limited-edition prints of stunning original artworks, perfect for "
"enhancing your home or office space."
msgstr ""
"Descubra impressões de edição limitada de obras de arte originais "
"impressionantes, perfeitas para aprimorar sua casa ou escritório."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cta_box
msgid "Discover new creations<br/>and artistic masterpieces !"
msgstr "Descubra novas criações<br/> e obras-primas artísticas!"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_images_mosaic
msgid "Discover our new and inspiring artistic paint collections."
msgstr "Descubra nossas novas e inspiradoras coleções de tintas artísticas."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Discover the Colors that Transform Spaces"
msgstr "Descubra as cores que transformam os espaços"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid "Discover your potential"
msgstr "Descubra seu potencial"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid "Distinctive Creations: Art That Speaks"
msgstr "Criações diferenciadas: Arte que fala"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_center_top
msgid ""
"Dive into a world of creativity where art meets innovation, and find "
"inspiration through galleries, exhibitions, and unique artistic expressions."
msgstr ""
"Mergulhe em um mundo de criatividade onde a arte encontra a inovação e "
"encontre inspiração em galerias, exposições e expressões artísticas "
"exclusivas."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_shape_image
msgid "Dylan Doe"
msgstr "Dylan Doe"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_shape_image
msgid ""
"Dylan Doe is a 3D artist known for his vibrant and immersive digital "
"creations that captivate audiences worldwide."
msgstr ""
"Dylan Doe é um artista 3D conhecido por suas criações digitais vibrantes e "
"imersivas que cativam o público em todo o mundo."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid ""
"Each piece is a one-of-a-kind creation, meticulously handcrafted to convey "
"emotion, thought, and imagination in every brushstroke or sculpted form."
msgstr ""
"Cada peça é uma criação única, meticulosamente trabalhada à mão para "
"transmitir emoção, pensamento e imaginação em cada pincelada ou forma "
"esculpida."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Eco-friendly options for a sustainable future"
msgstr "Opções ecologicamente corretas para um futuro sustentável"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Elevate your home with vibrant, rich colors"
msgstr "Reavive sua casa com cores vibrantes e ricas"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cta_box
msgid ""
"Elevate your space with the unique wonders of artistic "
"craftsmanship.<br/><br/>"
msgstr ""
"Eleve seu espaço com as maravilhas exclusivas do artesanato "
"artístico.<br/><br/>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid "Elevating your creative vision"
msgstr "Elevando sua visão criativa"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cover
msgid "Emotions <br/>through the colors"
msgstr "Emoções <br/>através das cores"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_banner
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_framed_intro
msgid "Emotions through the colors"
msgstr "Emoções através das cores"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid ""
"Enhance your talent with our exclusive guidance, showcasing the best version"
" of your artistic expression, every day."
msgstr ""
"Aprimore seu talento com nossa orientação exclusiva, mostrando a melhor "
"versão de sua expressão artística, todos os dias."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid "Exclusive Art Prints"
msgstr "Impressões artísticas exclusivas"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid "Exclusive Artworks"
msgstr "Obras de arte exclusivas"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid "Expertise and Inspiration"
msgstr "Experiência e inspiração"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_center_top
msgid "Explore Now"
msgstr "Explorar agora"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Explore Our Premium Paint Collection"
msgstr "Explore nossa coleção de tintas premium"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_card_offset
msgid ""
"Explore a curated selection of artwork from emerging and established "
"artists. Our galleries are designed to ignite creativity and connect you "
"with timeless pieces."
msgstr ""
"Explore uma seleção com curadoria de obras de arte de artistas emergentes e "
"consagrados. Nossas galerias são projetadas para despertar a criatividade e "
"conectá-lo com peças atemporais."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid ""
"Explore more and find unique projects that align with your vision, crafted "
"for artistic growth and recognition."
msgstr ""
"Explore mais e encontre projetos exclusivos que se alinham com sua visão, "
"criados para o crescimento artístico e o reconhecimento."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid ""
"Explore one-of-a-kind pieces and limited editions that showcase exceptional "
"creativity and offer a distinctive touch to your collection."
msgstr ""
"Explore peças únicas e edições limitadas que demonstram uma criatividade "
"excepcional e oferecem um toque diferenciado à sua coleção."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_freegrid
msgid ""
"Explore our carefully curated selection of artworks that push the boundaries"
" of creativity and innovation. Our galleries showcase the finest pieces that"
" inspire and captivate."
msgstr ""
"Explore nossa seleção cuidadosamente selecionada de obras de arte que "
"ultrapassam os limites da criatividade e da inovação. Nossas galerias exibem"
" as melhores peças que inspiram e cativam."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"Explore our curated selection of artistic masterpieces. Each piece is "
"thoughtfully crafted to inspire and captivate."
msgstr ""
"Explore nossa seleção selecionada de obras-primas artísticas. Cada peça é "
"cuidadosamente elaborada para inspirar e cativar."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_top
msgid "Exploring Art's Masterpieces: A Journey to the Best Creations"
msgstr ""
"Explorando as obras-primas da arte: Uma jornada pelas melhores criações"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid ""
"Formerly a corporate accountant, I started a professional transition 10 "
"years ago."
msgstr "Ex-contabilista, comecei minha transição profissional há 10 anos."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_empowerment
msgid "Grab your tickets   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"
msgstr "Adquira seus ingressos   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid ""
"Here is a selection of my works, exhibited in my studio and in galleries "
"around the world."
msgstr ""
"Aqui está uma seleção de meus trabalhos, exibidos em meu estúdio e em "
"galerias de todo o mundo."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid ""
"I offer bespoke art commissions, collaborating closely with clients to "
"create custom pieces that resonate deeply with their personal stories and "
"spaces."
msgstr ""
"Trabalho com artes encomendadas sob medida, colaborando estreitamente com os"
" clientes para criar peças personalizadas que ressoem profundamente com suas"
" histórias e espaços pessoais."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "Immemorabili"
msgstr "Immemorabili"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quadrant
msgid ""
"Immerse yourself in the world of art. Our galleries showcase a diverse range"
" of creativity, from paintings to photography.<br/><br/> Discover the works "
"that speak to your soul and elevate your space."
msgstr ""
"Mergulhe fundo no mundo da arte. Nossas galerias exibem uma gama "
"diversificada de criatividade, de pinturas a fotografias.<br/><br/> Conheça "
"as obras que tocarão sua alma e elevarão seu espaço."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Innovative paints for long-lasting beauty"
msgstr "Tintas inovadoras para embelezamento permanente"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_title
msgid "It's all about perception"
msgstr "Percepção é a chave"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_sidegrid
msgid "Join the artistic<br/>revolution"
msgstr "Participe da revolução<br/>artística"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_text_cover
msgid "Learn more"
msgstr "Saiba mais"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_card_offset
msgid "Let art be the language of your space."
msgstr "Deixe que a arte seja a linguagem de seu espaço."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_text_image
msgid "Let yourself <br/>be transported"
msgstr "Deixe-se <br/>transportar"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "London"
msgstr "Londres"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_empowerment
msgid "Lunar Echoes:<br/>Around the World tour"
msgstr "Lunar Echoes: <br/>turnê pelo mundo"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid "Mastery and Quality"
msgstr "Domínio e qualidade"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Midnight Dreamscape"
msgstr "Paisagem dos sonhos da meia-noite"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "More details"
msgstr "Mais detalhes"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid ""
"My art is a reflection of a distinctive vision, blending contemporary "
"techniques with personal storytelling to create pieces that captivate and "
"inspire."
msgstr ""
"Minha arte é um reflexo de uma visão distinta, combinando técnicas "
"contemporâneas com narrativas pessoais para criar peças que cativam e "
"inspiram."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "My artistic process"
msgstr "Meu processo artístico"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "My work"
msgstr "Meu trabalho"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid ""
"My work is known for its vibrant colors and dynamic textures, pushing the "
"boundaries of conventional art forms to evoke a strong emotional response."
msgstr ""
"Meu trabalho é conhecido por suas cores vibrantes e texturas dinâmicas, "
"ultrapassando os limites das formas de arte convencionais para evocar uma "
"forte resposta emocional."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "New York"
msgstr "Nova York"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid "Original and Handcrafted Works"
msgstr "Trabalhos originais e artesanais"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Our Gallery"
msgstr "Nossa galeria"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Paris"
msgstr "Paris"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Perfect shades for every surface and style"
msgstr "Tons perfeitos para cada superfície e estilo"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid "Personalized Art Commissions"
msgstr "Comissões de arte personalizadas"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid ""
"Receive ongoing guidance and assistance throughout your project, ensuring "
"that your artistic needs and preferences are fully realized."
msgstr ""
"Receba orientação e assistência contínuas durante todo o seu projeto, "
"garantindo que suas necessidades e preferências artísticas sejam totalmente "
"atendidas."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid ""
"Refresh your portfolio with our expert advice and unique opportunities."
msgstr ""
"Atualize seu portfólio com nossa consultoria especializada e oportunidades "
"exclusivas."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Renaissance Revival"
msgstr "Estilo Renascentista"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid ""
"Restore your cherished art pieces to their former glory with expert "
"cleaning, repair, and preservation services."
msgstr ""
"Restaure suas peças de arte mais queridas com serviços especializados de "
"limpeza, reparo e preservação."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Royal Museums of Fine Arts of Belgium"
msgstr "Museus Reais de Belas Artes da Bélgica"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_text_cover
msgid "Share Art.<br/>Effortlessly."
msgstr "Compartilhar arte.<br/>Sem esforço."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Sunset Serenade"
msgstr "Serenata ao pôr do sol"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid "Sustainable Art"
msgstr "Arte sustentável"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "The Garden of Serenity"
msgstr "O Jardim da Serenidade"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "The Museum of Modern Art (MoMA)"
msgstr "O Museu de Arte Moderna (MoMA)"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid ""
"The choice of materials, colours are not insignificant, discover more about "
"my artistic process."
msgstr ""
"A escolha de materiais e cores é intencional. Conheça mais do meu processo "
"artístico."

#. module: theme_artists
#: model:ir.model,name:theme_artists.model_theme_utils
msgid "Theme Utils"
msgstr "Utilitários de temas"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"This modern piece captures the energy of city life, using sharp lines and "
"reflective surfaces to portray an urban landscape."
msgstr ""
"Essa peça moderna captura a energia da vida na cidade, usando linhas nítidas"
" e superfícies reflexivas para retratar uma paisagem urbana."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_empowerment
msgid "Tour agenda"
msgstr "Agenda da turnê"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_image_title
msgid ""
"Transform your surroundings with our latest art collection, where "
"imagination meets technique. Elevate your space with pieces that blend "
"artistic expression and aesthetic beauty effortlessly."
msgstr ""
"Transforme seu ambiente com nossa mais recente coleção de arte, onde a "
"imaginação encontra a técnica. Eleve seu espaço com peças que combinam "
"expressão artística e beleza estética."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid "Unique Creative Vision"
msgstr "Visão criativa exclusiva"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_center_top
msgid "Unleash Your Creative"
msgstr "Liberte sua criatividade"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_images_mosaic
msgid "Unveiling Our Latest Artistic Creations"
msgstr "Apresentação nossas mais recentes criações artísticas"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Upcoming Exhibitions"
msgstr "Próximas exposições"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Urban Reflection"
msgstr "Reflexão urbana"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "Vincam"
msgstr "Vincam"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quadrant
msgid "Visit Galleries"
msgstr "Visitar galerias"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_freegrid
msgid "Visit the Gallery"
msgstr "Visite a galeria"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_call_to_action
msgid "Want to discover more?"
msgstr "Quer saber mais?"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid ""
"We craft personalized art to express your unique vision. Our team works "
"closely with you to bring your ideas to life from concept to completion."
msgstr ""
"Criamos artes personalizadas para expressar sua visão exclusiva. Nossa "
"equipe trabalha com você para dar vida às suas ideias, desde o conceito até "
"a conclusão."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid ""
"We craft personalized artworks that capture your vision and style, "
"delivering unique pieces that reflect your individuality."
msgstr ""
"Criamos obras de arte personalizadas que capturam sua visão e estilo, "
"oferecendo peças exclusivas para refletir sua individualidade."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid ""
"We provide innovative art solutions for today’s challenges. Using cutting-"
"edge methods, we help you achieve your creative goals."
msgstr ""
"Fornecemos soluções artísticas inovadoras para os desafios atuais. Com "
"métodos de ponta, ajudamos você a atingir suas metas criativas."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_text_image
msgid ""
"Welcome to my online home. I invite you to come in, learn more about me and "
"my work, and stop to embrace the slow pleasure of art and beauty."
msgstr ""
"Boas-vindas ao meu lar online. Você está convidado a entrar, conhecer meu "
"trabalho e eu, e parar um momento para aproveitar o prazer lento da arte e "
"da beleza."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid "What we create for our customers"
msgstr "O que criamos para nossos clientes"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_freegrid
msgid "Where Creativity Meets Innovation in Art"
msgstr "Onde a criatividade encontra a inovação na arte"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid ""
"With vast experience and artistic insight, we offer techniques and concepts "
"that keep you ahead in the art world."
msgstr ""
"Com vasta experiência e visão artística, oferecemos técnicas e conceitos que"
" o mantêm à frente no mundo da arte."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid "Your art, curated for success"
msgstr "Sua arte, com curadoria para o sucesso"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid ""
"Your satisfaction is our passion. Our support team is always available, "
"ensuring your artistic journey is seamless and fulfilling."
msgstr ""
"Sua satisfação é a nossa paixão. Nossa equipe de suporte está sempre "
"disponível, garantindo que sua jornada artística seja tranquila e "
"gratificante."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_big_number
msgid "songs released"
msgstr "músicas lançadas"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "✽  Classic Art"
msgstr "✽  Arte clássica"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "✽  Modern Art"
msgstr "✽  Arte moderna"
