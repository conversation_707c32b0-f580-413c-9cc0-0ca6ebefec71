# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_artists
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-07 13:30+0000\n"
"PO-Revision-Date: 2024-09-25 18:03+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quotes_carousel_minimal
msgid ""
"\" A visionary partner for growth. <br/>Innovative, skilled, and always "
"pushing creative boundaries. \""
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quotes_carousel_minimal
msgid ""
"\" Outstanding artistry and service! <br/>They consistently exceed our "
"creative expectations. \""
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quotes_carousel_minimal
msgid "\" Their creativity transformed our vision. Unique and truly inspiring. \""
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,200.00"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,300.00"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,350.00"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,450.00"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,500.00"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,600.00"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "02.08.2025"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "03.01.2026"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "09.09.2025"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "12.11.2025"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "12.12.2025"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "<b>Painting concept / April 2021</b>"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "<b>Photoshoot / June 2021</b>"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "<b>Typographic collection / March 2021</b>"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_sidegrid
msgid ""
"<br/>Art is a journey of expression, where every stroke and color tells a "
"story waiting to be unveiled.<br/><br/>"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_text_cover
msgid ""
"<br/>Showcase your artwork and updates online with a user-friendly platform "
"that simplifies everything, making it easy for art lovers to discover and "
"appreciate your creations.<br/>"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_empowerment
msgid "<br/>The very last date of our World tour !<br/><br/>"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_big_number
msgid ""
"<font class=\"text-gradient\" style=\"background-image: linear-gradient(0deg, rgb(165, 78, 223) 8%, rgb(222, 222, 222) 80%);\">\n"
"            250+\n"
"        </font>"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_empowerment
msgid ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/>  Don't "
"miss the final show !"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_image_title
msgid "A Deep Dive into Creativity and Excellence"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"A classical piece inspired by Renaissance techniques, featuring intricate "
"details and a rich color palette."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"A tranquil garden scene that blends soft greens and pastel flowers, creating"
" a peaceful and calming atmosphere."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"A vibrant abstract painting that blends bold colors and dynamic shapes, "
"creating a sense of harmony and movement."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "About me"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Abstract Harmony"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Abstract Vision"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Amsterdam"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"An imaginative dreamscape in deep blues and purples, this piece invites "
"viewers to explore a world of night-time fantasy."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "Aquitani"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid "Art Restoration &amp; Repair"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_center_top
msgid "Art that speaks to your soul"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Artistic journey around the world"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Berlin"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Berlin Art Week"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid "Bespoke Art Creations"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid "Bold Use of Color and Texture"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid ""
"Bring your vision to life with personalized artwork created by talented "
"artists, tailored to your unique preferences."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Brussels"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"Capturing the serene beauty of a sunset, this painting uses warm tones and "
"soft brushstrokes to evoke a sense of peace."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quadrant
msgid "Celebrating Artistic Expression"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_top
msgid ""
"Color trends are already waiting to spring into action for the next summer."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Comput'Art"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_shape_image
msgid "Contact Me"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "Contact me"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_call_to_action
msgid "Contact me for a visit of my studio."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid "Custom Art Commissions"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid "Custom Creations"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid "Dedicated Artist Support"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Digital Art Paris"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cta_box
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "Discover"
msgstr "Opdag"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_image_text
msgid "Discover <br/>my works"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_card_offset
msgid "Discover Art that Inspires Your Soul"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid ""
"Discover limited-edition prints of stunning original artworks, perfect for "
"enhancing your home or office space."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cta_box
msgid "Discover new creations<br/>and artistic masterpieces !"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_images_mosaic
msgid "Discover our new and inspiring artistic paint collections."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Discover the Colors that Transform Spaces"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid "Discover your potential"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid "Distinctive Creations: Art That Speaks"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_center_top
msgid ""
"Dive into a world of creativity where art meets innovation, and find "
"inspiration through galleries, exhibitions, and unique artistic expressions."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_shape_image
msgid "Dylan Doe"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_shape_image
msgid ""
"Dylan Doe is a 3D artist known for his vibrant and immersive digital "
"creations that captivate audiences worldwide."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid ""
"Each piece is a one-of-a-kind creation, meticulously handcrafted to convey "
"emotion, thought, and imagination in every brushstroke or sculpted form."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Eco-friendly options for a sustainable future"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Elevate your home with vibrant, rich colors"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cta_box
msgid ""
"Elevate your space with the unique wonders of artistic "
"craftsmanship.<br/><br/>"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid "Elevating your creative vision"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cover
msgid "Emotions <br/>through the colors"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_banner
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_framed_intro
msgid "Emotions through the colors"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid ""
"Enhance your talent with our exclusive guidance, showcasing the best version"
" of your artistic expression, every day."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid "Exclusive Art Prints"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid "Exclusive Artworks"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid "Expertise and Inspiration"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_center_top
msgid "Explore Now"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Explore Our Premium Paint Collection"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_card_offset
msgid ""
"Explore a curated selection of artwork from emerging and established "
"artists. Our galleries are designed to ignite creativity and connect you "
"with timeless pieces."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid ""
"Explore more and find unique projects that align with your vision, crafted "
"for artistic growth and recognition."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid ""
"Explore one-of-a-kind pieces and limited editions that showcase exceptional "
"creativity and offer a distinctive touch to your collection."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_freegrid
msgid ""
"Explore our carefully curated selection of artworks that push the boundaries"
" of creativity and innovation. Our galleries showcase the finest pieces that"
" inspire and captivate."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"Explore our curated selection of artistic masterpieces. Each piece is "
"thoughtfully crafted to inspire and captivate."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_top
msgid "Exploring Art's Masterpieces: A Journey to the Best Creations"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid ""
"Formerly a corporate accountant, I started a professional transition 10 "
"years ago."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_empowerment
msgid "Grab your tickets   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid ""
"Here is a selection of my works, exhibited in my studio and in galleries "
"around the world."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid ""
"I offer bespoke art commissions, collaborating closely with clients to "
"create custom pieces that resonate deeply with their personal stories and "
"spaces."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "Immemorabili"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quadrant
msgid ""
"Immerse yourself in the world of art. Our galleries showcase a diverse range"
" of creativity, from paintings to photography.<br/><br/> Discover the works "
"that speak to your soul and elevate your space."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Innovative paints for long-lasting beauty"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_title
msgid "It's all about perception"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_sidegrid
msgid "Join the artistic<br/>revolution"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_text_cover
msgid "Learn more"
msgstr "Lær mere"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_card_offset
msgid "Let art be the language of your space."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_text_image
msgid "Let yourself <br/>be transported"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "London"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_empowerment
msgid "Lunar Echoes:<br/>Around the World tour"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid "Mastery and Quality"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Midnight Dreamscape"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "More details"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid ""
"My art is a reflection of a distinctive vision, blending contemporary "
"techniques with personal storytelling to create pieces that captivate and "
"inspire."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "My artistic process"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "My work"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid ""
"My work is known for its vibrant colors and dynamic textures, pushing the "
"boundaries of conventional art forms to evoke a strong emotional response."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "New York"
msgstr "New York"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid "Original and Handcrafted Works"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Our Gallery"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Paris"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Perfect shades for every surface and style"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid "Personalized Art Commissions"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid ""
"Receive ongoing guidance and assistance throughout your project, ensuring "
"that your artistic needs and preferences are fully realized."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid ""
"Refresh your portfolio with our expert advice and unique opportunities."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Renaissance Revival"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid ""
"Restore your cherished art pieces to their former glory with expert "
"cleaning, repair, and preservation services."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Royal Museums of Fine Arts of Belgium"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_text_cover
msgid "Share Art.<br/>Effortlessly."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Sunset Serenade"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid "Sustainable Art"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "The Garden of Serenity"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "The Museum of Modern Art (MoMA)"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid ""
"The choice of materials, colours are not insignificant, discover more about "
"my artistic process."
msgstr ""

#. module: theme_artists
#: model:ir.model,name:theme_artists.model_theme_utils
msgid "Theme Utils"
msgstr "Tema værktøjer"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"This modern piece captures the energy of city life, using sharp lines and "
"reflective surfaces to portray an urban landscape."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_empowerment
msgid "Tour agenda"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_image_title
msgid ""
"Transform your surroundings with our latest art collection, where "
"imagination meets technique. Elevate your space with pieces that blend "
"artistic expression and aesthetic beauty effortlessly."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid "Unique Creative Vision"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_center_top
msgid "Unleash Your Creative"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_images_mosaic
msgid "Unveiling Our Latest Artistic Creations"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Upcoming Exhibitions"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Urban Reflection"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "Vincam"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quadrant
msgid "Visit Galleries"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_freegrid
msgid "Visit the Gallery"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_call_to_action
msgid "Want to discover more?"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid ""
"We craft personalized art to express your unique vision. Our team works "
"closely with you to bring your ideas to life from concept to completion."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid ""
"We craft personalized artworks that capture your vision and style, "
"delivering unique pieces that reflect your individuality."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid ""
"We provide innovative art solutions for today’s challenges. Using cutting-"
"edge methods, we help you achieve your creative goals."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_text_image
msgid ""
"Welcome to my online home. I invite you to come in, learn more about me and "
"my work, and stop to embrace the slow pleasure of art and beauty."
msgstr ""
"Velkommen til mit online hjem. Jeg inviterer dig til at komme ind, lære mere"
" om mig og mit arbejde, og stoppe op for at omfavne kunstens og skønhedens "
"langsomme fornøjelse."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid "What we create for our customers"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_freegrid
msgid "Where Creativity Meets Innovation in Art"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid ""
"With vast experience and artistic insight, we offer techniques and concepts "
"that keep you ahead in the art world."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid "Your art, curated for success"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid ""
"Your satisfaction is our passion. Our support team is always available, "
"ensuring your artistic journey is seamless and fulfilling."
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_big_number
msgid "songs released"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "✽  Classic Art"
msgstr ""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "✽  Modern Art"
msgstr ""
