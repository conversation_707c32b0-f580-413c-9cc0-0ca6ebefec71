# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_artists
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-07 13:30+0000\n"
"PO-Revision-Date: 2024-09-25 18:03+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quotes_carousel_minimal
msgid ""
"\" A visionary partner for growth. <br/>Innovative, skilled, and always "
"pushing creative boundaries. \""
msgstr ""
"„Ein vorausschauender Partner für Wachstum. <br/>Innovativ, talentiert und "
"durchaus kreativ.“"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quotes_carousel_minimal
msgid ""
"\" Outstanding artistry and service! <br/>They consistently exceed our "
"creative expectations. \""
msgstr ""
"„Erstklassige Kunst und Service! <br/>Sie übertreffen immer wieder unsere "
"Erwartungen.“"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quotes_carousel_minimal
msgid "\" Their creativity transformed our vision. Unique and truly inspiring. \""
msgstr ""
"„Ihre krativität hat unsere Vision verändert. Einzigartig und einfach "
"inspierend.“"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,200.00"
msgstr "1.200,00 €"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,300.00"
msgstr "1.300,00 €"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,350.00"
msgstr "1.350,00 €"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,450.00"
msgstr "1.450,00 €"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,500.00"
msgstr "1.500,00 €"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,600.00"
msgstr "1.600,00 €"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "02.08.2025"
msgstr "02.08.2025"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "03.01.2026"
msgstr "03.01.2026"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "09.09.2025"
msgstr "09.09.2025"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "12.11.2025"
msgstr "12.11.2025"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "12.12.2025"
msgstr "12.12.2025"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "<b>Painting concept / April 2021</b>"
msgstr "<b>Painting concept / April 2021</b>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "<b>Photoshoot / June 2021</b>"
msgstr "<b>Photoshoot / Juni 2021</b>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "<b>Typographic collection / March 2021</b>"
msgstr "<b>Typographische Kollektion / März 2021</b>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_sidegrid
msgid ""
"<br/>Art is a journey of expression, where every stroke and color tells a "
"story waiting to be unveiled.<br/><br/>"
msgstr ""
"<br/>Kunst ist eine Reise durch Eindrücke, wo jeder Strich und jede Farbe "
"eine Geschichte erzählt, die darauf wartet, enthüllt zu werden.<br/><br/>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_text_cover
msgid ""
"<br/>Showcase your artwork and updates online with a user-friendly platform "
"that simplifies everything, making it easy for art lovers to discover and "
"appreciate your creations.<br/>"
msgstr ""
"<br/>Präsentieren Sie Ihre Kunstwerke und Updates online auf einer "
"benutzerfreundlichen Plattform, die alles vereinfacht und es Kunstliebhabern"
" leicht macht, Ihre Kreationen zu entdecken und zu schätzen.<br/>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_empowerment
msgid "<br/>The very last date of our World tour !<br/><br/>"
msgstr "<br/>Das allerletzte Datum unserer Welttour!<br/><br/>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_big_number
msgid ""
"<font class=\"text-gradient\" style=\"background-image: linear-gradient(0deg, rgb(165, 78, 223) 8%, rgb(222, 222, 222) 80%);\">\n"
"            250+\n"
"        </font>"
msgstr ""
"<font class=\"text-gradient\" style=\"background-image: linear-gradient(0deg, rgb(165, 78, 223) 8%, rgb(222, 222, 222) 80%);\">\n"
"            250+\n"
"        </font>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_empowerment
msgid ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/>  Don't "
"miss the final show !"
msgstr ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" "
"role=\"img\"/>  Verpassen Sie nicht die letzte Show!"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_image_title
msgid "A Deep Dive into Creativity and Excellence"
msgstr "Ein tiefer Einblick in Krativität und Exzellenz"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"A classical piece inspired by Renaissance techniques, featuring intricate "
"details and a rich color palette."
msgstr ""
"Ein klassisches Werk, das von der Renaissance inspiriert ist und sich durch "
"aufwendige Details und eine reiche Farbpalette auszeichnet."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"A tranquil garden scene that blends soft greens and pastel flowers, creating"
" a peaceful and calming atmosphere."
msgstr ""
"Eine ruhige Gartenszene, die sanfte Grüntöne und pastellfarbene Blumen "
"miteinander verbindet und eine friedliche und beruhigende Atmosphäre "
"schafft."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"A vibrant abstract painting that blends bold colors and dynamic shapes, "
"creating a sense of harmony and movement."
msgstr ""
"Ein lebendiges abstraktes Gemälde, das kräftige Farben und dynamische Formen"
" miteinander verbindet und so ein Gefühl von Harmonie und Bewegung erzeugt."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "About me"
msgstr "Über mich"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Abstract Harmony"
msgstr "Abstract Harmony"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Abstract Vision"
msgstr "Abstract Harmony"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Amsterdam"
msgstr "Amsterdam"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"An imaginative dreamscape in deep blues and purples, this piece invites "
"viewers to explore a world of night-time fantasy."
msgstr ""
"Dieses Werk ist eine fantasievolle Traumlandschaft in tiefen Blau- und "
"Violetttönen und lädt den Betrachter ein, eine Welt der nächtlichen Fantasie"
" zu erkunden."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "Aquitani"
msgstr "Aquitani"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid "Art Restoration &amp; Repair"
msgstr "Kunstrestaurierung &amp; Reparatur"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_center_top
msgid "Art that speaks to your soul"
msgstr "Kunst, die zu Ihrer Seele spricht"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Artistic journey around the world"
msgstr "Künstlerische Reise um die Welt"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Berlin"
msgstr "Berlin"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Berlin Art Week"
msgstr "Berlin-Kunstwoche"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid "Bespoke Art Creations"
msgstr "Individuelle Kunstwerke"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid "Bold Use of Color and Texture"
msgstr "Mutige Verwendung von Farbe und Textur"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid ""
"Bring your vision to life with personalized artwork created by talented "
"artists, tailored to your unique preferences."
msgstr ""
"Erwecken Sie Ihre Visionen zum Leben, indem Sie ein von talentierten "
"Künstlern geschaffenes, auf Ihre individuellen Wünsche zugeschnittenes "
"Kunstwerk anfertigen."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Brussels"
msgstr "Brüssel"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"Capturing the serene beauty of a sunset, this painting uses warm tones and "
"soft brushstrokes to evoke a sense of peace."
msgstr ""
"Dieses Gemälde fängt die ruhige Schönheit eines Sonnenuntergangs ein und "
"verwendet warme Farbtöne und sanfte Pinselstriche, um ein Gefühl des "
"Friedens zu vermitteln."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quadrant
msgid "Celebrating Artistic Expression"
msgstr "Künstlerischer Ausdruck wird gefeiert"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_top
msgid ""
"Color trends are already waiting to spring into action for the next summer."
msgstr ""
"Die Farbtrends für den nächsten Sommer stehen schon in den Startlöchern."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Comput'Art"
msgstr "Comput'Art"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_shape_image
msgid "Contact Me"
msgstr "Kontakt"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "Contact me"
msgstr "Kontaktieren Sie mich"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_call_to_action
msgid "Contact me for a visit of my studio."
msgstr "Kontaktieren Sie mich für einen Atelier-Rundgang."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid "Custom Art Commissions"
msgstr "Individuelle Auftragsarbeit"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid "Custom Creations"
msgstr "Individuelle Werke"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid "Dedicated Artist Support"
msgstr "Spezialisierter Künstlersupport"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Digital Art Paris"
msgstr "Digitale Kunst in Paris"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cta_box
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "Discover"
msgstr "Entdecken"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_image_text
msgid "Discover <br/>my works"
msgstr "Meine Arbeiten <br/>entdecken"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_card_offset
msgid "Discover Art that Inspires Your Soul"
msgstr "Entdecken Sie Kunst, die Ihre Seele inspiriert"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid ""
"Discover limited-edition prints of stunning original artworks, perfect for "
"enhancing your home or office space."
msgstr ""
"Entdecken Sie limitierte Abzüge atemberaubender Originalkunstwerken, perfekt"
" zur Verschönerung Ihres Zuhauses oder Büros."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cta_box
msgid "Discover new creations<br/>and artistic masterpieces !"
msgstr "Entdecken Sie neue Werke<br/>und künstlerliche Meisterwerke!"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_images_mosaic
msgid "Discover our new and inspiring artistic paint collections."
msgstr "Entdecken Sie unsere neuen und inspirierenden Kunstsammlungen."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Discover the Colors that Transform Spaces"
msgstr "Entdecken Sie die Farben, die ganze Räume verändern"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid "Discover your potential"
msgstr "Entfesseln Sie Ihr Potenzial"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid "Distinctive Creations: Art That Speaks"
msgstr "Außergewöhnliche Werke: Kunst, die spricht"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_center_top
msgid ""
"Dive into a world of creativity where art meets innovation, and find "
"inspiration through galleries, exhibitions, and unique artistic expressions."
msgstr ""
"Tauchen Sie ein in eine Welt der Kreativität, in der Kunst auf Innovation "
"trifft, und lassen Sie sich von Galerien, Ausstellungen und einzigartigen "
"künstlerischen Ausdrucksformen inspirieren."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_shape_image
msgid "Dylan Doe"
msgstr "Dylan Doe"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_shape_image
msgid ""
"Dylan Doe is a 3D artist known for his vibrant and immersive digital "
"creations that captivate audiences worldwide."
msgstr ""
"Dylan Doe ist ein 3D-Künstler, der für seine lebendigen und fesselnden "
"digitalen Kreationen bekannt ist, die ein weltweites Publikum in ihren Bann "
"ziehen."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid ""
"Each piece is a one-of-a-kind creation, meticulously handcrafted to convey "
"emotion, thought, and imagination in every brushstroke or sculpted form."
msgstr ""
"Jedes Werk ist einzigartig, das in sorgfältiger Handarbeit gefertigt wurde, "
"um mit jedem Pinselstrich oder jeder geformten Form Emotionen, Gedanken und "
"Fantasie zu vermitteln."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Eco-friendly options for a sustainable future"
msgstr "Umweltfreundliche Optionen für eine nachhaltige Zukunft"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Elevate your home with vibrant, rich colors"
msgstr "Verschönern Sie Ihr Zuhause mit leuchtenden, satten Farben"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cta_box
msgid ""
"Elevate your space with the unique wonders of artistic "
"craftsmanship.<br/><br/>"
msgstr ""
"Verschönern Sie Ihr Zuhause mit den einzigartigen Wundern der "
"Handwerkskunst.<br/><br/>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid "Elevating your creative vision"
msgstr "Ihre kreative Vision im Vordergrund"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cover
msgid "Emotions <br/>through the colors"
msgstr "Emotionen <br/>durch Farben"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_banner
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_framed_intro
msgid "Emotions through the colors"
msgstr "Emotionen durch Farben"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid ""
"Enhance your talent with our exclusive guidance, showcasing the best version"
" of your artistic expression, every day."
msgstr ""
"Verbessern Sie Ihr Talent mit unserer exklusiven Anleitung, um jeden Tag die"
" beste Version Ihres künstlerischen Ausdrucks zu zeigen."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid "Exclusive Art Prints"
msgstr "Exklusive Kunstwerke"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid "Exclusive Artworks"
msgstr "Exklusive Kunstwerke"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid "Expertise and Inspiration"
msgstr "Expertise und Inspiration"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_center_top
msgid "Explore Now"
msgstr "Jetzt entdecken"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Explore Our Premium Paint Collection"
msgstr "Entdecken Sie unsere Premium-Farbkollektion"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_card_offset
msgid ""
"Explore a curated selection of artwork from emerging and established "
"artists. Our galleries are designed to ignite creativity and connect you "
"with timeless pieces."
msgstr ""
"Entdecken Sie eine Auswahl an Kunstwerken von aufstrebenden und etablierten "
"Künstlern. Unsere Galerien sollen Ihre Kreativität anregen und Sie mit "
"zeitlosen Werken in Kontakt bringen."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid ""
"Explore more and find unique projects that align with your vision, crafted "
"for artistic growth and recognition."
msgstr ""
"Entdecken Sie mehr und finden Sie einzigartige Projekte, die Ihrer Vision "
"entsprechen und für künstlerisches Wachstum und Anerkennung geschaffen sind."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid ""
"Explore one-of-a-kind pieces and limited editions that showcase exceptional "
"creativity and offer a distinctive touch to your collection."
msgstr ""
"Entdecken Sie Unikate und limitierte Auflagen, die von außergewöhnlicher "
"Kreativität zeugen und Ihrer Sammlung eine besondere Note verleihen."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_freegrid
msgid ""
"Explore our carefully curated selection of artworks that push the boundaries"
" of creativity and innovation. Our galleries showcase the finest pieces that"
" inspire and captivate."
msgstr ""
"Entdecken Sie unsere sorgfältig zusammengestellte Auswahl an Kunstwerken, "
"die die Grenzen von Kreativität und Innovation erweitern. In unseren "
"Galerien finden Sie die besten Stücke, die inspirieren und fesseln."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"Explore our curated selection of artistic masterpieces. Each piece is "
"thoughtfully crafted to inspire and captivate."
msgstr ""
"Entdecken Sie unsere sorgfältig zusammengestellte Auswahl an künstlerischen "
"Meisterwerken. Jedes Stück ist sorgfältig gestaltet, um zu inspirieren und "
"zu fesseln."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_top
msgid "Exploring Art's Masterpieces: A Journey to the Best Creations"
msgstr ""
"Die Meisterwerke der Kunst erforschen: Eine Reise zu den besten Kreationen"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid ""
"Formerly a corporate accountant, I started a professional transition 10 "
"years ago."
msgstr ""
"Ich war früher Buchhalterin in einem Unternehmen und habe mich vor 10 Jahren"
" beruflich verändert."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_empowerment
msgid "Grab your tickets   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"
msgstr "Tickets kaufen   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid ""
"Here is a selection of my works, exhibited in my studio and in galleries "
"around the world."
msgstr ""
"Hier ist eine Auswahl meiner Arbeiten, die ich in meinem Atelier und in "
"Galerien rund um die Welt ausstelle."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid ""
"I offer bespoke art commissions, collaborating closely with clients to "
"create custom pieces that resonate deeply with their personal stories and "
"spaces."
msgstr ""
"Ich biete individuelle Auftragsarbeiten an, bei denen ich eng mit meinen "
"Kunden zusammenarbeite, um individuelle Werke zu schaffen, die einen starken"
" Bezug zu ihren persönlichen Geschichten und Räumen haben."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "Immemorabili"
msgstr "Immemorabili"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quadrant
msgid ""
"Immerse yourself in the world of art. Our galleries showcase a diverse range"
" of creativity, from paintings to photography.<br/><br/> Discover the works "
"that speak to your soul and elevate your space."
msgstr ""
"Tauchen Sie ein in die Welt der Kunst. Unsere Galerien zeigen ein breites "
"Spektrum an Kreativität, von der Malerei bis zur Fotografie.<br/><br/> "
"Entdecken Sie die Werke, die Ihre Seele ansprechen und Ihren Wohnraum "
"verschönern."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Innovative paints for long-lasting beauty"
msgstr "Innovative Gemälde für dauerhafte Schönheit"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_title
msgid "It's all about perception"
msgstr "Alles eine Frage der Wahrnehmung"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_sidegrid
msgid "Join the artistic<br/>revolution"
msgstr "Eine künstlerische<br/>Revolution"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_text_cover
msgid "Learn more"
msgstr "Mehr erfahren"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_card_offset
msgid "Let art be the language of your space."
msgstr "Die Kunst kann die Sprache Ihres Zuhauses sein."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_text_image
msgid "Let yourself <br/>be transported"
msgstr "Werden Sie <br/>verführen"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "London"
msgstr "London"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_empowerment
msgid "Lunar Echoes:<br/>Around the World tour"
msgstr "Lunar Echoes:<br/>Welttournee"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid "Mastery and Quality"
msgstr "Meisterwerk und Qualität"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Midnight Dreamscape"
msgstr "Midnight Dreamscape"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "More details"
msgstr "Mehr Details"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid ""
"My art is a reflection of a distinctive vision, blending contemporary "
"techniques with personal storytelling to create pieces that captivate and "
"inspire."
msgstr ""
"Meine Kunst spiegelt eine unverwechselbare Vision wider, die zeitgenössische"
" Techniken mit persönlichen Erzählungen verbindet, um Werke zu schaffen, die"
" fesseln und inspirieren."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "My artistic process"
msgstr "Mein künstlerischer Weg"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "My work"
msgstr "Meine Arbeit"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid ""
"My work is known for its vibrant colors and dynamic textures, pushing the "
"boundaries of conventional art forms to evoke a strong emotional response."
msgstr ""
"Meine Arbeiten sind bekannt für ihre leuchtenden Farben und dynamischen "
"Texturen, mit denen ich die Grenzen konventioneller Kunstformen überschreite"
" und eine starke emotionale Reaktion hervorrufe."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "New York"
msgstr "New York"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid "Original and Handcrafted Works"
msgstr "Einzigartige und handgefertigte Werke"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Our Gallery"
msgstr "Unsere Galerie"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Paris"
msgstr "Paris"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Perfect shades for every surface and style"
msgstr "Perfekte Schatten für jede Oberfläche und jeden Stil"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid "Personalized Art Commissions"
msgstr "Personalisierte Auftragswerke"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid ""
"Receive ongoing guidance and assistance throughout your project, ensuring "
"that your artistic needs and preferences are fully realized."
msgstr ""
"Sie erhalten während des gesamten Projekts kontinuierliche Beratung und "
"Unterstützung, um sicherzustellen, dass Ihre künstlerischen Bedürfnisse und "
"Vorlieben vollständig umgesetzt werden."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid ""
"Refresh your portfolio with our expert advice and unique opportunities."
msgstr ""
"Aktualisieren Sie Ihr Portfolio mit unserer fachkundigen Beratung und "
"unseren einzigartigen Möglichkeiten."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Renaissance Revival"
msgstr "Renaissance Revival"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid ""
"Restore your cherished art pieces to their former glory with expert "
"cleaning, repair, and preservation services."
msgstr ""
"Lassen Sie Ihre wertvollen Kunstwerke durch fachmännische Reinigung, "
"Reparatur und Konservierung in neuem Glanz erstrahlen."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Royal Museums of Fine Arts of Belgium"
msgstr "Königliches Museum der schönen Künste in Belgien"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_text_cover
msgid "Share Art.<br/>Effortlessly."
msgstr "Kunst verbreiten. <br/>Mühelos."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Sunset Serenade"
msgstr "Sunset Serenade"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid "Sustainable Art"
msgstr "Nachhaltige Kunst"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "The Garden of Serenity"
msgstr "The Garden of Serenity"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "The Museum of Modern Art (MoMA)"
msgstr "The Museum of Modern Art (MoMA)"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid ""
"The choice of materials, colours are not insignificant, discover more about "
"my artistic process."
msgstr ""
"Die Wahl der Materialien und Farben ist nicht unwichtig, entdecken Sie mehr "
"über meinen künstlerischen Prozess."

#. module: theme_artists
#: model:ir.model,name:theme_artists.model_theme_utils
msgid "Theme Utils"
msgstr "Designwerkzeuge"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"This modern piece captures the energy of city life, using sharp lines and "
"reflective surfaces to portray an urban landscape."
msgstr ""
"Dieses moderne Werk präsentiert die Energie des Stadtlebens und verwendet "
"scharfe Linien und reflektierende Oberflächen, um eine städtische Landschaft"
" darzustellen."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_empowerment
msgid "Tour agenda"
msgstr "Tourprogramm"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_image_title
msgid ""
"Transform your surroundings with our latest art collection, where "
"imagination meets technique. Elevate your space with pieces that blend "
"artistic expression and aesthetic beauty effortlessly."
msgstr ""
"Verändern Sie Ihre Umgebung mit unserer neuen Kunstsammlung, in der "
"Vorstellungskraft auf Technik trifft. Werten Sie Ihren Raum mit Werken auf, "
"die künstlerichen Ausdruck und ästhetische Schönheit nahtlos miteinander "
"verbinden."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid "Unique Creative Vision"
msgstr "Einzigartige kreative Visio"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_center_top
msgid "Unleash Your Creative"
msgstr "Ihre Kreativität entfachen"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_images_mosaic
msgid "Unveiling Our Latest Artistic Creations"
msgstr "Enthüllung unserer neuesten Kunstwerke"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Upcoming Exhibitions"
msgstr "Zukünftige Ausstellungen"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Urban Reflection"
msgstr "Urban Reflection"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "Vincam"
msgstr "Vincam"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quadrant
msgid "Visit Galleries"
msgstr "Galerien besuchen"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_freegrid
msgid "Visit the Gallery"
msgstr "Die Galerie besuchen"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_call_to_action
msgid "Want to discover more?"
msgstr "Sie möchten mehr entdecken?"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid ""
"We craft personalized art to express your unique vision. Our team works "
"closely with you to bring your ideas to life from concept to completion."
msgstr ""
"Wir schaffen personalisierte Kunst, die Ihre einzigartige Vision zum "
"Ausdruck bringt. Unser Team arbeitet eng mit Ihnen zusammen, um Ihre Ideen "
"vom Konzept bis zur Fertigstellung zu verwirklichen."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid ""
"We craft personalized artworks that capture your vision and style, "
"delivering unique pieces that reflect your individuality."
msgstr ""
"Wir stellen personalisierte Kunstwerke her, die Ihre Vision und Ihren Stil "
"einfangen, und liefern einzigartige Stücke, die Ihre Individualität "
"widerspiegeln."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid ""
"We provide innovative art solutions for today’s challenges. Using cutting-"
"edge methods, we help you achieve your creative goals."
msgstr ""
"Wir bieten innovative Kunstlösungen für die Herausforderungen von heute. Mit"
" modernsten Methoden helfen wir Ihnen, Ihre kreativen Ziele zu erreichen."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_text_image
msgid ""
"Welcome to my online home. I invite you to come in, learn more about me and "
"my work, and stop to embrace the slow pleasure of art and beauty."
msgstr ""
"Willkommen in meinem Online-Haus. Ich lade Sie ein, mehr über mich und meine"
" Arbeit zu erfahren und innezuhalten, um sich dem langsamen Genuss von Kunst"
" und Schönheit hinzugeben."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid "What we create for our customers"
msgstr "Was wir für unsere Kunden fertigen"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_freegrid
msgid "Where Creativity Meets Innovation in Art"
msgstr "Wo Kreativität auf Innovation in der Kunst trifft"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid ""
"With vast experience and artistic insight, we offer techniques and concepts "
"that keep you ahead in the art world."
msgstr ""
"Mit unserer langjährigen Erfahrung und unserem künstlerischen Verständnis "
"bieten wir Ihnen Techniken und Konzepte, die Sie in der Kunstwelt "
"weiterbringen."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid "Your art, curated for success"
msgstr "Ihre Kunst, für den Erfolg zusammengestellt"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid ""
"Your satisfaction is our passion. Our support team is always available, "
"ensuring your artistic journey is seamless and fulfilling."
msgstr ""
"Ihre Zufriedenheit ist unsere Leidenschaft. Unser Supportteam steht Ihnen "
"jederzeit zur Verfügung und sorgt dafür, dass Ihre künstlerische Reise "
"nahtlos und erfüllend verläuft."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_big_number
msgid "songs released"
msgstr "veröffentlichte Songs"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "✽  Classic Art"
msgstr "✽  Klassische Kunst"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "✽  Modern Art"
msgstr "✽  Moderne Kunst"
