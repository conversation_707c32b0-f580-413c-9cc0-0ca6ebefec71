# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_artists
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-07 13:30+0000\n"
"PO-Revision-Date: 2024-09-25 18:03+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quotes_carousel_minimal
msgid ""
"\" A visionary partner for growth. <br/>Innovative, skilled, and always "
"pushing creative boundaries. \""
msgstr ""
"\" Un partenaire visionnaire qui favorise le développement. <br/>Innovant, "
"compétent et repoussant sans cesse les limites de la créativité. \""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quotes_carousel_minimal
msgid ""
"\" Outstanding artistry and service! <br/>They consistently exceed our "
"creative expectations. \""
msgstr ""
"\" Un travail artistique et un service exceptionnels ! <br/>Ils dépassent "
"continuellement nos attentes en matière de créativité. \""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quotes_carousel_minimal
msgid "\" Their creativity transformed our vision. Unique and truly inspiring. \""
msgstr ""
"\" Leur créativité a transformé notre vision. Unique et vraiment inspirant. "
"\""

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,200.00"
msgstr "1 200 €"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,300.00"
msgstr "1 300 €"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,350.00"
msgstr "1 350 €"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,450.00"
msgstr "1 450 €"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,500.00"
msgstr "1 500 $"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "$1,600.00"
msgstr "1 600 €"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "02.08.2025"
msgstr "02.08.2025"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "03.01.2026"
msgstr "03.01.2026"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "09.09.2025"
msgstr "09.09.2025"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "12.11.2025"
msgstr "12.11.2025"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "12.12.2025"
msgstr "12.12.2025"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "<b>Painting concept / April 2021</b>"
msgstr "<b>Concept de peinture / Avril 2021</b>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "<b>Photoshoot / June 2021</b>"
msgstr "<b>Séance photo / Juin 2021</b>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "<b>Typographic collection / March 2021</b>"
msgstr "<b>Collection typographique / Mars 2021</b>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_sidegrid
msgid ""
"<br/>Art is a journey of expression, where every stroke and color tells a "
"story waiting to be unveiled.<br/><br/>"
msgstr ""
"<br/>L'art est un voyage expressif, où chaque trait et chaque couleur "
"racontent une histoire qui attend d'être dévoilée.<br/><br/>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_text_cover
msgid ""
"<br/>Showcase your artwork and updates online with a user-friendly platform "
"that simplifies everything, making it easy for art lovers to discover and "
"appreciate your creations.<br/>"
msgstr ""
"<br/>Présentez vos œuvres d'art et vos nouveautés en ligne grâce à une "
"plateforme simple d'utilisation qui facilite la découverte et l'appréciation"
" de vos créations par les amateurs d'art.<br/>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_empowerment
msgid "<br/>The very last date of our World tour !<br/><br/>"
msgstr "<br/>La toute dernière date de notre tournée mondiale !<br/><br/>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_big_number
msgid ""
"<font class=\"text-gradient\" style=\"background-image: linear-gradient(0deg, rgb(165, 78, 223) 8%, rgb(222, 222, 222) 80%);\">\n"
"            250+\n"
"        </font>"
msgstr ""
"<font class=\"text-gradient\" style=\"background-image: linear-gradient(0deg, rgb(165, 78, 223) 8%, rgb(222, 222, 222) 80%);\">\n"
"            Plus de 250\n"
"        </font>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_empowerment
msgid ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/>  Don't "
"miss the final show !"
msgstr ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/>  Ne ratez"
" pas le spectacle final !"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_image_title
msgid "A Deep Dive into Creativity and Excellence"
msgstr "Plongez au cœur de la créativité et de l'excellence"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"A classical piece inspired by Renaissance techniques, featuring intricate "
"details and a rich color palette."
msgstr ""
"Une œuvre classique inspirée de techniques de la Renaissance, avec des "
"détails complexes et une riche palette de couleurs."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"A tranquil garden scene that blends soft greens and pastel flowers, creating"
" a peaceful and calming atmosphere."
msgstr ""
"Une scène de nature paisible qui mêle des couleurs vertes douces et des "
"fleurs pastel, créant ainsi une atmosphère de calme et de sérénité."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"A vibrant abstract painting that blends bold colors and dynamic shapes, "
"creating a sense of harmony and movement."
msgstr ""
"Cette peinture abstraite et pleine de vie mêle des couleurs vives et des "
"formes dynamiques, pour créer un sentiment d'harmonie et de mouvement."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "About me"
msgstr "À propos de moi"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Abstract Harmony"
msgstr "Abstract Harmony"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Abstract Vision"
msgstr "Abstract Vision"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Amsterdam"
msgstr "Amsterdam"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"An imaginative dreamscape in deep blues and purples, this piece invites "
"viewers to explore a world of night-time fantasy."
msgstr ""
"Un paysage de rêve imaginaire dans des tons bleus et violets profonds. Cette"
" œuvre invite les spectateurs à explorer un monde de fantaisie nocturne."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "Aquitani"
msgstr "Aquitani"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid "Art Restoration &amp; Repair"
msgstr "Restauration et réparation d'œuvres d'art"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_center_top
msgid "Art that speaks to your soul"
msgstr "De l'art qui vous touche"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Artistic journey around the world"
msgstr "Un voyage artistique à travers le monde"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Berlin"
msgstr "Berlin"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Berlin Art Week"
msgstr "Berlin Art Week"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid "Bespoke Art Creations"
msgstr "Des créations artistiques sur mesure"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid "Bold Use of Color and Texture"
msgstr "Une audacieuse utilisation de couleurs et de textures"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid ""
"Bring your vision to life with personalized artwork created by talented "
"artists, tailored to your unique preferences."
msgstr ""
"Donnez vie à vos idées grâce à des œuvres d'art personnalisées, créées par "
"des artistes talentueux et adaptées à vos préférences uniques."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Brussels"
msgstr "Bruxelles"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"Capturing the serene beauty of a sunset, this painting uses warm tones and "
"soft brushstrokes to evoke a sense of peace."
msgstr ""
"Ce tableau, qui capture la beauté sereine d'un coucher de soleil, utilise "
"des tons chauds et des coups de pinceau doux pour évoquer un sentiment de "
"paix."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quadrant
msgid "Celebrating Artistic Expression"
msgstr "Nous célébrons l'expression artistique"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_top
msgid ""
"Color trends are already waiting to spring into action for the next summer."
msgstr ""
"Les tendances en matière de couleurs sont déjà prêtes pour l'été prochain."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Comput'Art"
msgstr "Comput'Art"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_shape_image
msgid "Contact Me"
msgstr "Me contacter"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "Contact me"
msgstr "Me contacter"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_call_to_action
msgid "Contact me for a visit of my studio."
msgstr "Contactez-moi pour visiter mon studio."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid "Custom Art Commissions"
msgstr "Des commandes d'œuvres d'art personnalisées"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid "Custom Creations"
msgstr "Des créations sur mesure"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid "Dedicated Artist Support"
msgstr "Une assistance dédiée aux artistes"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Digital Art Paris"
msgstr "Digital Art Paris"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cta_box
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "Discover"
msgstr "Découvrir"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_image_text
msgid "Discover <br/>my works"
msgstr "Découvrez <br/>mes œuvres"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_card_offset
msgid "Discover Art that Inspires Your Soul"
msgstr "Découvrez de l'art qui inspire l'âme"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid ""
"Discover limited-edition prints of stunning original artworks, perfect for "
"enhancing your home or office space."
msgstr ""
"Découvrez des tirages en édition limitée d'œuvres d'art originales "
"époustouflantes, parfaites pour sublimer votre maison ou votre bureau."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cta_box
msgid "Discover new creations<br/>and artistic masterpieces !"
msgstr ""
"Découvrez de nouvelles créations<br/>et des chefs-d'œuvre artistiques !"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_images_mosaic
msgid "Discover our new and inspiring artistic paint collections."
msgstr ""
"Découvrez nos nouvelles collections inspirantes, composées de peintures "
"artistiques."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Discover the Colors that Transform Spaces"
msgstr "Découvrez des couleurs qui transforment les espaces"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid "Discover your potential"
msgstr "Découvrez votre potentiel"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid "Distinctive Creations: Art That Speaks"
msgstr "Créations distinctives : L'art qui parle"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_center_top
msgid ""
"Dive into a world of creativity where art meets innovation, and find "
"inspiration through galleries, exhibitions, and unique artistic expressions."
msgstr ""
"Plongez dans un monde de créativité où l'art côtoie l'innovation, et trouvez"
" l'inspiration à travers des galeries, des expositions et des expressions "
"artistiques uniques."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_shape_image
msgid "Dylan Doe"
msgstr "Dylan Doe"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_shape_image
msgid ""
"Dylan Doe is a 3D artist known for his vibrant and immersive digital "
"creations that captivate audiences worldwide."
msgstr ""
"Dylan Doe est un artiste 3D connu pour ses créations digitales vibrantes et "
"immersives qui captivent le public du monde entier."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid ""
"Each piece is a one-of-a-kind creation, meticulously handcrafted to convey "
"emotion, thought, and imagination in every brushstroke or sculpted form."
msgstr ""
"Chaque pièce est une création unique, méticuleusement fabriquée à la main "
"pour exprimer une émotion, une pensée et de l'imagination dans chaque coup "
"de pinceau ou forme sculptée."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Eco-friendly options for a sustainable future"
msgstr "Des options écologiques pour un avenir durable"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Elevate your home with vibrant, rich colors"
msgstr "Donnez du cachet à votre maison avec des couleurs riches et vibrantes"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cta_box
msgid ""
"Elevate your space with the unique wonders of artistic "
"craftsmanship.<br/><br/>"
msgstr ""
"Mettez en valeur votre espace grâce aux merveilles uniques du savoir-faire "
"artistique.<br/><br/>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid "Elevating your creative vision"
msgstr "RenforceZ votre vision créative"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cover
msgid "Emotions <br/>through the colors"
msgstr "Les émotions <br/>à travers les couleurs"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_banner
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_framed_intro
msgid "Emotions through the colors"
msgstr "Les émotions à travers les couleurs"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid ""
"Enhance your talent with our exclusive guidance, showcasing the best version"
" of your artistic expression, every day."
msgstr ""
"Mettez votre talent en valeur grâce à nos conseils exclusifs, afin de faire "
"ressortir quotidiennement le meilleur de votre expression artistique."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid "Exclusive Art Prints"
msgstr "Des impressions artistiques exclusives"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid "Exclusive Artworks"
msgstr "Des œuvres d'art exclusives"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid "Expertise and Inspiration"
msgstr "Savoir-faire et inspiration"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_center_top
msgid "Explore Now"
msgstr "Explorez"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Explore Our Premium Paint Collection"
msgstr "Découvrez notre collection des meilleures peintures"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_card_offset
msgid ""
"Explore a curated selection of artwork from emerging and established "
"artists. Our galleries are designed to ignite creativity and connect you "
"with timeless pieces."
msgstr ""
"Explorez une sélection d'œuvres d'art réalisées par des artistes prometteurs"
" ou confirmés. Nos galeries sont conçues pour stimuler la créativité et vous"
" faire découvrir des œuvres intemporelles."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid ""
"Explore more and find unique projects that align with your vision, crafted "
"for artistic growth and recognition."
msgstr ""
"Explorez davantage et trouvez des projets uniques qui correspondent à votre "
"vision, conçus pour l'épanouissement artistique et la reconnaissance."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid ""
"Explore one-of-a-kind pieces and limited editions that showcase exceptional "
"creativity and offer a distinctive touch to your collection."
msgstr ""
"Découvrez des pièces uniques et des éditions limitées qui témoignent d'une "
"créativité exceptionnelle et apportent une touche distinctive à votre "
"collection."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_freegrid
msgid ""
"Explore our carefully curated selection of artworks that push the boundaries"
" of creativity and innovation. Our galleries showcase the finest pieces that"
" inspire and captivate."
msgstr ""
"Explorez notre sélection d'œuvres d'art soigneusement sélectionnées qui "
"repoussent les limites de la créativité et de l'innovation. Nos galeries "
"présentent les plus belles pièces inspirantes et captivantes."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"Explore our curated selection of artistic masterpieces. Each piece is "
"thoughtfully crafted to inspire and captivate."
msgstr ""
"Explorez notre sélection de chefs-d'œuvre artistiques. Chaque pièce est "
"soigneusement conçue pour inspirer et captiver."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_top
msgid "Exploring Art's Masterpieces: A Journey to the Best Creations"
msgstr ""
"EPartez à la découverte des chefs-d'œuvre artistiques : Un voyage vers les "
"meilleures créations"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid ""
"Formerly a corporate accountant, I started a professional transition 10 "
"years ago."
msgstr ""
"Ancienne comptable d'entreprise, j'ai entamé une reconversion "
"professionnelle il y a 10 ans."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_empowerment
msgid "Grab your tickets   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"
msgstr "Prenez vos billets   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid ""
"Here is a selection of my works, exhibited in my studio and in galleries "
"around the world."
msgstr ""
"Voici une sélection de mes œuvres, exposées dans mon atelier et dans des "
"galeries du monde entier."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid ""
"I offer bespoke art commissions, collaborating closely with clients to "
"create custom pieces that resonate deeply with their personal stories and "
"spaces."
msgstr ""
"Je propose des commandes d'art sur mesure, en collaborant étroitement avec "
"les clients pour créer des pièces personnalisées qui reflètent profondément "
"leurs histoires et leurs espaces personnels."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "Immemorabili"
msgstr "Immemorabili"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quadrant
msgid ""
"Immerse yourself in the world of art. Our galleries showcase a diverse range"
" of creativity, from paintings to photography.<br/><br/> Discover the works "
"that speak to your soul and elevate your space."
msgstr ""
"Plongez dans le monde de l'art. Nos galeries présentent une grande diversité"
" de créations, de la peinture à la photographie.<br/><br/> Découvrez des "
"œuvres qui parlent à votre âme et embellissent votre espace."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Innovative paints for long-lasting beauty"
msgstr "Des peintures innovantes pour une beauté de longue durée"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_title
msgid "It's all about perception"
msgstr "Tout est question de perception."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_sidegrid
msgid "Join the artistic<br/>revolution"
msgstr "Participez à la révolution<br/>artistique"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_text_cover
msgid "Learn more"
msgstr "En savoir plus"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_card_offset
msgid "Let art be the language of your space."
msgstr "Laissez l'art être le langage de votre espace."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_text_image
msgid "Let yourself <br/>be transported"
msgstr "Laissez-vous <br/>transporter"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "London"
msgstr "Londres"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_empowerment
msgid "Lunar Echoes:<br/>Around the World tour"
msgstr "Lunar Echoes:<br/>Around the World tour"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid "Mastery and Quality"
msgstr "Maîtrise et qualité"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Midnight Dreamscape"
msgstr "Midnight Dreamscape"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "More details"
msgstr "Plus de détails"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid ""
"My art is a reflection of a distinctive vision, blending contemporary "
"techniques with personal storytelling to create pieces that captivate and "
"inspire."
msgstr ""
"Mon art est le reflet d'une vision distinctive, mêlant des techniques "
"contemporaines à des récits personnels pour créer des œuvres captivantes et "
"inspirantes."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "My artistic process"
msgstr "Mon processus artistique"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid "My work"
msgstr "Mes œuvres "

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid ""
"My work is known for its vibrant colors and dynamic textures, pushing the "
"boundaries of conventional art forms to evoke a strong emotional response."
msgstr ""
"Mon travail est connu pour ses couleurs vibrantes et ses textures "
"dynamiques, repoussant les limites des formes d'art conventionnelles pour "
"susciter une forte réponse émotionnelle."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "New York"
msgstr "New York"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid "Original and Handcrafted Works"
msgstr "Des œuvres originales et artisanales"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Our Gallery"
msgstr "Notre galerie"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Paris"
msgstr "Paris"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_images
msgid "Perfect shades for every surface and style"
msgstr "Des nuances parfaites pour chaque surface et chaque style"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid "Personalized Art Commissions"
msgstr "Des commandes d'œuvres d'art personnalisées"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid ""
"Receive ongoing guidance and assistance throughout your project, ensuring "
"that your artistic needs and preferences are fully realized."
msgstr ""
"Bénéficiez de conseils et d'une assistance tout au long de votre projet, "
"afin que vos besoins et préférences artistiques soient pleinement pris en "
"compte."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid ""
"Refresh your portfolio with our expert advice and unique opportunities."
msgstr ""
"Renouvelez votre portfolio grâce à nos conseils d'experts et à des "
"opportunités uniques."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Renaissance Revival"
msgstr "Renaissance Revival"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_features_wall
msgid ""
"Restore your cherished art pieces to their former glory with expert "
"cleaning, repair, and preservation services."
msgstr ""
"Restaurez vos précieuses œuvres d'art à leur splendeur d'antan grâce à des "
"services spécialisés de nettoyage, de réparation et de préservation."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Royal Museums of Fine Arts of Belgium"
msgstr "Musées Royaux des Beaux-Arts de Belgique"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_text_cover
msgid "Share Art.<br/>Effortlessly."
msgstr "Partagez l'art.<br/>En toute simplicité."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Sunset Serenade"
msgstr "Sunset Serenade"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid "Sustainable Art"
msgstr "De l'art durable"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "The Garden of Serenity"
msgstr "The Garden of Serenity"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "The Museum of Modern Art (MoMA)"
msgstr "The Museum of Modern Art (MoMA)"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_three_columns
msgid ""
"The choice of materials, colours are not insignificant, discover more about "
"my artistic process."
msgstr ""
"Le choix des matériaux et des couleurs n'est pas anodin. Découvrez-en plus "
"sur mon processus artistique."

#. module: theme_artists
#: model:ir.model,name:theme_artists.model_theme_utils
msgid "Theme Utils"
msgstr "Thèmes utiles"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid ""
"This modern piece captures the energy of city life, using sharp lines and "
"reflective surfaces to portray an urban landscape."
msgstr ""
"Cette pièce moderne capture l'énergie de la vie urbaine, en utilisant des "
"lignes précises et des surfaces réfléchissantes pour dépeindre un paysage "
"urbain."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_empowerment
msgid "Tour agenda"
msgstr "Agenda de la tournée"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_image_title
msgid ""
"Transform your surroundings with our latest art collection, where "
"imagination meets technique. Elevate your space with pieces that blend "
"artistic expression and aesthetic beauty effortlessly."
msgstr ""
"Transformez votre espace avec notre dernière collection d'art, où "
"l'imagination rencontre la technique. Rehaussez votre intérieur avec des "
"pièces qui allient harmonieusement l'expression artistique et la beauté "
"esthétique."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_cards_grid
msgid "Unique Creative Vision"
msgstr "Une vision créative unique"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_striped_center_top
msgid "Unleash Your Creative"
msgstr "Libérez votre créativité"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_images_mosaic
msgid "Unveiling Our Latest Artistic Creations"
msgstr "Découvrez nos dernières créations artistiques"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_product_catalog
msgid "Upcoming Exhibitions"
msgstr "Prochaines expositions"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "Urban Reflection"
msgstr "Urban Reflection"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel
msgid "Vincam"
msgstr "Vincam"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_quadrant
msgid "Visit Galleries"
msgstr "Visiter les galeries"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_freegrid
msgid "Visit the Gallery"
msgstr "Visiter la galerie"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_call_to_action
msgid "Want to discover more?"
msgstr "Envie de découvrir davantage ?"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid ""
"We craft personalized art to express your unique vision. Our team works "
"closely with you to bring your ideas to life from concept to completion."
msgstr ""
"La création d'œuvres d'art personnalisées permet d'exprimer votre vision "
"unique. Notre équipe travaille en étroite collaboration avec vous pour "
"donner vie à vos idées, de la conception à la réalisation."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_key_benefits
msgid ""
"We craft personalized artworks that capture your vision and style, "
"delivering unique pieces that reflect your individuality."
msgstr ""
"Nous créons des œuvres d'art personnalisées qui reflètent votre vision et "
"votre style. Nous livrons des pièces uniques qui reflètent votre "
"individualité."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid ""
"We provide innovative art solutions for today’s challenges. Using cutting-"
"edge methods, we help you achieve your creative goals."
msgstr ""
"Nous proposons des solutions artistiques innovantes pour relever les défis "
"actuels. Grâce à des méthodes innovantes, nous vous aidons à atteindre vos "
"objectifs créatifs."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_text_image
msgid ""
"Welcome to my online home. I invite you to come in, learn more about me and "
"my work, and stop to embrace the slow pleasure of art and beauty."
msgstr ""
"Bienvenue dans ma maison en ligne. Je vous invite à entrer, à en apprendre "
"plus sur moi et sur mes œuvres et à savourer le lent plaisir de l'art et de "
"la beauté."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid "What we create for our customers"
msgstr "Ce que nous créons pour nos clients"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_freegrid
msgid "Where Creativity Meets Innovation in Art"
msgstr "Quand la créativité rencontre l'innovation dans l'art"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid ""
"With vast experience and artistic insight, we offer techniques and concepts "
"that keep you ahead in the art world."
msgstr ""
"Forts d'une vaste expérience et d'une vision artistique, nous proposons des "
"techniques et des concepts qui vous permettent de rester à jour dans le "
"monde de l'art."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_carousel_intro
msgid "Your art, curated for success"
msgstr "Votre art, conçu pour le succès"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_wavy_grid
msgid ""
"Your satisfaction is our passion. Our support team is always available, "
"ensuring your artistic journey is seamless and fulfilling."
msgstr ""
"Votre satisfaction est notre passion. Notre équipe d'assistance est toujours"
" disponible et veille à ce que votre parcours artistique se déroule sans "
"encombre et de manière optimale."

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_big_number
msgid "songs released"
msgstr "chansons sorties"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "✽  Classic Art"
msgstr "✽  Art classique"

#. module: theme_artists
#: model_terms:theme.ir.ui.view,arch:theme_artists.s_pricelist_boxed
msgid "✽  Modern Art"
msgstr "✽  Art moderne"
