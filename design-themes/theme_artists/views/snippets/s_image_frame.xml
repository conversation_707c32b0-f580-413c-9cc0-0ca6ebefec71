<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="s_image_frame" inherit_id="website.s_image_frame">
    <!-- Section -->
    <xpath expr="//section" position="attributes">
        <attribute name="class" remove="o_cc1" add="o_cc5" separator=" "/>
    </xpath>
    <!-- Grid Item -->
    <xpath expr="//div[hasclass('o_grid_item')]" position="attributes">
        <attribute name="class" remove="rounded" separator=" "/>
        <attribute name="style" remove="border-radius: 6.4px !important;" separator=";"/>
    </xpath>
    <!-- Grid Item 2 -->
    <xpath expr="//div[hasclass('o_grid_item')][2]" position="attributes">
        <attribute name="class" remove="rounded" separator=" "/>
        <attribute name="style" add="--grid-item-padding-y: 32px !important;" separator=";"/>
    </xpath>
    <!-- Image -->
    <xpath expr="//img" position="attributes">
        <attribute name="style" remove="padding: 16px;" separator=";"/>
    </xpath>
</template>

<template id="configurator_s_image_frame" inherit_id="website.configurator_s_image_frame">
    <xpath expr="//section" position="attributes">
        <attribute name="class" remove="pt64 pb64" add="pt40 pb0" separator=" "/>
    </xpath>
</template>

</odoo>
