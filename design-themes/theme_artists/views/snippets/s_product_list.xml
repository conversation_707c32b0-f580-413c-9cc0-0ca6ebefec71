<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="s_product_list" inherit_id="website.s_product_list">
    <!-- Section -->
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="o_cc o_cc5" separator=" "/>
    </xpath>
    <!-- Cards -->
    <xpath expr="//div[hasclass('col-lg-2')]//div[hasclass('card')]" position="attributes">
        <attribute name="class" remove="o_cc o_cc1" add="o_cc o_cc5" separator=" "/>
    </xpath>
    <xpath expr="//div[hasclass('col-lg-2')][2]//div[hasclass('card')]" position="attributes">
        <attribute name="class" remove="o_cc o_cc1" add="o_cc o_cc5" separator=" "/>
    </xpath>
    <xpath expr="//div[hasclass('col-lg-2')][3]//div[hasclass('card')]" position="attributes">
        <attribute name="class" remove="o_cc o_cc1" add="o_cc o_cc5" separator=" "/>
    </xpath>
    <xpath expr="//div[hasclass('col-lg-2')][4]//div[hasclass('card')]" position="attributes">
        <attribute name="class" remove="o_cc o_cc1" add="o_cc o_cc5" separator=" "/>
    </xpath>
    <xpath expr="//div[hasclass('col-lg-2')][5]//div[hasclass('card')]" position="attributes">
        <attribute name="class" remove="o_cc o_cc1" add="o_cc o_cc5" separator=" "/>
    </xpath>
    <xpath expr="//div[hasclass('col-lg-2')][6]//div[hasclass('card')]" position="attributes">
        <attribute name="class" remove="o_cc o_cc1" add="o_cc o_cc5" separator=" "/>
    </xpath>
</template>

</odoo>
