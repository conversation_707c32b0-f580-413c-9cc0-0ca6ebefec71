<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="s_images_wall" inherit_id="website.s_images_wall">
    <!-- Section -->
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="o_cc o_cc5 pb56 pt16" remove="pt24 pb24" separator=" "/>
    </xpath>
    <!-- Images -->
    <xpath expr="//img" position="attributes">
        <attribute name="class" remove="rounded" separator=" "/>
    </xpath>
    <xpath expr="(//img)[2]" position="attributes">
        <attribute name="class" remove="rounded" separator=" "/>
    </xpath>
    <xpath expr="(//img)[3]" position="attributes">
        <attribute name="class" remove="rounded" separator=" "/>
    </xpath>
    <xpath expr="(//img)[4]" position="attributes">
        <attribute name="class" remove="rounded" separator=" "/>
    </xpath>
    <xpath expr="(//img)[5]" position="attributes">
        <attribute name="class" remove="rounded" separator=" "/>
    </xpath>
    <xpath expr="(//img)[6]" position="attributes">
        <attribute name="class" remove="rounded" separator=" "/>
    </xpath>
</template>

<template id="s_image_gallery" inherit_id="website.s_image_gallery">
    <!-- Section -->
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="o_cc o_cc5 o_full_screen_height" separator=" "/>
    </xpath>
    <!-- Container -->
    <xpath expr="//div[hasclass('carousel')]" position="attributes">
        <attribute name="class" remove="carousel-dark" separator=" "/>
    </xpath>
</template>

</odoo>
