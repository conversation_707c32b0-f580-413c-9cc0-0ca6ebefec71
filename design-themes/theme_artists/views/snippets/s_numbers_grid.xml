<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="s_numbers_grid" inherit_id="website.s_numbers_grid">
    <!-- Section -->
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="o_cc5" remove="o_cc2" separator=" "/>
    </xpath>
    <!-- Cells -->
    <xpath expr="//div[hasclass('col-lg-3')]" position="attributes">
        <attribute name="class" add="o_cc4" remove="o_cc1" separator=" "/>
    </xpath>
    <xpath expr="(//div[hasclass('col-lg-3')])[2]" position="attributes">
        <attribute name="class" add="o_cc4" remove="o_cc1" separator=" "/>
    </xpath>
    <xpath expr="(//div[hasclass('col-lg-3')])[3]" position="attributes">
        <attribute name="class" add="o_cc4" remove="o_cc1" separator=" "/>
    </xpath>
    <xpath expr="(//div[hasclass('col-lg-3')])[4]" position="attributes">
        <attribute name="class" add="o_cc4" remove="o_cc1" separator=" "/>
    </xpath>
    <xpath expr="(//div[hasclass('col-lg-3')])[5]" position="attributes">
        <attribute name="class" add="o_cc4" remove="o_cc1" separator=" "/>
    </xpath>
    <xpath expr="(//div[hasclass('col-lg-3')])[6]" position="attributes">
        <attribute name="class" add="o_cc4" remove="o_cc1" separator=" "/>
    </xpath>
    <xpath expr="(//div[hasclass('col-lg-3')])[7]" position="attributes">
        <attribute name="class" add="o_cc4" remove="o_cc1" separator=" "/>
    </xpath>
    <xpath expr="(//div[hasclass('col-lg-3')])[8]" position="attributes">
        <attribute name="class" add="o_cc4" remove="o_cc1" separator=" "/>
    </xpath>
</template>

</odoo>
