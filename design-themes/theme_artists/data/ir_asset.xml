<?xml version="1.0" encoding="utf-8"?>
<odoo>

<record id="theme_artists.s_banner_parallax_000_variables_scss" model="theme.ir.asset">
    <field name="key">theme_artists.s_banner_parallax_000_variables_scss</field>
    <field name="name">Banner parallax 000 variables SCSS</field>
    <field name="bundle">web._assets_primary_variables</field>
    <field name="path">theme_artists/static/src/old_snippets/s_banner_parallax/000_variables.scss</field>
    <field name="active" eval="False"/>
</record>

<record id="theme_artists.s_products_carousel_000_variables_scss" model="theme.ir.asset">
    <field name="key">theme_artists.s_products_carousel_000_variables_scss</field>
    <field name="name">Products carousel 000 variables SCSS</field>
    <field name="bundle">web._assets_primary_variables</field>
    <field name="path">theme_artists/static/src/old_snippets/s_products_carousel/000_variables.scss</field>
    <field name="active" eval="False"/>
</record>

<record id="theme_artists.primary_variables_scss" model="theme.ir.asset">
    <field name="key">theme_artists.primary_variables_scss</field>
    <field name="name">Primary variables SCSS</field>
    <field name="bundle">web._assets_primary_variables</field>
    <field name="path">theme_artists/static/src/scss/primary_variables.scss</field>
</record>

<record id="theme_artists.compatibility_saas_11_4_variables_scss" model="theme.ir.asset">
    <field name="key">theme_artists.compatibility_saas_11_4_variables_scss</field>
    <field name="name">Compatibility saas 11 4 variables SCSS</field>
    <field name="bundle">web._assets_primary_variables</field>
    <field name="path">/theme_artists/static/src/scss/compatibility-saas-11.4-variables.scss</field>
    <field name="active" eval="False"/>
</record>

</odoo>
