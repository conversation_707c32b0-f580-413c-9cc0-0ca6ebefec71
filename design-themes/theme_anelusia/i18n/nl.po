# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_anelusia
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-07 13:29+0000\n"
"PO-Revision-Date: 2024-09-25 18:03+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_quotes_carousel_minimal
msgid ""
"\" A go-to partner for stylish growth. <br/>Creative, timely, and ahead of "
"the fashion game. \""
msgstr ""
"\" Een partner voor stijlvolle groei. <br/>Creatief, op tijd en vooruit op "
"de modewereld. \""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_quotes_carousel_minimal
msgid ""
"\" Exceptional quality and service! <br/>They consistently deliver on all "
"our fashion needs. \""
msgstr ""
"\" Uitzonderlijke kwaliteit en service! <br/>Ze leveren consequent aan al "
"onze modebehoeften. \""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_quotes_carousel_minimal
msgid ""
"\" Their designs revamped our image. Fashion-forward and always on trend. \""
msgstr ""
"\" Hun ontwerpen vernieuwden ons imago. Fashion-forward en altijd on trend. "
"\""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "$120.00"
msgstr "$ 120,00"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "$130.00"
msgstr "$130.00"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "$135.00"
msgstr "$135.00"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "$145.00"
msgstr "$145.00"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "$150.00"
msgstr "$150.00"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "$160.00"
msgstr "$160.00"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_text_cover
msgid ""
"<br/>Color trends are already waiting to spring into action for the  next "
"summer.<br/>"
msgstr ""
"<br/>De kleurentrends staan al klaar om in actie te komen voor de volgende "
"zomer.<br/>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_empowerment
msgid "<br/>Unveil the latest, shop the best!<br/><br/>"
msgstr "<br/>Onthul het nieuwste, shop het beste!<br/><br/>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_big_number
msgid ""
"<font class=\"text-gradient\" style=\"background-image: linear-gradient(0deg, rgb(29, 32, 48) 25%, rgb(222, 222, 222) 80%);\">\n"
"            100+\n"
"        </font>"
msgstr ""
"<font class=\"text-gradient\" style=\"background-image: linear-gradient(0deg, rgb(29, 32, 48) 25%, rgb(222, 222, 222) 80%);\">\n"
"            100+\n"
"        </font>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_text_cover
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%,"
" var(--o-color-2) 100%);\" class=\"text-gradient\"><strong>The "
"next</strong></font><br/><font style=\"background-image: linear-"
"gradient(135deg, var(--o-color-1) 0%, var(--o-color-2) 100%);\" "
"class=\"text-gradient\"> <strong>summer</strong></font><br/><font "
"style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%, "
"var(--o-color-2) 100%);\" class=\"text-gradient\"> "
"<strong>collection</strong></font>"
msgstr ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%,"
" var(--o-color-2) 100%);\" class=\"text-gradient\"><strong>De "
"volgende</strong></font><br/><font style=\"background-image: linear-"
"gradient(135deg, var(--o-color-1) 0%, var(--o-color-2) 100%);\" "
"class=\"text-gradient\"> <strong>zomer</strong></font><br/><font "
"style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%, "
"var(--o-color-2) 100%);\" class=\"text-gradient\"> "
"<strong>collectie</strong></font>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references_grid
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references_social
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%,"
" var(--o-color-2) 100%);\" class=\"text-gradient\">Our top brands</font>"
msgstr ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%,"
" var(--o-color-2) 100%);\" class=\"text-gradient\">Onze topmerken</font>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%, var(--o-color-5) 100%);\" class=\"text-gradient\">Mich Stark</font>\n"
"        <br/>"
msgstr ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%, var(--o-color-5) 100%);\" class=\"text-gradient\">Mich Stark</font>\n"
"        <br/>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%,"
" var(--o-color-5) 100%);\" class=\"text-gradient\">Tony Fred</font>"
msgstr ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%,"
" var(--o-color-5) 100%);\" class=\"text-gradient\">Tony Fred</font>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-2) 0%,"
" var(--o-color-5) 100%);\" class=\"text-gradient\">Aline Turner</font>"
msgstr ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-2) 0%,"
" var(--o-color-5) 100%);\" class=\"text-gradient\">Aline Turner</font>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-2) 0%,"
" var(--o-color-5) 100%);\" class=\"text-gradient\">Iris Joe</font>"
msgstr ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-2) 0%,"
" var(--o-color-5) 100%);\" class=\"text-gradient\">Iris Joe</font>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_empowerment
msgid ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/>  Enjoy "
"exclusive deals up to -50% !"
msgstr ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/> Geniet "
"van exclusieve aanbiedingen tot -50%!"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_image_title
msgid "A Deep Dive into Style and Innovation"
msgstr "Een diepe duik in stijl en innovatie"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid ""
"A beautiful midi dress with vibrant floral prints, designed with a flowing "
"skirt and fitted bodice, ideal for summer outings."
msgstr ""
"Een prachtige midi-jurk met levendige bloemenprints, ontworpen met een "
"vloeiende rok en getailleerd lijfje, ideaal voor zomerse uitstapjes."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_key_images
msgid "A glimpse of elegance and comfort in every piece"
msgstr "Een glimp van elegantie en comfort in elk stuk"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Aline Turner"
msgstr "Aline Turner"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She likes to capture the moment from another point of view, another "
"perspective."
msgstr ""
"Aline is een van de iconische mensen in het leven die kunnen zeggen dat ze "
"houden van wat ze doen. Ze legt graag het moment vast vanuit een ander "
"gezichtspunt, een ander perspectief."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_color_blocks_2
msgid ""
"Are you looking for sustainable clothing for men or women? Discover our "
"exclusive fashion brand for both of you."
msgstr ""
"Op zoek naar duurzame kledij voor dames of heren? Ontdek onze exclusieve "
"merken voor beide."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_call_to_action
msgid "Be yourself, create your own style"
msgstr "Wees jezelf. Maak je eigen stijl"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_shape_image
msgid "Celebrating diversity in fashion"
msgstr "Diversiteit in de mode vieren"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "Classic Black Dress"
msgstr "Klassiek zwart jurkje"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid ""
"Classic blue denim trucker jacket with a modern fit, featuring button "
"closures and a slight stretch for comfort."
msgstr ""
"Klassiek blauw denim truckerjack met een moderne pasvorm, knoopsluitingen en"
" een lichte stretch voor comfort."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cover
msgid "Color Matters"
msgstr "Kleur is belangrijk"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_striped_top
msgid ""
"Color trends are already waiting to spring into action for the next summer."
msgstr ""
"De kleurentrends staan al klaar om in actie te komen voor de volgende zomer."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cover
msgid ""
"Color trends are already waiting to spring into action for the next summer.\n"
"        <br/>\n"
"        <br/>"
msgstr ""
"De kleurtrends staan klaar om volgende zomer in actie te schieten.\n"
"        <br/>\n"
"        <br/>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_striped_center_top
msgid "Crafted with passion for every season"
msgstr "Met passie gemaakt voor elk seizoen"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_features_wall
msgid "Custom Tailoring"
msgstr "Maatwerk"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid ""
"Customer delight drives us. Our support team is always on hand, ensuring "
"your fashion journey is smooth and satisfying."
msgstr ""
"Klanttevredenheid is onze drijfveer. Ons supportteam staat altijd voor je "
"klaar om ervoor te zorgen dat je modereis soepel en bevredigend verloopt."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "Denim Trucker Jacket"
msgstr "Denim Trucker Jas"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid ""
"Designed for the modern individual, our fashion seamlessly transitions from "
"day to night, offering versatile pieces for any wardrobe."
msgstr ""
"Onze mode is ontworpen voor het moderne individu, gaat naadloos over van dag"
" naar nacht en biedt veelzijdige stukken voor elke garderobe."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cover
msgid "Discover"
msgstr "Ontdekken"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_key_images
msgid "Discover Our Latest Fashion Collection"
msgstr "Ontdek onze nieuwste modecollectie"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_call_to_action
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_text_cover
msgid "Discover it"
msgstr "Ontdek ze"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_shape_image
msgid "Discover more"
msgstr "Ontdek meer"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid ""
"Discover our latest fashion pieces with a modern touch. Perfectly crafted "
"for every style!"
msgstr ""
"Ontdek onze nieuwste mode met een modern tintje. Perfect gemaakt voor elke "
"stijl!"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cta_box
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_empowerment
msgid "Discover our new collection,<br/>shop our bestsellers today!"
msgstr "Ontdek onze nieuwe collectie,<br/>shop onze bestsellers vandaag nog!"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_freegrid
msgid "Discover the Latest Fashion and Fitness Trends"
msgstr "Ontdek de laatste mode- en fitnesstrends"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_quadrant
msgid ""
"Discover the latest in fashion and sportswear, where style meets "
"performance. Our curated collections are designed to inspire and empower "
"your active lifestyle.<br/><br/> Experience the perfect blend of form and "
"function."
msgstr ""
"Ontdek het nieuwste op het gebied van mode en sportkleding, waar stijl en "
"prestaties elkaar ontmoeten. Onze collecties zijn ontworpen om je te "
"inspireren en je actieve levensstijl te versterken.<br/><br/> Ervaar de "
"perfecte mix van vorm en functie."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_card_offset
msgid ""
"Discover the latest trends in fashion and sportswear, designed to elevate "
"your style and performance. Our collection offers a blend of innovation, "
"comfort, and flair."
msgstr ""
"Ontdek de nieuwste trends op het gebied van mode en sportkleding, ontworpen "
"om je stijl en prestaties te verbeteren. Onze collectie biedt een mix van "
"innovatie, comfort en flair."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_striped_center_top
msgid ""
"Discover the latest trends in fashion, sports, and fitness, designed to "
"empower your unique style and elevate your active lifestyle."
msgstr ""
"Ontdek de nieuwste trends op het gebied van mode, sport en fitness, "
"ontworpen om je unieke stijl kracht bij te zetten en je actieve levensstijl "
"te verbeteren."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_call_to_action
msgid "Discover the new summer collection."
msgstr "Ontdek de nieuwe zomercollectie."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_image_hexagonal
msgid ""
"Discover timeless elegance and modern style with our exclusive fashion "
"collections. Elevate your wardrobe with unique pieces crafted to make a "
"statement."
msgstr ""
"Ontdek tijdloze elegantie en moderne stijl met onze exclusieve "
"modecollecties. Verhoog je garderobe met unieke stukken die gemaakt zijn om "
"een statement te maken."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_carousel_intro
msgid "Discover your style"
msgstr "Ontdek je stijl"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references_social
msgid "Disrupting fashion since 2013"
msgstr "Ontwrichtende mode sinds 2013"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid ""
"Edgy black leather jacket with classic biker details, including zippers, "
"studs, and an asymmetrical front closure."
msgstr ""
"Edgy zwart leren jack met klassieke biker details, zoals ritsen, studs en "
"een asymmetrische sluiting aan de voorkant."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Editor-in-chief"
msgstr "Hoofdredacteur"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_features_wall
msgid ""
"Elevate your wardrobe with personalized styling advice from our experts, "
"helping you express your individuality effortlessly."
msgstr ""
"Verhoog je garderobe met persoonlijk stylingadvies van onze experts, zodat "
"je je individualiteit moeiteloos kunt uitdrukken."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_quadrant
msgid "Elevating Fashion Trends"
msgstr "Modetrends opwaarderen"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_striped_top
msgid "Embrace Style: Discover the Ultimate Experience in Fashion Excellence"
msgstr "Omarm stijl: Ontdek de ultieme ervaring in uitmuntende mode"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_striped_center_top
msgid "Embrace the Future of Fashion"
msgstr "Omarm de toekomst van mode"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_carousel_intro
msgid ""
"Enhance elegance and comfort with our thoughtfully crafted pieces that bring"
" out the best version of you, every day."
msgstr ""
"Verbeter elegantie en comfort met onze zorgvuldig vervaardigde stukken die "
"elke dag de beste versie van jou naar boven halen."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references_social
msgid "Every style for everybody."
msgstr "Elke stijl voor iedereen."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_features_wall
msgid ""
"Experience perfectly fitted clothing with our bespoke tailoring service, "
"designed to match your unique style and body shape."
msgstr ""
"Ervaar perfect passende kleding met onze op maat gemaakte "
"kleermakersservice, ontworpen om bij je unieke stijl en lichaamsvorm te "
"passen."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid "Expertise and Vision"
msgstr "Expertise en visie"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_quadrant
msgid "Explore Collections"
msgstr "Verken Collecties"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_freegrid
msgid "Explore Now"
msgstr "Ontdek nu"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_carousel_intro
msgid ""
"Explore more and find unique fashion that aligns with your personal style, "
"crafted for confidence and sophistication."
msgstr ""
"Ontdek meer en vind unieke mode die aansluit bij je persoonlijke stijl, "
"gemaakt voor zelfvertrouwen en verfijning."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_freegrid
msgid ""
"Explore our collection of fashion and sportswear designed to keep you ahead "
"of the trends. From innovative materials to cutting-edge designs, we offer "
"products that combine style and performance."
msgstr ""
"Ontdek onze collectie mode en sportkleding die is ontworpen om de trends "
"voor te blijven. Van innovatieve materialen tot geavanceerde ontwerpen, wij "
"bieden producten die stijl en prestaties combineren."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_shape_image
msgid ""
"Explore our diverse range of fashion that embraces all identities and "
"styles. We believe in inclusivity, offering collections that represent "
"everyone. Fashion is for all—find your unique expression with us."
msgstr ""
"Ontdek ons gevarieerde modeaanbod dat alle identiteiten en stijlen omarmt. "
"Wij geloven in inclusiviteit en bieden collecties die iedereen "
"vertegenwoordigen. Mode is voor iedereen - vind bij ons je unieke expressie."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_images_mosaic
msgid "Explore our newest styles to elevate your wardrobe."
msgstr "Ontdek onze nieuwste stijlen om je garderobe te verbeteren."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_key_images
msgid "Explore the Trends that Define Style"
msgstr "Ontdek de trends die stijl bepalen"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_color_blocks_2
msgid "Fancy for them"
msgstr "Elegant voor hen"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_color_blocks_2
msgid "Fashion for her"
msgstr "Mode voor haar"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "Floral Midi Dress"
msgstr "Florale midi jurk"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. "
"He loves to keep his hands full by participating in the development of the "
"magazine and does not hesitate to carry out field surveys."
msgstr ""
"Oprichter en belangrijkste visionair, Tony is de drijvende kracht achter het"
" bedrijf. Hij blijft graag bezig, neemt actief deel aan de ontwikkeling van "
"het magazine en aarzelt niet om veldonderzoeken uit te voeren."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_media_list
msgid ""
"Go off beaten track and discover unexpected and curious places. Here’s our "
"ultimate guide to the best-kept secret spots that you simply must visit!"
msgstr ""
"Ga buiten de gebaande paden en ontdek onverwachte en eigenaardige plekken. "
"Hier is onze ultieme gids naar de best bewaarde geheime plekken dat je "
"gewoonweg moet bezoeker!"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid "Innovative Design"
msgstr "Innovatief ontwerp"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_images_mosaic
msgid "Introducing Our Latest Fashion Trends"
msgstr "Onze nieuwste modetrends"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Iris Joe"
msgstr "Iris Joe"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"Iris, with her international experience, helps us easily understand global "
"politics. She is determined to drive success and delivers her professional "
"acumen to bring the magazine to the next level."
msgstr ""
"Dankzij haar internationale ervaring helpt Iris ons de wereldpolitiek "
"gemakkelijk te begrijpen. Ze is vastbesloten om succes te boeken en levert "
"haar professioneel inzicht om het magazine naar een hoger niveau te tillen."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "Leather Moto Jacket"
msgstr "Leren Moto jas"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid ""
"Luxurious satin wrap dress in deep red, with a v-neckline and adjustable "
"waist tie, perfect for evening events."
msgstr ""
"Luxueuze satijnen wikkeljurk in dieprood, met een v-halslijn en verstelbare "
"tailleband, perfect voor avondevenementen."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Mich Stark"
msgstr "Mich Stark"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"Mich loves taking on challenges. With his multi-year experience as a "
"journalist, he wanted to join a team that puts people at the center of the "
"stories."
msgstr ""
"Mich gaat graag uitdagingen aan. Met zijn jarenlange ervaring als journalist"
" wilde hij zich aansluiten bij een team dat mensen in het middelpunt van de "
"verhalen plaatst."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_color_blocks_2
msgid "More Details"
msgstr "Meer details"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "Our Collection"
msgstr "Onze collectie"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid ""
"Our collection captures the essence of timeless elegance, blending classic "
"designs with modern sophistication for every occasion."
msgstr ""
"Onze collectie omvat de essentie van tijdloze elegantie en combineert "
"klassieke ontwerpen met moderne verfijning voor elke gelegenheid."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_key_images
msgid "Perfect outfits for every occasion"
msgstr "Perfecte outfits voor elke gelegenheid"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_color_blocks_2
msgid ""
"Perfectly combined by fashion experts. Get fresh outfit ideas for the new "
"season and every occasion. Discover what's made for him."
msgstr ""
"Perfect samengesteld door mode-experts. Doe nieuwe outfitideeën op voor het "
"nieuwe seizen en elke gelegenheid. Ontdek wat voor hem gemaakt is."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_features_wall
msgid "Personal Styling"
msgstr "Persoonlijke Styling"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Photograph"
msgstr "Foto"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Political Reporter"
msgstr "Politiek verslaggever"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid ""
"Pushing the boundaries of fashion, our innovative designs merge cutting-edge"
" trends with unparalleled comfort and quality."
msgstr ""
"Onze innovatieve ontwerpen verleggen de grenzen van de mode en combineren "
"geavanceerde trends met ongeëvenaard comfort en kwaliteit."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid "Quality and Craftsmanship"
msgstr "Kwaliteit en vakmanschap"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_key_images
msgid "Quality that speaks for itself"
msgstr "Kwaliteit die voor zichzelf spreekt"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_media_list
msgid "Read more"
msgstr "Lees meer"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_key_images
msgid "Redefine your wardrobe with our latest styles"
msgstr "Herdefinieer je garderobe met onze nieuwste stijlen"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid "Redefining Fashion"
msgstr "Mode opnieuw gedefinieerd"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_card_offset
msgid "Redefining Style with Every Step"
msgstr "Herdefiniëren van stijl met elke stap"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references_social
msgid "Redefining elegance since 2009"
msgstr "Een nieuwe definitie van elegantie sinds 2009"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_carousel_intro
msgid "Redefining your style statement"
msgstr "Je stijlstatement herdefiniëren"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_color_blocks_2
msgid ""
"Refresh your daily rotation with our women’s clothing range. Discover our "
"last collection for her for the next summer."
msgstr ""
"Verfris je dagelijkse rotatie met onze dameskledinglijn. Ontdek onze laatste"
" collectie voor haar voor volgende zomer."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_carousel_intro
msgid "Refresh your look with our exclusive designs and expert styling tips."
msgstr ""
"Verfris je look met onze exclusieve ontwerpen en stylingtips van experts."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "Satin Wrap Dress"
msgstr "Satijnen Wikkeljurk"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references_social
msgid "Shaping your style since 2015"
msgstr "Vormgeven aan je stijl sinds 2015"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid ""
"Sharp and sophisticated tailored blazer, crafted from high-quality wool, "
"featuring structured shoulders and a slim fit."
msgstr ""
"Scherpe en verfijnde getailleerde blazer, gemaakt van wol van hoge "
"kwaliteit, met gestructureerde schouders en een slanke pasvorm."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_striped_center_top
msgid "Shop Now"
msgstr "Koop nu"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cta_box
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_empowerment
msgid "Shop now"
msgstr "Shop nu"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_empowerment
msgid "Shop now   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"
msgstr "Nu winkelen  <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_card_offset
msgid "Shop the looks that define the season."
msgstr "Shop de looks die het seizoen bepalen."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Sports Reporter"
msgstr "Sportverslaggever"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_features_wall
msgid ""
"Stay ahead of fashion trends with our insightful forecasts, curated to keep "
"your look fresh and contemporary all year round."
msgstr ""
"Blijf modetrends voor met onze inzichtelijke voorspellingen, samengesteld om"
" je look het hele jaar door fris en eigentijds te houden."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_color_blocks_2
msgid "Style for him"
msgstr "Stijl voor hem"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_media_list
msgid "Summer Essentials"
msgstr "Zomer essentials"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid "Sustainable Craftsmanship"
msgstr "Duurzaam vakmanschap"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid "Sustainable Fashion"
msgstr "Duurzame mode"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "Tailored Blazer"
msgstr "Getailleerde blazer"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid "Tailored Designs"
msgstr "Ontwerpen op maat"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_media_list
msgid ""
"The right summer essentials can make the difference between a good time and "
"a stand-out great time. Discover our selection for everyday wear during the "
"warm summer months."
msgstr ""
"De juiste zomer essentials kunnen het verschil maken tussen een leuke tijd "
"en een uitstekende tijd. Ontdek onze selectie voor dagelijkse kleding "
"tijdens de warme zomermaanden."

#. module: theme_anelusia
#: model:ir.model,name:theme_anelusia.model_theme_utils
msgid "Theme Utils"
msgstr "Thematools"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_sidegrid
msgid ""
"This is a simple hero unit, a simple jumbotron-style component for calling "
"extra attention to featured content or information."
msgstr ""
"Dit is een eenvoudige hero unit, een eenvoudige jumbotron-achtige component "
"om extra aandacht te vragen voor uitgelichte inhoud of informatie."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid "Timeless Elegance"
msgstr "Tijdloze elegantie"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid ""
"Timeless black dress with a flattering silhouette, featuring a soft fabric "
"and elegant neckline, perfect for any occasion."
msgstr ""
"Tijdloze zwarte jurk met een flatterend silhouet, met een zachte stof en "
"elegante halslijn, perfect voor elke gelegenheid."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Tony Fred"
msgstr "Tony Fred"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_image_title
msgid ""
"Transform your wardrobe with our latest fashion collection, where elegance "
"meets versatility. Elevate your style with pieces that blend sophistication "
"and comfort effortlessly."
msgstr ""
"Transformeer je garderobe met onze nieuwste modecollectie, waar elegantie en"
" veelzijdigheid elkaar ontmoeten. Verhoog je stijl met stukken die "
"verfijning en comfort moeiteloos combineren."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references_social
msgid "Transforming trends since 2018"
msgstr "Transformerende trends sinds 2018"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_features_wall
msgid "Trend Forecasting"
msgstr "Trendprognoses"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_sidegrid
msgid "Unleash your potential"
msgstr "Ontketen je potentieel"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_image_hexagonal
msgid "Unvail our <br/>Exclusive <br/>Collections"
msgstr "Maak gebruik van onze <br/>Exclusieve <br/>Collecties"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cta_box
msgid "Unveil the latest, shop the best!<br/><br/>"
msgstr "Onthul het nieuwste, shop het beste!<br/><br/>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid "Versatile Style"
msgstr "Veelzijdige stijl"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid ""
"We are committed to sustainable practices, using eco-friendly materials and "
"supporting ethical craftsmanship in every piece we create."
msgstr ""
"We zetten ons in voor duurzame praktijken, gebruiken milieuvriendelijke "
"materialen en ondersteunen ethisch vakmanschap in elk stuk dat we maken."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid ""
"We craft bespoke styles to reflect your unique identity. Our team "
"collaborates with you to ensure flawless fashion from concept to creation."
msgstr ""
"We maken stijlen op maat die je unieke identiteit weerspiegelen. Ons team "
"werkt met je samen om van concept tot creatie een vlekkeloze mode te "
"garanderen."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid ""
"We deliver innovative fashion solutions for today’s demands. Harnessing the "
"latest trends, we help you set new standards."
msgstr ""
"Wij leveren innovatieve modeoplossingen voor de eisen van vandaag. Door "
"gebruik te maken van de nieuwste trends helpen we je om nieuwe normen te "
"stellen."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid "What we bring to our clients"
msgstr "Wat we onze klanten bieden"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid ""
"With rich experience and fashion expertise, we offer trends and solutions "
"that keep you ahead of the curve."
msgstr ""
"Met onze rijke ervaring en mode-expertise bieden we trends en oplossingen "
"waarmee je voorop blijft lopen."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_media_list
msgid "World's top Hidden gems"
msgstr "De best verborgen juweeltjes ter wereld"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_carousel_intro
msgid "Your wardrobe tailored for you"
msgstr "Je garderobe op maat"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_big_number
msgid "happy customers"
msgstr "tevreden klanten"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "✽  Dresses"
msgstr "jurken"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "✽  Jackets"
msgstr "jassen"
