# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_anelusia
# 
# Translators:
# <PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-07 13:29+0000\n"
"PO-Revision-Date: 2024-09-25 18:03+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_quotes_carousel_minimal
msgid ""
"\" A go-to partner for stylish growth. <br/>Creative, timely, and ahead of "
"the fashion game. \""
msgstr ""
"\"Il partner da scegliere per far crescere il proprio stile. <br/>Creativo, "
"preciso e all'avanguardia.\""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_quotes_carousel_minimal
msgid ""
"\" Exceptional quality and service! <br/>They consistently deliver on all "
"our fashion needs. \""
msgstr ""
"\"Qualità e servizio eccezionali! <br/>Sono sempre in grado di soddisfare "
"tutte le nostre esigenze. \""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_quotes_carousel_minimal
msgid ""
"\" Their designs revamped our image. Fashion-forward and always on trend. \""
msgstr ""
"\"I loro design hanno rivoluzionato la nostra immagine. Alla moda e sempre "
"di tendenza.\""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "$120.00"
msgstr "$120.00"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "$130.00"
msgstr "130,00 $"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "$135.00"
msgstr "135,00 $"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "$145.00"
msgstr "145,00 $"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "$150.00"
msgstr "150,00 $"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "$160.00"
msgstr "160,00 $"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_text_cover
msgid ""
"<br/>Color trends are already waiting to spring into action for the  next "
"summer.<br/>"
msgstr ""
"<br/>Le tendenze cromatiche sono già pronte per la prossima estate.<br/>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_empowerment
msgid "<br/>Unveil the latest, shop the best!<br/><br/>"
msgstr "<br/>Scopri le novità, acquista il meglio!<br/><br/>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_big_number
msgid ""
"<font class=\"text-gradient\" style=\"background-image: linear-gradient(0deg, rgb(29, 32, 48) 25%, rgb(222, 222, 222) 80%);\">\n"
"            100+\n"
"        </font>"
msgstr ""
"<font class=\"text-gradient\" style=\"background-image: linear-gradient(0deg, rgb(29, 32, 48) 25%, rgb(222, 222, 222) 80%);\">\n"
"            100+\n"
"        </font>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_text_cover
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%,"
" var(--o-color-2) 100%);\" class=\"text-gradient\"><strong>The "
"next</strong></font><br/><font style=\"background-image: linear-"
"gradient(135deg, var(--o-color-1) 0%, var(--o-color-2) 100%);\" "
"class=\"text-gradient\"> <strong>summer</strong></font><br/><font "
"style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%, "
"var(--o-color-2) 100%);\" class=\"text-gradient\"> "
"<strong>collection</strong></font>"
msgstr ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%,"
" var(--o-color-2) 100%);\" class=\"text-gradient\"><strong>La "
"prossima</strong></font><br/><font style=\"background-image: linear-"
"gradient(135deg, var(--o-color-1) 0%, var(--o-color-2) 100%);\" "
"class=\"text-gradient\"> <strong>collezione</strong></font><br/><font "
"style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%, "
"var(--o-color-2) 100%);\" class=\"text-gradient\"> "
"<strong>estiva</strong></font>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references_grid
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references_social
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%,"
" var(--o-color-2) 100%);\" class=\"text-gradient\">Our top brands</font>"
msgstr ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%,"
" var(--o-color-2) 100%);\" class=\"text-gradient\">I nostri brand "
"migliori</font>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%, var(--o-color-5) 100%);\" class=\"text-gradient\">Mich Stark</font>\n"
"        <br/>"
msgstr ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%, var(--o-color-5) 100%);\" class=\"text-gradient\">Mich Stark</font>\n"
"        <br/>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%,"
" var(--o-color-5) 100%);\" class=\"text-gradient\">Tony Fred</font>"
msgstr ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%,"
" var(--o-color-5) 100%);\" class=\"text-gradient\">Tony Fred</font>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-2) 0%,"
" var(--o-color-5) 100%);\" class=\"text-gradient\">Aline Turner</font>"
msgstr ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-2) 0%,"
" var(--o-color-5) 100%);\" class=\"text-gradient\">Aline Turner</font>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-2) 0%,"
" var(--o-color-5) 100%);\" class=\"text-gradient\">Iris Joe</font>"
msgstr ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-2) 0%,"
" var(--o-color-5) 100%);\" class=\"text-gradient\">Iris Joe</font>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_empowerment
msgid ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/>  Enjoy "
"exclusive deals up to -50% !"
msgstr ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" "
"role=\"img\"/> Approfitta di offerte esclusive fino a -50%!"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_image_title
msgid "A Deep Dive into Style and Innovation"
msgstr "Un'immersione nello stile e nell'innovazione"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid ""
"A beautiful midi dress with vibrant floral prints, designed with a flowing "
"skirt and fitted bodice, ideal for summer outings."
msgstr ""
"Un bellissimo abito midi con vivaci stampe floreali, progettato con una "
"gonna fluida e un corpetto aderente, ideale per le uscite estive."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_key_images
msgid "A glimpse of elegance and comfort in every piece"
msgstr "Un tocco di eleganza e comodità in ogni pezzo"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Aline Turner"
msgstr "Aline Turner"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She likes to capture the moment from another point of view, another "
"perspective."
msgstr ""
"Aline è una persona iconica, di quelle che possono vantarsi di amare il "
"proprio lavoro. Ama catturare i momenti da un altro punto di vista, una "
"prospettiva completamente diversa."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_color_blocks_2
msgid ""
"Are you looking for sustainable clothing for men or women? Discover our "
"exclusive fashion brand for both of you."
msgstr ""
"Sei alla ricerca di abbigliamento sostenibile per uomini e donne? Scopri il "
"nuovo brand esclusivo."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_call_to_action
msgid "Be yourself, create your own style"
msgstr "Sii te stesso, crea il tuo stile"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_shape_image
msgid "Celebrating diversity in fashion"
msgstr "La celebrazione della diversità nella moda"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "Classic Black Dress"
msgstr "Vestito nero classico"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid ""
"Classic blue denim trucker jacket with a modern fit, featuring button "
"closures and a slight stretch for comfort."
msgstr ""
"Classica giacca trucker in denim blu dalla vestibilità moderna, con chiusure"
" a bottoni e un leggero stretch per il massimo comfort."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cover
msgid "Color Matters"
msgstr "I colori contano"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_striped_top
msgid ""
"Color trends are already waiting to spring into action for the next summer."
msgstr "Le tendenze cromatiche sono già pronte per la prossima estate."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cover
msgid ""
"Color trends are already waiting to spring into action for the next summer.\n"
"        <br/>\n"
"        <br/>"
msgstr ""
"Le tendenze cromatiche sono già pronte per la prossima estate.\n"
"        <br/>\n"
"        <br/>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_striped_center_top
msgid "Crafted with passion for every season"
msgstr "Confezionati con passione per ogni stagione"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_features_wall
msgid "Custom Tailoring"
msgstr "Sartoria su misura"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid ""
"Customer delight drives us. Our support team is always on hand, ensuring "
"your fashion journey is smooth and satisfying."
msgstr ""
"È la soddisfazione dei clienti a guidarci. Il nostro team di assistenza è "
"sempre a disposizione, per garantire che il viaggio nella moda sia fluido e "
"soddisfacente."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "Denim Trucker Jacket"
msgstr "Giacca trucker in denim"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid ""
"Designed for the modern individual, our fashion seamlessly transitions from "
"day to night, offering versatile pieces for any wardrobe."
msgstr ""
"Pensata per l'individuo moderno, la nostra moda passa senza problemi dal "
"giorno alla sera, offrendo pezzi versatili per qualsiasi guardaroba."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cover
msgid "Discover"
msgstr "Scopri"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_key_images
msgid "Discover Our Latest Fashion Collection"
msgstr "Scopri l'ultima collezione"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_call_to_action
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_text_cover
msgid "Discover it"
msgstr "Scopri"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_shape_image
msgid "Discover more"
msgstr "Scopri di più"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid ""
"Discover our latest fashion pieces with a modern touch. Perfectly crafted "
"for every style!"
msgstr ""
"Scopri i nostri ultimi capi con un tocco moderno. Perfettamente realizzati "
"per ogni stile!"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cta_box
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_empowerment
msgid "Discover our new collection,<br/>shop our bestsellers today!"
msgstr ""
"Scopri la nostra nuova collezione,<br/>acquista i capi più venduti oggi!"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_freegrid
msgid "Discover the Latest Fashion and Fitness Trends"
msgstr "Scopri le ultime novità e le tendenze sportive"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_quadrant
msgid ""
"Discover the latest in fashion and sportswear, where style meets "
"performance. Our curated collections are designed to inspire and empower "
"your active lifestyle.<br/><br/> Experience the perfect blend of form and "
"function."
msgstr ""
"Scopri le ultime novità in fatto di moda e abbigliamento sportivo, dove lo "
"stile incontra le prestazioni. Le nostre collezioni curate sono progettate "
"per ispirare e potenziare uno stile di vita attivo.<br/><br/> L'esperienza è"
" la combinazione perfetta di forma e funzione."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_card_offset
msgid ""
"Discover the latest trends in fashion and sportswear, designed to elevate "
"your style and performance. Our collection offers a blend of innovation, "
"comfort, and flair."
msgstr ""
"Scopri le ultime tendenze della moda e dell'abbigliamento sportivo, "
"progettate per rivoluzionare il tuo stile e le tue prestazioni. La nostra "
"collezione offre un mix di innovazione, comfort ed estro."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_striped_center_top
msgid ""
"Discover the latest trends in fashion, sports, and fitness, designed to "
"empower your unique style and elevate your active lifestyle."
msgstr ""
"Scopri le ultime tendenze in fatto di moda, sport e fitness, progettate per "
"potenziare il tuo stile unico e spronare uno stile di vita attivo."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_call_to_action
msgid "Discover the new summer collection."
msgstr "Scopri la nuova collezione estiva."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_image_hexagonal
msgid ""
"Discover timeless elegance and modern style with our exclusive fashion "
"collections. Elevate your wardrobe with unique pieces crafted to make a "
"statement."
msgstr ""
"Scopri l'eleganza senza tempo e lo stile moderno con le nostre esclusive "
"collezioni di moda. Arricchisci il guardaroba con pezzi unici, creati per "
"farsi notare."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_carousel_intro
msgid "Discover your style"
msgstr "Scopri il tuo stile"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references_social
msgid "Disrupting fashion since 2013"
msgstr "Influenzando la moda dal 2013"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid ""
"Edgy black leather jacket with classic biker details, including zippers, "
"studs, and an asymmetrical front closure."
msgstr ""
"Giacca in pelle nera con dettagli classici da motociclista, tra cui "
"cerniere, borchie e chiusura frontale asimmetrica."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Editor-in-chief"
msgstr "Redattore capo"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_features_wall
msgid ""
"Elevate your wardrobe with personalized styling advice from our experts, "
"helping you express your individuality effortlessly."
msgstr ""
"Trasforma il tuo guardaroba con i consigli di stile personalizzati dei "
"nostri esperti, che ti aiuteranno a esprimere la tua unicità senza sforzi."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_quadrant
msgid "Elevating Fashion Trends"
msgstr "Trasformando le tendenze della moda"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_striped_top
msgid "Embrace Style: Discover the Ultimate Experience in Fashion Excellence"
msgstr ""
"Accogli lo stile: scopri l'esperienza definitiva nell'eccellenza della moda"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_striped_center_top
msgid "Embrace the Future of Fashion"
msgstr "Accogli il futuro della moda"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_carousel_intro
msgid ""
"Enhance elegance and comfort with our thoughtfully crafted pieces that bring"
" out the best version of you, every day."
msgstr ""
"Esalta l'eleganza e il comfort con i nostri pezzi realizzati con cura, che "
"fanno emergere la versione migliore di te, ogni giorno."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references_social
msgid "Every style for everybody."
msgstr "Tutti gli stili."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_features_wall
msgid ""
"Experience perfectly fitted clothing with our bespoke tailoring service, "
"designed to match your unique style and body shape."
msgstr ""
"Sperimenta un abbigliamento perfettamente aderente con il nostro servizio di"
" sartoria su misura, progettato per adattarsi al tuo stile e alla tua "
"corporatura."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid "Expertise and Vision"
msgstr "Esperienza e lungimiranza"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_quadrant
msgid "Explore Collections"
msgstr "Esplora le collezioni"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_freegrid
msgid "Explore Now"
msgstr "Esplora ora"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_carousel_intro
msgid ""
"Explore more and find unique fashion that aligns with your personal style, "
"crafted for confidence and sophistication."
msgstr ""
"Esplora di più e trova una moda unica che si allinei al tuo stile personale,"
" realizzata per garantire sicurezza e raffinatezza."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_freegrid
msgid ""
"Explore our collection of fashion and sportswear designed to keep you ahead "
"of the trends. From innovative materials to cutting-edge designs, we offer "
"products that combine style and performance."
msgstr ""
"Scopri la nostra collezione di abbigliamento sportivo e di moda progettato "
"per essere sempre al passo con le tendenze. Dai materiali innovativi ai "
"design all'avanguardia, offriamo prodotti che combinano stile e prestazioni."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_shape_image
msgid ""
"Explore our diverse range of fashion that embraces all identities and "
"styles. We believe in inclusivity, offering collections that represent "
"everyone. Fashion is for all—find your unique expression with us."
msgstr ""
"Scopri la nostra variegata gamma di moda che abbraccia tutte le identità e "
"gli stili. Crediamo nell'inclusività e offriamo collezioni che rappresentano"
" tutti. La moda è per tutti: con noi troverai la tua espressione unica."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_images_mosaic
msgid "Explore our newest styles to elevate your wardrobe."
msgstr "Scopri le ultime creazioni per rendere unico il tuo guardaroba."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_key_images
msgid "Explore the Trends that Define Style"
msgstr "Scopri le tendenze che dettano lo stile"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_color_blocks_2
msgid "Fancy for them"
msgstr "Stravaganti per loro"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_color_blocks_2
msgid "Fashion for her"
msgstr "Alla moda per lei"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "Floral Midi Dress"
msgstr "Vestito midi floreale"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. "
"He loves to keep his hands full by participating in the development of the "
"magazine and does not hesitate to carry out field surveys."
msgstr ""
"Fondatore e capo visionario, Tony è la forza motrice dell'azienda. Ama "
"tenersi occupato partecipando allo sviluppo del giornale e non esita a "
"effettuare indagini sul campo."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_media_list
msgid ""
"Go off beaten track and discover unexpected and curious places. Here’s our "
"ultimate guide to the best-kept secret spots that you simply must visit!"
msgstr ""
"Abbandona i sentieri battuti e scopri luoghi inaspettati e curiosi. Ecco la "
"nostra guida definitiva ai luoghi segreti meglio custoditi che dovete "
"assolutamente visitare!"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid "Innovative Design"
msgstr "Design innovativo"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_images_mosaic
msgid "Introducing Our Latest Fashion Trends"
msgstr "Le nostre ultime tendenze di moda"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Iris Joe"
msgstr "Iris Joe"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"Iris, with her international experience, helps us easily understand global "
"politics. She is determined to drive success and delivers her professional "
"acumen to bring the magazine to the next level."
msgstr ""
"Grazie alla sua esperienza internazionale, Iris aiuta a comprendere le "
"politiche mondiali. È determinata ad avere successo mettendo le sue capacità"
" professionali al servizio del giornale per renderlo sempre più famoso."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "Leather Moto Jacket"
msgstr "Giacca moto in pelle"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid ""
"Luxurious satin wrap dress in deep red, with a v-neckline and adjustable "
"waist tie, perfect for evening events."
msgstr ""
"Lussuoso abito a portafoglio in raso di colore rosso intenso, con scollo a V"
" e laccetto regolabile in vita, perfetto per gli eventi serali."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Mich Stark"
msgstr "Mich Stark"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"Mich loves taking on challenges. With his multi-year experience as a "
"journalist, he wanted to join a team that puts people at the center of the "
"stories."
msgstr ""
"Mich ama affrontare le sfide. Grazie alla sua esperienza pluriennale come "
"giornalista, ha voluto unirsi a un team che mette le persone al centro delle"
" storie."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_color_blocks_2
msgid "More Details"
msgstr "Maggiori dettagli"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "Our Collection"
msgstr "La nostra collezione"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid ""
"Our collection captures the essence of timeless elegance, blending classic "
"designs with modern sophistication for every occasion."
msgstr ""
"La nostra collezione cattura l'essenza di un'eleganza senza tempo, unendo "
"modelli classici alla raffinatezza moderna per ogni occasione."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_key_images
msgid "Perfect outfits for every occasion"
msgstr "Outfit perfetti per ogni occasione"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_color_blocks_2
msgid ""
"Perfectly combined by fashion experts. Get fresh outfit ideas for the new "
"season and every occasion. Discover what's made for him."
msgstr ""
"La combinazione perfetta degli esperti di moda. Ottieni nuove idee di outfit"
" per la nuova stagione e per ogni occasione. Scopri la selezione per lui."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_features_wall
msgid "Personal Styling"
msgstr "Styling personale"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Photograph"
msgstr "Fotografo"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Political Reporter"
msgstr "Giornalista politico"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid ""
"Pushing the boundaries of fashion, our innovative designs merge cutting-edge"
" trends with unparalleled comfort and quality."
msgstr ""
"I nostri modelli innovativi, spingendosi oltre i confini della moda, fondono"
" tendenze all'avanguardia con un comfort e una qualità senza pari."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid "Quality and Craftsmanship"
msgstr "Qualità e artigianato"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_key_images
msgid "Quality that speaks for itself"
msgstr "La qualità che parla da sé"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_media_list
msgid "Read more"
msgstr "Leggi di più"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_key_images
msgid "Redefine your wardrobe with our latest styles"
msgstr "Rivoluziona il tuo guardaroba grazie ai nostri ultimi modelli"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid "Redefining Fashion"
msgstr "Ridefinire la moda"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_card_offset
msgid "Redefining Style with Every Step"
msgstr "Ridefinire lo stile in ogni passo"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references_social
msgid "Redefining elegance since 2009"
msgstr "Ridefinendo l'eleganza dal 2009"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_carousel_intro
msgid "Redefining your style statement"
msgstr "Ridefinire il proprio stile"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_color_blocks_2
msgid ""
"Refresh your daily rotation with our women’s clothing range. Discover our "
"last collection for her for the next summer."
msgstr ""
"Rinfresca i tuoi outfit quotidiani con la nostra gamma di abbigliamento "
"femminile. Scopri la nostra nuova collezione per lei."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_carousel_intro
msgid "Refresh your look with our exclusive designs and expert styling tips."
msgstr ""
"Dai una ventata di aria fresca al tuo look grazie ai nostri design esclusivi"
" e ai consigli degli esperti."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "Satin Wrap Dress"
msgstr "Vestito a portafoglio in satin"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references_social
msgid "Shaping your style since 2015"
msgstr "Definendo lo stile dal 2015"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid ""
"Sharp and sophisticated tailored blazer, crafted from high-quality wool, "
"featuring structured shoulders and a slim fit."
msgstr ""
"Blazer sartoriale elegante e sofisticato, realizzato in lana di alta "
"qualità, con spalle strutturate e vestibilità slim."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_striped_center_top
msgid "Shop Now"
msgstr "Acquista ora"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cta_box
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_empowerment
msgid "Shop now"
msgstr "Acquista ora"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_empowerment
msgid "Shop now   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"
msgstr "Acquista ora   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_card_offset
msgid "Shop the looks that define the season."
msgstr "Acquista i look di stagione."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Sports Reporter"
msgstr "Giornalista sportivo"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_features_wall
msgid ""
"Stay ahead of fashion trends with our insightful forecasts, curated to keep "
"your look fresh and contemporary all year round."
msgstr ""
"Rimani al passo con le tendenze grazie alle nostre previsioni accurate, per "
"mantenere il tuo look fresco e contemporaneo tutto l'anno."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_color_blocks_2
msgid "Style for him"
msgstr "Pensato per lui"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_media_list
msgid "Summer Essentials"
msgstr "I capi essenziali per l'estate"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid "Sustainable Craftsmanship"
msgstr "Artigianato sostenibile"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid "Sustainable Fashion"
msgstr "Moda sostenibile"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "Tailored Blazer"
msgstr "Blazer su misura"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid "Tailored Designs"
msgstr "Modelli su misura"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_media_list
msgid ""
"The right summer essentials can make the difference between a good time and "
"a stand-out great time. Discover our selection for everyday wear during the "
"warm summer months."
msgstr ""
"I capi giusti possono fare la differenza tra una bella giornata e una "
"giornata fantastica. Scopri la nostra selezione per l'abbigliamento di tutti"
" i giorni a tema estate."

#. module: theme_anelusia
#: model:ir.model,name:theme_anelusia.model_theme_utils
msgid "Theme Utils"
msgstr "Utilità tema"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_sidegrid
msgid ""
"This is a simple hero unit, a simple jumbotron-style component for calling "
"extra attention to featured content or information."
msgstr ""
"Questa è una unità Hero, un semplice componente stile Jumbotron per la "
"presentazione di contenuti o informazioni in evidenza."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid "Timeless Elegance"
msgstr "Eleganza senza tempo"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid ""
"Timeless black dress with a flattering silhouette, featuring a soft fabric "
"and elegant neckline, perfect for any occasion."
msgstr ""
"Abito nero intramontabile dalla silhouette accattivante, caratterizzato da "
"un tessuto morbido e da una scollatura elegante, perfetto per ogni "
"occasione."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Tony Fred"
msgstr "Tony Fred"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_image_title
msgid ""
"Transform your wardrobe with our latest fashion collection, where elegance "
"meets versatility. Elevate your style with pieces that blend sophistication "
"and comfort effortlessly."
msgstr ""
"Trasforma il guardaroba con la nostra ultima collezione di moda, dove "
"l'eleganza incontra la versatilità. Rivoluziona il tuo stile con capi che "
"uniscono raffinatezza e comfort senza sforzo."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references_social
msgid "Transforming trends since 2018"
msgstr "Trasformando le tendenze dal 2018"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_features_wall
msgid "Trend Forecasting"
msgstr "Previsione delle tendenze"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_sidegrid
msgid "Unleash your potential"
msgstr "Scatena il tuo potenziale"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_image_hexagonal
msgid "Unvail our <br/>Exclusive <br/>Collections"
msgstr "Scopri le nostre collezioni <br/>esclusive <br/>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cta_box
msgid "Unveil the latest, shop the best!<br/><br/>"
msgstr "Scopri le novità, acquista il meglio!<br/><br/>"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid "Versatile Style"
msgstr "Stile versatile"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid ""
"We are committed to sustainable practices, using eco-friendly materials and "
"supporting ethical craftsmanship in every piece we create."
msgstr ""
"Ci impegniamo a sfruttare pratiche sostenibili, utilizzando materiali "
"ecologici e sostenendo l'artigianato etico in ogni pezzo che creiamo."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid ""
"We craft bespoke styles to reflect your unique identity. Our team "
"collaborates with you to ensure flawless fashion from concept to creation."
msgstr ""
"Creiamo stili su misura per riflettere un'identità unica. Il nostro team "
"collabora con te per garantire una moda impeccabile dall'idea alla "
"creazione."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid ""
"We deliver innovative fashion solutions for today’s demands. Harnessing the "
"latest trends, we help you set new standards."
msgstr ""
"Forniamo soluzioni di moda innovative per le esigenze di oggi. Ti aiutiamo a"
" stabilire nuovi standard sfruttando le ultime tendenze."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid "What we bring to our clients"
msgstr "Cosa offriamo ai nostri clienti"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid ""
"With rich experience and fashion expertise, we offer trends and solutions "
"that keep you ahead of the curve."
msgstr ""
"Grazie all'esperienza e alla competenza nel campo della moda, offriamo "
"tendenze e soluzioni che ti permetteranno di essere sempre all'avanguardia."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_media_list
msgid "World's top Hidden gems"
msgstr "Le migliori perle nascoste del mondo"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_carousel_intro
msgid "Your wardrobe tailored for you"
msgstr "Un guardaroba su misura per te"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_big_number
msgid "happy customers"
msgstr "clienti felici"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "✽  Dresses"
msgstr "✽  Vestiti"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "✽  Jackets"
msgstr "✽  Giacche"
