# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_anelusia
# 
# Translators:
# <PERSON><PERSON>do<PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-07 13:29+0000\n"
"PO-Revision-Date: 2024-09-25 18:03+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_quotes_carousel_minimal
msgid ""
"\" A go-to partner for stylish growth. <br/>Creative, timely, and ahead of "
"the fashion game. \""
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_quotes_carousel_minimal
msgid ""
"\" Exceptional quality and service! <br/>They consistently deliver on all "
"our fashion needs. \""
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_quotes_carousel_minimal
msgid ""
"\" Their designs revamped our image. Fashion-forward and always on trend. \""
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "$120.00"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "$130.00"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "$135.00"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "$145.00"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "$150.00"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "$160.00"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_text_cover
msgid ""
"<br/>Color trends are already waiting to spring into action for the  next "
"summer.<br/>"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_empowerment
msgid "<br/>Unveil the latest, shop the best!<br/><br/>"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_big_number
msgid ""
"<font class=\"text-gradient\" style=\"background-image: linear-gradient(0deg, rgb(29, 32, 48) 25%, rgb(222, 222, 222) 80%);\">\n"
"            100+\n"
"        </font>"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_text_cover
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%,"
" var(--o-color-2) 100%);\" class=\"text-gradient\"><strong>The "
"next</strong></font><br/><font style=\"background-image: linear-"
"gradient(135deg, var(--o-color-1) 0%, var(--o-color-2) 100%);\" "
"class=\"text-gradient\"> <strong>summer</strong></font><br/><font "
"style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%, "
"var(--o-color-2) 100%);\" class=\"text-gradient\"> "
"<strong>collection</strong></font>"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references_grid
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references_social
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%,"
" var(--o-color-2) 100%);\" class=\"text-gradient\">Our top brands</font>"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%, var(--o-color-5) 100%);\" class=\"text-gradient\">Mich Stark</font>\n"
"        <br/>"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-1) 0%,"
" var(--o-color-5) 100%);\" class=\"text-gradient\">Tony Fred</font>"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-2) 0%,"
" var(--o-color-5) 100%);\" class=\"text-gradient\">Aline Turner</font>"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"<font style=\"background-image: linear-gradient(135deg, var(--o-color-2) 0%,"
" var(--o-color-5) 100%);\" class=\"text-gradient\">Iris Joe</font>"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_empowerment
msgid ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/>  Enjoy "
"exclusive deals up to -50% !"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_image_title
msgid "A Deep Dive into Style and Innovation"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid ""
"A beautiful midi dress with vibrant floral prints, designed with a flowing "
"skirt and fitted bodice, ideal for summer outings."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_key_images
msgid "A glimpse of elegance and comfort in every piece"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Aline Turner"
msgstr "Aline Turner"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She likes to capture the moment from another point of view, another "
"perspective."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_color_blocks_2
msgid ""
"Are you looking for sustainable clothing for men or women? Discover our "
"exclusive fashion brand for both of you."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_call_to_action
msgid "Be yourself, create your own style"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_shape_image
msgid "Celebrating diversity in fashion"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "Classic Black Dress"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid ""
"Classic blue denim trucker jacket with a modern fit, featuring button "
"closures and a slight stretch for comfort."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cover
msgid "Color Matters"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_striped_top
msgid ""
"Color trends are already waiting to spring into action for the next summer."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cover
msgid ""
"Color trends are already waiting to spring into action for the next summer.\n"
"        <br/>\n"
"        <br/>"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_striped_center_top
msgid "Crafted with passion for every season"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_features_wall
msgid "Custom Tailoring"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid ""
"Customer delight drives us. Our support team is always on hand, ensuring "
"your fashion journey is smooth and satisfying."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "Denim Trucker Jacket"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid ""
"Designed for the modern individual, our fashion seamlessly transitions from "
"day to night, offering versatile pieces for any wardrobe."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cover
msgid "Discover"
msgstr "Objevujte"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_key_images
msgid "Discover Our Latest Fashion Collection"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_call_to_action
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_text_cover
msgid "Discover it"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_shape_image
msgid "Discover more"
msgstr "Objevte více"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid ""
"Discover our latest fashion pieces with a modern touch. Perfectly crafted "
"for every style!"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cta_box
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_empowerment
msgid "Discover our new collection,<br/>shop our bestsellers today!"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_freegrid
msgid "Discover the Latest Fashion and Fitness Trends"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_quadrant
msgid ""
"Discover the latest in fashion and sportswear, where style meets "
"performance. Our curated collections are designed to inspire and empower "
"your active lifestyle.<br/><br/> Experience the perfect blend of form and "
"function."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_card_offset
msgid ""
"Discover the latest trends in fashion and sportswear, designed to elevate "
"your style and performance. Our collection offers a blend of innovation, "
"comfort, and flair."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_striped_center_top
msgid ""
"Discover the latest trends in fashion, sports, and fitness, designed to "
"empower your unique style and elevate your active lifestyle."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_call_to_action
msgid "Discover the new summer collection."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_image_hexagonal
msgid ""
"Discover timeless elegance and modern style with our exclusive fashion "
"collections. Elevate your wardrobe with unique pieces crafted to make a "
"statement."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_carousel_intro
msgid "Discover your style"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references_social
msgid "Disrupting fashion since 2013"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid ""
"Edgy black leather jacket with classic biker details, including zippers, "
"studs, and an asymmetrical front closure."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Editor-in-chief"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_features_wall
msgid ""
"Elevate your wardrobe with personalized styling advice from our experts, "
"helping you express your individuality effortlessly."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_quadrant
msgid "Elevating Fashion Trends"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_striped_top
msgid "Embrace Style: Discover the Ultimate Experience in Fashion Excellence"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_striped_center_top
msgid "Embrace the Future of Fashion"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_carousel_intro
msgid ""
"Enhance elegance and comfort with our thoughtfully crafted pieces that bring"
" out the best version of you, every day."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references_social
msgid "Every style for everybody."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_features_wall
msgid ""
"Experience perfectly fitted clothing with our bespoke tailoring service, "
"designed to match your unique style and body shape."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid "Expertise and Vision"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_quadrant
msgid "Explore Collections"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_freegrid
msgid "Explore Now"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_carousel_intro
msgid ""
"Explore more and find unique fashion that aligns with your personal style, "
"crafted for confidence and sophistication."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_freegrid
msgid ""
"Explore our collection of fashion and sportswear designed to keep you ahead "
"of the trends. From innovative materials to cutting-edge designs, we offer "
"products that combine style and performance."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_shape_image
msgid ""
"Explore our diverse range of fashion that embraces all identities and "
"styles. We believe in inclusivity, offering collections that represent "
"everyone. Fashion is for all—find your unique expression with us."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_images_mosaic
msgid "Explore our newest styles to elevate your wardrobe."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_key_images
msgid "Explore the Trends that Define Style"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_color_blocks_2
msgid "Fancy for them"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_color_blocks_2
msgid "Fashion for her"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "Floral Midi Dress"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. "
"He loves to keep his hands full by participating in the development of the "
"magazine and does not hesitate to carry out field surveys."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_media_list
msgid ""
"Go off beaten track and discover unexpected and curious places. Here’s our "
"ultimate guide to the best-kept secret spots that you simply must visit!"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid "Innovative Design"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_images_mosaic
msgid "Introducing Our Latest Fashion Trends"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Iris Joe"
msgstr "Iris Joe"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"Iris, with her international experience, helps us easily understand global "
"politics. She is determined to drive success and delivers her professional "
"acumen to bring the magazine to the next level."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "Leather Moto Jacket"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid ""
"Luxurious satin wrap dress in deep red, with a v-neckline and adjustable "
"waist tie, perfect for evening events."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Mich Stark"
msgstr "Mich Stark"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
msgid ""
"Mich loves taking on challenges. With his multi-year experience as a "
"journalist, he wanted to join a team that puts people at the center of the "
"stories."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_color_blocks_2
msgid "More Details"
msgstr "Více informací"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "Our Collection"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid ""
"Our collection captures the essence of timeless elegance, blending classic "
"designs with modern sophistication for every occasion."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_key_images
msgid "Perfect outfits for every occasion"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_color_blocks_2
msgid ""
"Perfectly combined by fashion experts. Get fresh outfit ideas for the new "
"season and every occasion. Discover what's made for him."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_features_wall
msgid "Personal Styling"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Photograph"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Political Reporter"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid ""
"Pushing the boundaries of fashion, our innovative designs merge cutting-edge"
" trends with unparalleled comfort and quality."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid "Quality and Craftsmanship"
msgstr "Kvalita a řemeslné zpracování"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_key_images
msgid "Quality that speaks for itself"
msgstr "Kvalita, která mluví sama za sebe"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_media_list
msgid "Read more"
msgstr "Přečtěte si více"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_key_images
msgid "Redefine your wardrobe with our latest styles"
msgstr "Změňte definici svého šatníku s našimi nejnovějšími styly"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid "Redefining Fashion"
msgstr "Nová definice módy"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_card_offset
msgid "Redefining Style with Every Step"
msgstr "Nový styl na každém kroku"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references_social
msgid "Redefining elegance since 2009"
msgstr "Nová definice elegance od roku 2009"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_carousel_intro
msgid "Redefining your style statement"
msgstr "Znovu definujte svůj styl"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_color_blocks_2
msgid ""
"Refresh your daily rotation with our women’s clothing range. Discover our "
"last collection for her for the next summer."
msgstr ""
"Osvěžte svou každodenní rotaci s naší nabídkou dámského oblečení. Objevte "
"naši poslední kolekci pro ni na příští léto."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_carousel_intro
msgid "Refresh your look with our exclusive designs and expert styling tips."
msgstr ""
"Osvěžte svůj vzhled pomocí našich exkluzivních návrhů a odborných tipů na "
"styling."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "Satin Wrap Dress"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references_social
msgid "Shaping your style since 2015"
msgstr "Tvoříme váš styl od roku 2015"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid ""
"Sharp and sophisticated tailored blazer, crafted from high-quality wool, "
"featuring structured shoulders and a slim fit."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_striped_center_top
msgid "Shop Now"
msgstr "Nakoupit teď"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cta_box
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_empowerment
msgid "Shop now"
msgstr "Nakoupit teď"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_empowerment
msgid "Shop now   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_card_offset
msgid "Shop the looks that define the season."
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Sports Reporter"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_features_wall
msgid ""
"Stay ahead of fashion trends with our insightful forecasts, curated to keep "
"your look fresh and contemporary all year round."
msgstr ""
"Držte si náskok před módními trendy díky našim pronikavým předpovědím, které"
" se starají o to, aby váš vzhled byl svěží a moderní po celý rok."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_color_blocks_2
msgid "Style for him"
msgstr "Styl pro něho"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_media_list
msgid "Summer Essentials"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid "Sustainable Craftsmanship"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid "Sustainable Fashion"
msgstr "Udržitelná móda"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "Tailored Blazer"
msgstr "Sako na míru"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid "Tailored Designs"
msgstr "Návrhy na míru"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_media_list
msgid ""
"The right summer essentials can make the difference between a good time and "
"a stand-out great time. Discover our selection for everyday wear during the "
"warm summer months."
msgstr ""
"Správné letní potřeby mohou znamenat rozdíl mezi dobrou zábavou a skvělou "
"zábavou. Objevte naši nabídku pro každodenní nošení během teplých letních "
"měsíců."

#. module: theme_anelusia
#: model:ir.model,name:theme_anelusia.model_theme_utils
msgid "Theme Utils"
msgstr "Utils šablony"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_sidegrid
msgid ""
"This is a simple hero unit, a simple jumbotron-style component for calling "
"extra attention to featured content or information."
msgstr ""
"Jedná se o jednoduchou jednotku hrdiny, jednoduchou komponentu ve stylu "
"jumbotronu, která věnuje zvláštní pozornost doporučenému obsahu nebo "
"informacím."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid "Timeless Elegance"
msgstr "Nadčasová elegance"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid ""
"Timeless black dress with a flattering silhouette, featuring a soft fabric "
"and elegant neckline, perfect for any occasion."
msgstr ""
"Nadčasové černé šaty lichotivé siluety z měkkého materiálu a s elegantním "
"výstřihem, ideální pro každou příležitost."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_company_team_basic
msgid "Tony Fred"
msgstr "Tony Fred"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_image_title
msgid ""
"Transform your wardrobe with our latest fashion collection, where elegance "
"meets versatility. Elevate your style with pieces that blend sophistication "
"and comfort effortlessly."
msgstr ""
"Proměňte svůj šatník s naší nejnovější módní kolekcí, v níž se elegance "
"snoubí s všestranností. Pozvedněte svůj styl díky kouskům, které bez námahy "
"kombinují sofistikovanost a pohodlí."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_references_social
msgid "Transforming trends since 2018"
msgstr "Proměny trendů od roku 2018"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_features_wall
msgid "Trend Forecasting"
msgstr "Předvídání trendů"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_sidegrid
msgid "Unleash your potential"
msgstr "Rozvíjejte svůj potenciál"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_image_hexagonal
msgid "Unvail our <br/>Exclusive <br/>Collections"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cta_box
msgid "Unveil the latest, shop the best!<br/><br/>"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid "Versatile Style"
msgstr ""

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_cards_grid
msgid ""
"We are committed to sustainable practices, using eco-friendly materials and "
"supporting ethical craftsmanship in every piece we create."
msgstr ""
"Zavázali jsme se k udržitelným postupům, používáme ekologické materiály a "
"podporujeme etické řemeslné zpracování každého kusu, který vytvoříme."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid ""
"We craft bespoke styles to reflect your unique identity. Our team "
"collaborates with you to ensure flawless fashion from concept to creation."
msgstr ""
"Vytváříme styly na míru, které odrážejí vaši jedinečnou identitu. Náš tým s "
"vámi spolupracuje, aby zajistil dokonalou módu od konceptu až po vytvoření."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid ""
"We deliver innovative fashion solutions for today’s demands. Harnessing the "
"latest trends, we help you set new standards."
msgstr ""
"Poskytujeme inovativní módní řešení pro dnešní požadavky. Využíváme "
"nejnovější trendy a pomáháme vám nastavit nové standardy."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid "What we bring to our clients"
msgstr "Co přinášíme našim klientům"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_wavy_grid
msgid ""
"With rich experience and fashion expertise, we offer trends and solutions "
"that keep you ahead of the curve."
msgstr ""
"Díky bohatým zkušenostem a odborným znalostem v oblasti módy nabízíme trendy"
" a řešení, která vás udrží na špici."

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_media_list
msgid "World's top Hidden gems"
msgstr "Nejlepší skryté klenoty světa"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_carousel_intro
msgid "Your wardrobe tailored for you"
msgstr "Váš šatník na míru"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_big_number
msgid "happy customers"
msgstr "spokojení zákazníci"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "✽  Dresses"
msgstr "✽  Šaty"

#. module: theme_anelusia
#: model_terms:theme.ir.ui.view,arch:theme_anelusia.s_pricelist_boxed
msgid "✽  Jackets"
msgstr "✽  Saka"
