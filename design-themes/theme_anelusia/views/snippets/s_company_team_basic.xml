<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="s_company_team_basic" inherit_id="website.s_company_team_basic">
    <!-- Team #01 - Title -->
    <xpath expr="//div[hasclass('o_grid_mode')]/div[2]/h4" position="replace" mode="inner">
        <PERSON>
    </xpath>
    <!-- Team #01 - Work Description -->
    <xpath expr="//div[hasclass('o_grid_mode')]/div[2]/p[last()]" position="replace" mode="inner">
        Editor-in-chief
    </xpath>
    <!-- Team #01 - Img -->
    <xpath expr="(//img)[1]" position="attributes">
        <attribute name="class" remove="rounded-circle" separator=" "/>
        <attribute name="src">/web_editor/image_shape/website.s_company_team_image_1/web_editor/geometric/geo_square_6.svg</attribute>
        <attribute name="data-shape">web_editor/geometric/geo_square_6</attribute>
        <attribute name="data-original-mimetype">image/jpeg</attribute>
        <attribute name="data-file-name">uiface_1.svg</attribute>
        <attribute name="data-shape-colors">;;;;</attribute>
    </xpath>

    <!-- Team #02 - Title -->
    <xpath expr="//div[hasclass('o_grid_mode')]/div[3]/h4" position="replace" mode="inner">
        Mich Stark
    </xpath>
    <!-- Team #02 - Work Description -->
    <xpath expr="//div[hasclass('o_grid_mode')]/div[3]/p[last()]" position="replace" mode="inner">
        Sports Reporter
    </xpath>
    <!-- Team #02 - Img -->
    <xpath expr="(//img)[2]" position="attributes">
        <attribute name="class" remove="rounded-circle" separator=" "/>
        <attribute name="src">/web_editor/image_shape/website.s_company_team_image_2/web_editor/geometric/geo_square_4.svg</attribute>
        <attribute name="data-shape">web_editor/geometric/geo_square_4</attribute>
        <attribute name="data-original-mimetype">image/jpeg</attribute>
        <attribute name="data-file-name">uiface_2.svg</attribute>
        <attribute name="data-shape-colors">;;;;</attribute>
    </xpath>

    <!-- Team #03 - Title -->
    <xpath expr="//div[hasclass('o_grid_mode')]/div[4]/h4" position="replace" mode="inner">
        Aline Turner
    </xpath>
    <!-- Team #03 - Work Description -->
    <xpath expr="//div[hasclass('o_grid_mode')]/div[4]/p[last()]" position="replace" mode="inner">
        Photograph
    </xpath>
    <!-- Team #03 - Img -->
    <xpath expr="(//img)[3]" position="attributes">
        <attribute name="class" remove="rounded-circle" separator=" "/>
        <attribute name="src">/web_editor/image_shape/website.s_company_team_image_3/web_editor/geometric/geo_square_3.svg</attribute>
        <attribute name="data-shape">web_editor/geometric/geo_square_3</attribute>
        <attribute name="data-original-mimetype">image/jpeg</attribute>
        <attribute name="data-file-name">uiface_3.svg</attribute>
        <attribute name="data-shape-colors">;;;;</attribute>
    </xpath>

    <!-- Team #04 - Title -->
    <xpath expr="//div[hasclass('o_grid_mode')]/div[5]/h4" position="replace" mode="inner">
        Iris Joe
    </xpath>
    <!-- Team #04 - Work Description -->
    <xpath expr="//div[hasclass('o_grid_mode')]/div[5]/p[last()]" position="replace" mode="inner">
        Political Reporter
    </xpath>
    <!-- Team #04 - Img -->
    <xpath expr="(//img)[4]" position="attributes">
        <attribute name="class" remove="rounded-circle" separator=" "/>
        <attribute name="src">/web_editor/image_shape/website.s_company_team_image_4/web_editor/geometric/geo_square_5.svg</attribute>
        <attribute name="data-shape">web_editor/geometric/geo_square_5</attribute>
        <attribute name="data-original-mimetype">image/jpeg</attribute>
        <attribute name="data-file-name">uiface_4.svg</attribute>
        <attribute name="data-shape-colors">;;;;</attribute>
    </xpath>

</template>

</odoo>
