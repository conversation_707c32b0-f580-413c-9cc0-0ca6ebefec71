<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="s_call_to_action" inherit_id="website.s_call_to_action">
    <!-- Section -->
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="pt120 pb104 o_cc4" remove="pt64 pb64 o_cc5" separator=" "/>
        <attribute name="data-oe-shape-data">{"shape":"web_editor/Floats/02","flip":[]}</attribute>
    </xpath>
    <!-- Shape -->
    <xpath expr="//div[hasclass('container')]" position="before">
        <div class="o_we_shape o_web_editor_Floats_02"/>
    </xpath>
    <!-- Title & subtitle -->
    <xpath expr="//div[hasclass('col-lg-9')]" position="attributes">
        <attribute name="class" add="col-lg-12" remove="col-lg-9" separator=" "/>
    </xpath>
    <xpath expr="//h3" position="replace" mode="inner">
        Be yourself, create your own style
    </xpath>
    <xpath expr="//h3" position="attributes">
        <attribute name="style">text-align: center;</attribute>
    </xpath>
    <xpath expr="//p" position="replace" mode="inner">
        Discover the new summer collection.
    </xpath>
    <xpath expr="//p" position="attributes">
        <attribute name="style">text-align: center;</attribute>
    </xpath>
    <!-- Button -->
    <xpath expr="//div[hasclass('col-lg-3')]" position="attributes">
        <attribute name="class" add="col-lg-12" remove="col-lg-3" separator=" "/>
    </xpath>
    <xpath expr="(//p)[2]" position="attributes">
        <attribute name="style">text-align: center;</attribute>
    </xpath>
    <xpath expr="//a[hasclass('btn')]/t" position="replace" mode="inner">
        Discover it
    </xpath>
</template>

</odoo>
