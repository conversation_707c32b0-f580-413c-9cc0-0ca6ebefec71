<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="s_text_image" inherit_id="website.s_text_image">
    <!-- Section -->
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="o_cc o_cc5" separator=" "/>
    </xpath>
    <!-- Img -->
    <xpath expr="//img" position="attributes">
        <attribute name="src">/web_editor/image_shape/website.s_text_image_default_image/web_editor/composition/composition_square_3.svg?c1=o-color-2&amp;c5=o-color-1</attribute>
        <attribute name="data-shape">web_editor/composition/composition_square_3</attribute>
        <attribute name="data-original-mimetype">image/jpeg</attribute>
        <attribute name="data-file-name">s_text_image.svg</attribute>
        <attribute name="data-shape-colors">o-color-2;;;;o-color-1</attribute>
    </xpath>
</template>

</odoo>
