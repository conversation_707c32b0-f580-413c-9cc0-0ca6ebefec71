<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="s_company_team" inherit_id="website.s_company_team">
    <!-- Section -->
    <xpath expr="section" position="attributes">
        <attribute name="class" add="pt64 pb64" remove="pt48 pb48" separator=" "/>
    </xpath>

    <!-- Team #01 -->
    <xpath expr="//div[hasclass('col-lg-6')]//img" position="attributes">
        <attribute name="class" remove="rounded-circle" separator=" "/>
    </xpath>
    <!-- Team #01 - Title -->
    <xpath expr="//div[hasclass('col-lg-6')]//h4" position="replace" mode="inner">
        <font style="background-image: linear-gradient(135deg, var(--o-color-1) 0%, var(--o-color-5) 100%);" class="text-gradient"><PERSON></font>
    </xpath>
    <!-- Team #01 - Work Description -->
    <xpath expr="//div[hasclass('col-lg-6')]//p" position="replace" mode="inner">
        Editor-in-chief
    </xpath>
    <!-- Team #01 - Paragraph -->
    <xpath expr="//div[hasclass('col-lg-6')]//p[2]" position="replace" mode="inner">
        Founder and chief visionary, Tony is the driving force behind the company. He loves to keep his hands full by participating in the development of the magazine and does not hesitate to carry out field surveys.
    </xpath>
    <!-- Team #01 - Img -->
    <xpath expr="//img" position="attributes">
        <attribute name="src">/web_editor/image_shape/website.s_company_team_image_1/web_editor/geometric/geo_square_1.svg</attribute>
        <attribute name="data-shape">web_editor/geometric/geo_square_1</attribute>
        <attribute name="data-original-mimetype">image/jpeg</attribute>
        <attribute name="data-file-name">uiface_1.svg</attribute>
        <attribute name="data-shape-colors">;;;;</attribute>
    </xpath>

    <!-- Team #02 -->
    <xpath expr="//div[hasclass('col-lg-6')][2]//img" position="attributes">
        <attribute name="class" remove="rounded-circle" separator=" "/>
    </xpath>
    <!-- Team #02 - Title -->
    <xpath expr="//div[hasclass('col-lg-6')][2]//h4" position="replace" mode="inner">
        <font style="background-image: linear-gradient(135deg, var(--o-color-1) 0%, var(--o-color-5) 100%);" class="text-gradient">Mich Stark</font>
        <br/>
    </xpath>
    <!-- Team #02 - Work Description -->
    <xpath expr="//div[hasclass('col-lg-6')][2]//p" position="replace" mode="inner">
        Sports Reporter
    </xpath>
    <!-- Team #02 - Paragraph -->
    <xpath expr="//div[hasclass('col-lg-6')][2]//p[2]" position="replace" mode="inner">
        Mich loves taking on challenges. With his multi-year experience as a journalist, he wanted to join a team that puts people at the center of the stories.
    </xpath>
    <!-- Team #02 - Img -->
    <xpath expr="(//img)[2]" position="attributes">
        <attribute name="src">/web_editor/image_shape/website.s_company_team_image_2/web_editor/geometric/geo_square_2.svg</attribute>
        <attribute name="data-shape">web_editor/geometric/geo_square_2</attribute>
        <attribute name="data-original-mimetype">image/jpeg</attribute>
        <attribute name="data-file-name">uiface_2.svg</attribute>
        <attribute name="data-shape-colors">;;;;</attribute>
    </xpath>

    <!-- Team #03 -->
    <xpath expr="//div[hasclass('col-lg-6')][3]//img" position="attributes">
        <attribute name="class" remove="rounded-circle" separator=" "/>
    </xpath>
    <!-- Team #03 - Title -->
    <xpath expr="//div[hasclass('col-lg-6')][3]//h4" position="replace" mode="inner">
        <font style="background-image: linear-gradient(135deg, var(--o-color-2) 0%, var(--o-color-5) 100%);" class="text-gradient">Aline Turner</font>
    </xpath>
    <!-- Team #03 - Work Description -->
    <xpath expr="//div[hasclass('col-lg-6')][3]//p" position="replace" mode="inner">
        Photograph
    </xpath>
    <!-- Team #03 - Paragraph -->
    <xpath expr="//div[hasclass('col-lg-6')][3]//p[2]" position="replace" mode="inner">
        Aline is one of the iconic people in life who can say they love what they do. She likes to capture the moment from another point of view, another perspective.
    </xpath>
    <!-- Team #03 - Img -->
    <xpath expr="(//img)[3]" position="attributes">
        <attribute name="src">/web_editor/image_shape/website.s_company_team_image_3/web_editor/geometric/geo_square_3.svg</attribute>
        <attribute name="data-shape">web_editor/geometric/geo_square_3</attribute>
        <attribute name="data-original-mimetype">image/jpeg</attribute>
        <attribute name="data-file-name">uiface_3.svg</attribute>
        <attribute name="data-shape-colors">;;;;</attribute>
    </xpath>

    <!-- Team #04 -->
    <xpath expr="//div[hasclass('col-lg-6')][4]//img" position="attributes">
        <attribute name="class" remove="rounded-circle" separator=" "/>
    </xpath>
    <!-- Team #04 - Title -->
    <xpath expr="//div[hasclass('col-lg-6')][4]//h4" position="replace" mode="inner">
        <font style="background-image: linear-gradient(135deg, var(--o-color-2) 0%, var(--o-color-5) 100%);" class="text-gradient">Iris Joe</font>
    </xpath>
    <!-- Team #04 - Work Description -->
    <xpath expr="//div[hasclass('col-lg-6')][3]//p" position="replace" mode="inner">
        Political Reporter
    </xpath>
    <!-- Team #04 - Paragraph -->
    <xpath expr="//div[hasclass('col-lg-6')][4]//p[2]" position="replace" mode="inner">
        Iris, with her international experience, helps us easily understand global politics. She is determined to drive success and delivers her professional acumen to bring the magazine to the next level.
    </xpath>
    <!-- Team #04 - Img -->
    <xpath expr="(//img)[4]" position="attributes">
        <attribute name="src">/web_editor/image_shape/website.s_company_team_image_4/web_editor/geometric/geo_square_1.svg</attribute>
        <attribute name="data-shape">web_editor/geometric/geo_square_1</attribute>
        <attribute name="data-original-mimetype">image/jpeg</attribute>
        <attribute name="data-file-name">uiface_4.svg</attribute>
        <attribute name="data-shape-colors">;;;;</attribute>
    </xpath>
</template>

</odoo>
