<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="s_image_text" inherit_id="website.s_image_text">
    <!-- Img -->
    <xpath expr="//img" position="attributes">
        <attribute name="src">/web_editor/image_shape/website.s_image_text_default_image/web_editor/composition/composition_square_1.svg?c1=o-color-1&amp;c2=o-color-3</attribute>
        <attribute name="data-shape">web_editor/composition/composition_square_1</attribute>
        <attribute name="data-original-mimetype">image/jpeg</attribute>
        <attribute name="data-file-name">s_image_text.svg</attribute>
        <attribute name="data-shape-colors">o-color-1;o-color-3;;;</attribute>
    </xpath>
</template>

</odoo>
