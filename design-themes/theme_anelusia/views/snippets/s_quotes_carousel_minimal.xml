<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="s_quotes_carousel_minimal" inherit_id="website.s_quotes_carousel_minimal">
    <!-- Layout -->
    <xpath expr="//div[hasclass('carousel')]" position="attributes">
        <attribute name="class" add="o_cc o_cc5" remove="carousel-dark" separator=" "/>
        <attribute name="data-oe-shape-data">{'shape':'web_editor/Zigs/04','flip':[],'showOnMobile':false,'shapeAnimationSpeed':'0'}</attribute>
    </xpath>
    <xpath expr="//div[hasclass('carousel-inner')]" position="before">
        <div class="o_we_shape o_web_editor_Zigs_04"/>
    </xpath>
    <xpath expr="//div[hasclass('carousel-item')]" position="attributes">
        <attribute name="class" add="pb144" remove="pb80" separator=" "/>
    </xpath>
    <xpath expr="(//div[hasclass('carousel-item')])[2]" position="attributes">
        <attribute name="class" add="pb144" remove="pb80" separator=" "/>
    </xpath>
    <xpath expr="(//div[hasclass('carousel-item')])[3]" position="attributes">
        <attribute name="class" add="pb144" remove="pb80" separator=" "/>
    </xpath>

    <!-- Texts -->
    <xpath expr="//p[hasclass('s_blockquote_quote')]" position="replace" mode="inner">
        " Their designs revamped our image. Fashion-forward and always on trend. "
    </xpath>
    <xpath expr="(//p[hasclass('s_blockquote_quote')])[2]" position="replace" mode="inner">
        " A go-to partner for stylish growth. <br/>Creative, timely, and ahead of the fashion game. "
    </xpath>
    <xpath expr="(//p[hasclass('s_blockquote_quote')])[3]" position="replace" mode="inner">
        " Exceptional quality and service! <br/>They consistently deliver on all our fashion needs. "
    </xpath>
</template>

</odoo>
