<?xml version="1.0" encoding="utf-8"?>
<odoo>

<!-- General customizations -->

<template id="new_page_template_s_comparisons" inherit_id="website.new_page_template_s_comparisons">
    <!-- Shape option -->
    <xpath expr="//section" position="attributes">
        <attribute name="data-oe-shape-data">{"shape":"web_editor/Origins/17","flip":[]}</attribute>
    </xpath>
    <!-- Shape -->
    <xpath expr="//div[hasclass('container')]" position="before">
        <div class="o_we_shape o_web_editor_Origins_17"/>
    </xpath>
</template>

<template id="new_page_template_s_product_catalog" inherit_id="website.new_page_template_s_product_catalog">
    <!-- Remove shape option -->
    <xpath expr="//section" position="attributes">
        <attribute name="data-oe-shape-data"/>
    </xpath>
    <!-- Remove shape -->
    <xpath expr="//div[hasclass('o_we_shape')]" position="replace"/>
</template>

<!-- Snippet customization Basic Pages -->

<!-- Snippet customization About Pages -->

<template id="new_page_template_about_full_1_s_text_block_h1" inherit_id="website.new_page_template_about_full_1_s_text_block_h1">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="pb0" remove="pb40" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_about_full_1_s_text_block_h2" inherit_id="website.new_page_template_about_full_1_s_text_block_h2">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="pb0" remove="pb40" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_about_map_s_text_block_h1" inherit_id="website.new_page_template_about_map_s_text_block_h1">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="pb0" remove="pb0" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_about_mini_s_cover" inherit_id="website.new_page_template_about_mini_s_cover">
    <xpath expr="//section" position="attributes">
        <attribute name="class" remove="pt40 pb40" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_about_full_s_numbers" inherit_id="website.new_page_template_about_full_s_numbers">
    <xpath expr="//section" position="attributes">
        <attribute name="class" remove="o_cc3" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_about_personal_s_numbers" inherit_id="website.new_page_template_about_personal_s_numbers">
    <xpath expr="//section" position="attributes">
        <attribute name="class" remove="o_cc3 o_half_screen_height" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_about_personal_s_text_block_h2" inherit_id="website.new_page_template_about_personal_s_text_block_h2">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="o_cc4" remove="o_cc3" separator=" "/>
    </xpath>
</template>

<!-- Snippet customization Landing Pages -->

<template id="new_page_template_landing_0_s_cover" inherit_id="website.new_page_template_landing_0_s_cover">
    <xpath expr="//section" position="attributes">
        <attribute name="class" remove="pt128 pb128" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_landing_4_s_cover" inherit_id="website.new_page_template_landing_4_s_cover">
    <xpath expr="//section" position="attributes">
        <!-- Added by both theme and new page template -->
        <attribute name="class" add="o_half_screen_height" remove="o_half_screen_height" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_landing_2_s_three_columns" inherit_id="website.new_page_template_landing_2_s_three_columns">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="o_cc2" remove="o_cc1" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_landing_3_s_text_block_h2" inherit_id="website.new_page_template_landing_3_s_text_block_h2">
    <xpath expr="//section" position="attributes">
        <attribute name="class" remove="o_cc2" separator=" "/>
    </xpath>
</template>

<!-- Snippet customization Gallery Pages -->

<!-- Snippet customization Services Pages -->

<!-- Snippet customization Pricing Pages -->

<template id="new_page_template_pricing_s_showcase" inherit_id="website.new_page_template_pricing_s_showcase">
    <!-- Remove shape option -->
    <xpath expr="//section" position="attributes">
        <attribute name="data-oe-shape-data"/>
    </xpath>
    <!-- Remove shape -->
    <xpath expr="//div[hasclass('o_we_shape')]" position="replace"/>
</template>

<template id="new_page_template_pricing_s_text_block_h1" inherit_id="website.new_page_template_pricing_s_text_block_h1">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="pb40" remove="pb0" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_pricing_5_s_text_block_h1" inherit_id="website.new_page_template_pricing_5_s_text_block_h1">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="pb0" remove="pb40" separator=" "/>
    </xpath>
</template>

<!-- Snippet customization Team Pages -->

<template id="new_page_template_team_0_s_text_block_h1" inherit_id="website.new_page_template_team_0_s_text_block_h1">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="pb0" remove="pb40" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_team_1_s_text_block_h1" inherit_id="website.new_page_template_team_1_s_text_block_h1">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="pb0" remove="pb40" separator=" "/>
    </xpath>
</template>

</odoo>
