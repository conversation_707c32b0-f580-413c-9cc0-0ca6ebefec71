# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_avantgarde
# 
# Translators:
# Yedigen, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>_Odo<PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-07 13:30+0000\n"
"PO-Revision-Date: 2024-09-25 18:03+0000\n"
"Last-Translator: <PERSON><PERSON>_<PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_pricelist_boxed
msgid "$1,500.00"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_pricelist_boxed
msgid "$1,800.00"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_pricelist_boxed
msgid "$2,000.00"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_pricelist_boxed
msgid "$250.00"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_pricelist_boxed
msgid "$500.00"
msgstr "$500.00"

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_pricelist_boxed
msgid "$700.00"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_timeline
msgid "13/06/2024"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_timeline
msgid "23/10/2015"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_timeline
msgid "25/06/2019"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_timeline
msgid "25/10/2019"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_quadrant
msgid ""
"250 Executive Park Blvd, Suite 3400<br/>San Francisco CA 94134<br/>United "
"States"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_call_to_action
msgid "<b>Since 1992</b> creating around the world."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_empowerment
msgid ""
"<br/>Crafting spaces where creativity meets functionality,<br/>one design at"
" a time.<br/><br/>"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_text_cover
msgid ""
"<br/>Present your architectural projects online with a user-friendly "
"platform that streamlines all the steps, from setup to project showcase, "
"making it easy to share your vision with clients.<br/>"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_big_number
msgid ""
"<font class=\"text-gradient\" style=\"background-image: linear-gradient(0deg, rgb(56, 60, 214) 0%, rgb(255, 255, 255) 65%);\">\n"
"            50+\n"
"        </font>"
msgstr ""
"<font class=\"text-gradient\" style=\"background-image: linear-gradient(0deg, rgb(56, 60, 214) 0%, rgb(255, 255, 255) 65%);\">\n"
"            50'den fazla\n"
"        </font>"

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_empowerment
msgid ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/>  Award-"
"Winning Designs"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_features_wall
msgid "<i class=\"fa fa-ticket\"/> Book your tickets"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_text_image
msgid ""
"<span class=\"text-break\" style=\"font-size: 62px; font-weight: "
"bolder;\">Our R&amp;D Approach</span>"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_three_columns
msgid "<span style=\"font-weight: bolder;\">Collaboration</span>"
msgstr "<span style=\"font-weight: bolder;\">İş birliği</span>"

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_three_columns
msgid "<span style=\"font-weight: bolder;\">Excellence</span>"
msgstr "<span style=\"font-weight: bolder;\">Mükemmeliyet</span>"

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_three_columns
msgid "<span style=\"font-weight: bolder;\">Sustainability</span>"
msgstr "<span style=\"font-weight: bolder;\">Sürdürülebilirlik</span>"

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_image_title
msgid "A Deep Dive into Boldness and Innovation"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_pricelist_boxed
msgid ""
"A relaxed portrait session for families, capturing genuine moments and "
"smiles in a comfortable setting."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_carousel
msgid "ARTWORK"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_timeline
msgid "Arrival of Aline Turner at the head of the Gallery"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_sidegrid
msgid "Art gallery"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_pricelist_boxed
msgid ""
"Artistic portrait photography with creative lighting and backgrounds, ideal "
"for personal projects or portfolio work."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_sidegrid
msgid "Avantgarde"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_quadrant
msgid "Avantgarde Gallery"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_empowerment
msgid "Book an appointment"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_key_images
msgid "Breaking the mold with bold, daring concepts"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_pricelist_boxed
msgid ""
"Capture your moments with our range of professional photography services. "
"Expertly crafted to make every memory unforgettable."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_sidegrid
msgid "Come visit us from Tuesday 9 AM to Saturday 6 PM"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_pricelist_boxed
msgid ""
"Comprehensive wedding day photography service capturing every special "
"moment, from the ceremony to the reception."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_pricelist_boxed
msgid "Concert Photography"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_key_benefits
msgid "Continuous Support"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_pricelist_boxed
msgid "Corporate Event Package"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_empowerment
msgid "Crafting spaces<br/>for everyday life"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_key_images
msgid "Crafting the future, one unique idea at a time"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_pricelist_boxed
msgid "Creative Portrait Session"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_carousel_intro
msgid "Defining the future of creativity"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_picture
msgid "Design Methodology"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_text_cover
msgid "Design Online. <br/>Effortlessly."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_striped_center_top
msgid "Design Your World with Creativity"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_shape_image
msgid "Designing the future"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_freegrid
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_striped_center_top
msgid "Discover More"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_features_wall
msgid ""
"Discover a variety of animals from around the world, beautifully captured in"
" these detailed, scientific paintings."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_images_mosaic
msgid "Discover our cutting-edge solutions pushing creative boundaries."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_card_offset
msgid "Discover the future of design today."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_timeline
msgid "Discover the journey that made us the most popular gallery in town."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_cta_box
msgid "Discover what innovation<br/>looks like !"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_freegrid
msgid ""
"Dive into the world of design and fine art with our collection of forward-"
"thinking pieces. Discover how creativity drives change in the art world "
"through our exclusive exhibitions and publications."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_features_wall
msgid "Diverse Wildlife"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_pricelist_boxed
msgid ""
"Dynamic concert photography capturing the energy of live performances with "
"high-quality images of artists and audience."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_carousel_intro
msgid ""
"Embrace cutting-edge ideas that challenge norms and redefine the boundaries "
"of modern creativity, every single day."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_wavy_grid
msgid "Excellence and Innovation"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_key_images
msgid "Experience the power of original thought"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_wavy_grid
msgid "Expertise and Vision"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_carousel_intro
msgid ""
"Explore more and discover innovative projects that align with your avant-"
"garde vision, crafted for impact and disruption."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_cta_box
msgid "Explore our large collection of disruptive products<br/><br/>"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_shape_image
msgid "Explore our projects"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_pricelist_boxed
msgid "Family Portrait Session"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_timeline
msgid "First Culture Award ®"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_features_wall
msgid "For all the family"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_features_wall
msgid "From 12th September to 20th February"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_quadrant
msgid "Get Directions"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_pricelist_boxed
msgid ""
"High-quality headshots perfect for business profiles, LinkedIn, or personal "
"branding, taken in our studio or on location."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_card_offset
msgid "Innovative Design Meets Fine Art"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_freegrid
msgid "Innovative Design and Art That Inspire Change"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_carousel_intro
msgid "Innovative concepts tailored for you"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_cta_box
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_text_cover
msgid "Learn more"
msgstr "Daha fazla bilgi edin"

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_text_image
msgid ""
"Making innovative design requires us to push the limits of technological "
"possibilities. As a group of in-house specialists, we develop and implement "
"computational workflows and new technologies."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_pricelist_boxed
msgid "Our Photography Services"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_empowerment
msgid "Our Projects"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_shape_image
msgid ""
"Our architectural vision shapes spaces that inspire. We blend innovation "
"with sustainability to create designs that stand the test of time. Let us "
"build your dream, one blueprint at a time."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_picture
msgid ""
"Our design methodology is rooted in our core values.<br/>\n"
"        We combine a wide spectrum of technologies with a good portion of creativity."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_timeline
msgid "Our history"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_key_benefits
msgid ""
"Our team is here to assist you at every stage of your project, ensuring a "
"smooth and stress-free process for your project."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_pricelist_boxed
msgid "Professional Headshots"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_pricelist_boxed
msgid ""
"Professional coverage for corporate events, including conferences, award "
"ceremonies, and networking events."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_timeline
msgid "Purchase of the building"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_key_images
msgid "Pushing Boundaries, Redefining Possibilities"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_wavy_grid
msgid "Revolutionary Concepts"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_empowerment
msgid "See Our Achievements   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_card_offset
msgid ""
"Stay ahead of the curve with our insights on design and fine art. Explore "
"the intersection of creativity and innovation through our galleries, shows, "
"and digital media."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_striped_center_top
msgid ""
"Stay ahead with cutting-edge design trends, fine art, and creative "
"inspirations that shape the future of galleries, shows, and digital media."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_features_wall
msgid ""
"Step back in time with classic, hand-painted illustrations that showcase the"
" charm of old-world artistry."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_three_columns
msgid ""
"Sustainability is at the heart of our design approach. We audit projects "
"against global standards as well as our own, more comprehensive, "
"responsibility framework."
msgstr ""
"Sürdürülebilirlik, tasarım yaklaşımımızın merkezinde yer almaktadır. "
"Projeleri, küresel standartların yanı sıra kendi daha kapsamlı sorumluluk "
"çerçevemize göre denetliyoruz."

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_wavy_grid
msgid "Sustainable Innovation"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_key_benefits
msgid "Tailored Pricing"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_key_benefits
msgid "Tax-Free Services"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_features_wall
msgid "The Boston Public Library Collection"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_features_wall
msgid ""
"The Boston Public Library's exhibit features lovely bird and flower "
"paintings, bringing together nature, art, and science."
msgstr ""

#. module: theme_avantgarde
#: model:ir.model,name:theme_avantgarde.model_theme_utils
msgid "Theme Utils"
msgstr "Tema Araçları"

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_features_wall
msgid ""
"This exhibition offers something for everyone, with fascinating artwork that"
" will captivate both kids and adults alike."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_text_image
msgid ""
"Through a mixture of project-based work and standalone research, we make "
"efficient projects, adaptable in the face of change."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_carousel_intro
msgid "Transform your approach with our forward-thinking strategies."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_image_title
msgid ""
"Transform your perspective with our groundbreaking collection, where "
"unconventional meets visionary. Elevate your style with pieces that "
"challenge norms and redefine creativity effortlessly."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_key_images
msgid "Unleash Your Imagination with Avant-Garde Designs"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_carousel_intro
msgid "Unleash your vision"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_images_mosaic
msgid "Unveiling Our Bold New Innovations"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_features_wall
msgid "Vintage Style"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_cover
msgid "We are Avantgarde."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_picture
msgid "We carry out an interdisciplinary design process."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_three_columns
msgid ""
"We conduct state-of-the-art research and development to solve complex design"
" challenges, taking the latest advances out of the lab and into the hands of"
" architects and engineers."
msgstr ""
"Karmaşık tasarım sorunlarını çözmek için en son teknolojiye sahip araştırma "
"ve geliştirme çalışmaları yürütüyoruz. Laboratuvardaki en son gelişmeleri "
"mimarların ve mühendislerin kullanımına sunuyoruz."

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_cover
msgid ""
"We create architecture, landscapes, interiors, product design and graphic design.<br/>\n"
"        Our methods allow us to explore a future that is equitable, data-driven, and green."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_wavy_grid
msgid ""
"We design groundbreaking solutions that push the boundaries of creativity. "
"Our team collaborates with you to bring visionary ideas to life from start "
"to finish."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_key_benefits
msgid ""
"We maximize value by offering tax-efficient design services, helping you "
"achieve your vision while keeping your project financially streamlined."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_wavy_grid
msgid ""
"We offer cutting-edge innovations to tackle modern challenges. Embracing the"
" latest advancements, we help you redefine success."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_timeline
msgid "We open the cafeteria!"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_call_to_action
msgid "We partner with ambitious clients. We’d love to hear from you."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_key_benefits
msgid ""
"We provide custom pricing based on the unique needs of your project, "
"ensuring you receive exceptional design services that fit your budget."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_three_columns
msgid ""
"We thrive on rich collaborations to push our thinking. A continuous state of"
" reinvention, driven by our partners in the process, is essential to our "
"work."
msgstr ""
"Zengin iş birlikleri sayesinde düşünce yapımızı geliştiriyoruz. Süreçteki "
"partnerlerimiz tarafından yönlendirilen sürekli bir yenilenme hali, "
"çalışmalarımız için çok önemlidir."

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_pricelist_boxed
msgid "Wedding Day Coverage"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_wavy_grid
msgid "What we envision for our clients"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_striped_center_top
msgid "Where creativity knows no bounds"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_key_images
msgid "Where innovation meets artistry"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_wavy_grid
msgid ""
"With profound expertise and forward-thinking strategies, we deliver insights"
" that keep you ahead of the curve."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_wavy_grid
msgid ""
"Your satisfaction fuels our drive. Our support team is ever-ready to guide "
"you, ensuring a seamless and impactful experience."
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_big_number
msgid "projects shipped"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_sidegrid
msgid "✴ San Francisco"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_masonry_block_alternation_text_image_text_template
msgid "✽<br/>Coming up in March<br/>✽"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_masonry_block_alternation_text_image_text_template
msgid "✽<br/>Our very new collection<br/>✽"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_pricelist_boxed
msgid "✽  Event Photography"
msgstr ""

#. module: theme_avantgarde
#: model_terms:theme.ir.ui.view,arch:theme_avantgarde.s_pricelist_boxed
msgid "✽  Portrait Photography"
msgstr ""
