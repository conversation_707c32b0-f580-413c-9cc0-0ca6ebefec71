<?xml version="1.0" encoding="utf-8"?>
<odoo>

<!-- General customizations -->

<template id="new_page_template_s_call_to_action" inherit_id="website.new_page_template_s_call_to_action">
    <xpath expr="//section" position="attributes">
        <attribute name="data-oe-shape-data">{"shape":"web_editor/Origins/14_001"}</attribute>
    </xpath>
    <xpath expr="//div[hasclass('container')]" position="before">
        <div class="o_we_shape o_web_editor_Origins_14_001"/>
    </xpath>
</template>

<template id="new_page_template_s_call_to_action_about" inherit_id="website.new_page_template_s_call_to_action_about">
    <xpath expr="//section" position="attributes">
        <attribute name="data-oe-shape-data">{"shape":"web_editor/Origins/14_001"}</attribute>
    </xpath>
    <xpath expr="//div[hasclass('container')]" position="before">
        <div class="o_we_shape o_web_editor_Origins_14_001"/>
    </xpath>
</template>

<template id="new_page_template_s_call_to_action_menu" inherit_id="website.new_page_template_s_call_to_action_menu">
    <xpath expr="//section" position="attributes">
        <attribute name="data-oe-shape-data">{"shape":"web_editor/Origins/14_001"}</attribute>
    </xpath>
    <xpath expr="//div[hasclass('container')]" position="before">
        <div class="o_we_shape o_web_editor_Origins_14_001"/>
    </xpath>
</template>

<!-- Snippet customization Basic Pages -->

<!-- Snippet customization About Pages -->

<template id="new_page_template_about_s_cover" inherit_id="website.new_page_template_about_s_cover">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="o_half_screen_height" remove="o_full_screen_height" separator=" "/>
    </xpath>
</template>

<!-- Snippet customization Landing Pages -->

<template id="new_page_template_landing_2_s_cover" inherit_id="website.new_page_template_landing_2_s_cover">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="o_half_screen_height" remove="o_full_screen_height" separator=" "/>
    </xpath>
</template>

<!-- Snippet customization Gallery Pages -->

<template id="new_page_template_gallery_s_cover" inherit_id="website.new_page_template_gallery_s_cover">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="o_half_screen_height" remove="o_full_screen_height" separator=" "/>
    </xpath>
</template>

<!-- Snippet customization Services Pages -->

<template id="new_page_template_services_1_s_text_block_h1" inherit_id="website.new_page_template_services_1_s_text_block_h1">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="pb40" remove="pb0" separator=" "/>
    </xpath>
</template>

<!-- Snippet customization Pricing Pages -->

<template id="new_page_template_pricing_s_cover" inherit_id="website.new_page_template_pricing_s_cover">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="o_half_screen_height" remove="o_full_screen_height" separator=" "/>
    </xpath>
</template>

<!-- Snippet customization Team Pages -->

<template id="new_page_template_team_s_text_block_h1" inherit_id="website.new_page_template_team_s_text_block_h1">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="o_cc2" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_team_1_s_text_block_h1" inherit_id="website.new_page_template_team_1_s_text_block_h1">
    <xpath expr="//section" position="attributes">
        <attribute name="class" add="pb0" remove="pb40 o_cc2" separator=" "/>
    </xpath>
</template>

<template id="new_page_template_team_2_s_text_block_h1" inherit_id="website.new_page_template_team_2_s_text_block_h1">
    <xpath expr="//section" position="attributes">
        <attribute name="class" remove="o_cc2" separator=" "/>
    </xpath>
</template>

</odoo>
