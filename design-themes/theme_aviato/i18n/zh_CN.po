# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_aviato
# 
# Translators:
# Wil Odoo, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-07 13:29+0000\n"
"PO-Revision-Date: 2024-09-25 18:03+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel_minimal
msgid ""
"\" A trusted partner for adventure. <br/>Professional, reliable, and always "
"delivering unique experiences. \""
msgstr "“值得信赖的探险伙伴。<br/>专业、可靠，始终提供独一无二的体验。”"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel_minimal
msgid ""
"\" Exceptional service and planning! <br/>They made every trip truly "
"memorable. \""
msgstr "“卓越的服务和规划！<br/>他们让每次旅行都真正难忘。”"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel_minimal
msgid ""
"\" They crafted unforgettable journeys. Seamless and perfectly tailored "
"trips. \""
msgstr "“他们精心打造了难忘的旅程。无缝衔接、完美定制的旅行。”"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_benefits
msgid "24/7 Travel Support"
msgstr "全天候旅行支持"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cover
msgid ""
"<b>A travel agent helps you to plan your <br/> trip from start to finish.</b>\n"
"        <br/>"
msgstr ""
"<b>旅行社帮助您从头到尾规划您的<br/> 旅行。</b>\n"
"        <br/>"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_text_cover
msgid ""
"<br/>Create and manage travel itineraries online with a user-friendly "
"platform that simplifies all the steps, from setup to itinerary management, "
"making it easy to offer seamless travel experiences to your clients.<br/>"
msgstr "<br/>通过用户友好型平台在线创建和管理旅行行程，简化从设置到行程管理的所有步骤，轻松为客户提供无缝旅行体验。<br/>"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_banner
msgid ""
"<br/>Enjoy breathtaking views of nature. Relax and embrace your "
"dreams.<br/><br/>"
msgstr "<br/>欣赏大自然的壮丽景色。放松身心，拥抱梦想。<br/><br/>"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_empowerment
msgid ""
"<br/>Your gateway to unforgettable adventures and dream "
"destinations.<br/><br/>"
msgstr "<br/>您通往难忘探险和梦想目的地的大门。<br/><br/>"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_big_number
msgid ""
"<font class=\"text-gradient\" style=\"background-image: linear-"
"gradient(0deg, rgb(244, 168, 26) 0%, rgb(222, 222, 222) 58%);\">100+</font>"
msgstr ""
"<font class=\"text-gradient\" style=\"background-image: linear-"
"gradient(0deg, rgb(244, 168, 26) 0%, rgb(222, 222, 222) 58%);\">100+</font>"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_empowerment
msgid ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" "
"role=\"img\"/>  Exclusive Travel Deals"
msgstr "<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/>  独家旅游优惠"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_image_title
msgid "A Deep Dive into Adventure and Discovery"
msgstr "深入探索冒险和发现"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid ""
"A heady mix of haunting ruins, awe-inspiring art and vibrant street life, "
"Italy’s hot-blooded capital is one of the world’s most romantic and "
"charismatic cities."
msgstr "魂牵梦萦的废墟、令人惊叹的艺术和充满活力的街头生活，意大利的热血之都是世界上最浪漫、最具魅力的城市之一。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_striped_top
msgid "A travel agent helps you to plan your trip from start to finish."
msgstr "旅行社帮助您从头到尾规划您的旅行。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_striped_center_top
msgid "Adventure Awaits"
msgstr "探险在即"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_company_team
msgid ""
"Aline designs bespoke travel itineraries, collaborating with a global "
"network of experts to deliver unique experiences tailored to each client's "
"needs."
msgstr "Aline 设计定制旅行行程，与全球专家网络合作，根据每位客户的需求提供独特的体验。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_banner
msgid ""
"An unforgettable experience! Every destination was stunning, and the service"
" was exceptional."
msgstr "一次难忘的经历！每个目的地都令人惊叹，服务也非常出色。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_striped_center_top
msgid "Book Your Trip"
msgstr "预订行程"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid "Book now"
msgstr "现在预订"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cta_box
msgid "Book your next holiday<br/>in a minute !"
msgstr "立即预订您的下一个假期！<br/>"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_masonry_block_default_template
msgid "Brazil"
msgstr "巴西"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_popup
msgid "Check out now and get $200 off your first booking."
msgstr "立即结账，首次预订即可享受 200 美元优惠。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_images
msgid "Discover Your Next Adventure with Us"
msgstr "与我们一起探索您的下一次旅程"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cta_box
msgid "Discover how we can help you for your next trip ! <br/><br/>"
msgstr "了解我们如何为您的下一次旅行提供帮助！<br/><br/>"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_freegrid
msgid ""
"Discover the beauty of the world through our curated travel experiences. "
"From adventurous excursions to relaxing tours, we tailor your journeys to "
"meet your wildest travel dreams."
msgstr "通过我们精心策划的旅行体验，发现世界之美。从探险远足到放松之旅，我们为您量身定制旅行，满足您最狂野的旅行梦想。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quadrant
msgid ""
"Discover the world with our bespoke travel services. From plane tours to "
"unique excursions, we create experiences that leave a lasting "
"impression.<br/><br/> Let us be your guide to extraordinary adventures."
msgstr "通过我们量身定制的旅行服务发现世界。从飞机之旅到独特的探险，我们创造令人难忘的体验。<br/><br/> 让我们成为您非凡冒险的向导。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_unveil
msgid ""
"Discover the world with our tailored travel services and exclusive deals."
msgstr "通过我们量身定制的旅行服务和独家优惠探索世界。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_carousel_intro
msgid "Discover your next adventure"
msgstr "探索您的下一次旅程"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_striped_top
msgid "Discovering the Ultimate Travel Experience"
msgstr "探索终极旅行体验"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_card_offset
msgid ""
"Embark on adventures that leave a lasting impression. Our travel agency "
"offers tailored excursions and tours that connect you with the world’s most "
"breathtaking destinations."
msgstr "踏上冒险之旅，留下深刻印象。我们的旅行社为您提供量身定制的短途旅行和观光线路，让您与世界上最令人叹为观止的目的地紧密相连。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_striped_center_top
msgid ""
"Embark on unforgettable journeys with our tailored travel experiences, "
"designed to explore the world’s most breathtaking destinations."
msgstr "我们为您量身定制的旅行体验，旨在探索世界上最令人叹为观止的旅游胜地，让您踏上难忘的旅程。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cards_grid
msgid ""
"Enjoy the peace and quiet of Finland's forests and lakes. Rest and relax in "
"one of our best partnered resorts."
msgstr "享受芬兰森林和湖泊的宁静。在我们绝佳合作度假村中休息放松。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid ""
"Everything was very well organised. Thank you very much for everything!"
msgstr "一切都安排得井井有条。非常感谢你们所做的一切！"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_benefits
msgid "Exclusive Experiences"
msgstr "独家体验"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_features_wall
msgid ""
"Experience personalized itineraries crafted to suit your interests, "
"preferences, and budget for the perfect vacation."
msgstr "体验根据您的兴趣、喜好和预算精心设计的个性化行程，享受完美假期。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_freegrid
msgid "Experience the World with Our Unique Travel Plans"
msgstr "用我们独特的旅行计划体验世界"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_carousel_intro
msgid ""
"Experience the beauty and culture of unique destinations, curated to bring "
"out the traveler in you, every single day."
msgstr "体验独特目的地的美丽和文化，精心策划，让您每一天都充满惊喜。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_sidegrid
msgid "Experience the real travel experience"
msgstr "感受最真实的旅行体验"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_wavy_grid
msgid "Expertise and Adventure"
msgstr "专业知识与探险"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_showcase
msgid "Expertly Curated Trips"
msgstr "专业策划的旅行"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_empowerment
msgid "Explore Destinations"
msgstr "探索目的地"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_unveil
msgid "Explore New Horizons"
msgstr "探索新视野"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_banner
msgid "Explore Now"
msgstr "立即探索"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_picture
msgid "Explore World's Hidden Gems"
msgstr "探索世界上隐藏的瑰宝"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_images
msgid "Explore exotic locations with expert guides"
msgstr "在专家导游的带领下探索异国风情"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_carousel_intro
msgid ""
"Explore more and find unique destinations that align with your travel "
"dreams, crafted for unforgettable experiences."
msgstr "探索更多，找到符合您旅行梦想的独特目的地，精心打造难忘体验。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_images_mosaic
msgid "Explore our latest destinations and unique travel experiences."
msgstr "探索我们最新的目的地和独特的旅行体验。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_carousel_intro
msgid "Explore the world"
msgstr "探索世界"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_card_offset
msgid "Explore the world with us today."
msgstr "今天就和我们一起探索世界。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_empowerment
msgid "Explore with<br/>GlobeTrek Travel"
msgstr "与 <br/>GlobeTrek 一起探索旅行"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_banner
msgid "Explored Peru in 2024"
msgstr "2024 年探索秘鲁"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_features_wall
msgid "Flight &amp; Hotel Deals"
msgstr "航班及酒店优惠"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_features
msgid "Flight Booking"
msgstr "航班预订"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_masonry_block_default_template
msgid "France"
msgstr "法国"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_masonry_block_default_template
msgid "From $119"
msgstr "$119 起"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_masonry_block_default_template
msgid "From $149"
msgstr "$149 起"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_masonry_block_default_template
msgid "From $299"
msgstr "$299 起"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_masonry_block_default_template
msgid "From $349"
msgstr "$349 起"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_masonry_block_default_template
msgid "From $699"
msgstr "$699 起"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid "From France"
msgstr "法国出发"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid "From Italy"
msgstr "意大利出发"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid "From United Kingdom"
msgstr "英国出发"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_showcase
msgid ""
"From booking to departure, we handle all the details so you can focus on "
"enjoying your adventure."
msgstr "从预订到出发，我们处理所有细节，让您可以专注于享受您的冒险。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_benefits
msgid ""
"Gain access to curated experiences and hidden gems that go beyond the usual "
"tourist paths, making your journey truly special."
msgstr "您可以获得精心策划的体验和隐藏的瑰宝，这些体验和瑰宝超越了常规的旅游路线，让您的旅程真正与众不同。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_text_cover
msgid "Get started"
msgstr "开始"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_masonry_block_default_template
msgid "Greece"
msgstr "希腊"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_features_wall
msgid "Group Tours &amp; Adventures"
msgstr "团体导赏及探险"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cards_grid
msgid "Hike in Switzerland"
msgstr "瑞士徒步旅行"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_features
msgid "Hotel Booking"
msgstr "酒店预订"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_company_team
msgid ""
"Iris is responsible for creating cost-effective travel packages, ensuring "
"clients can enjoy their adventures without compromising on quality."
msgstr "Iris 负责打造具有成本效益的旅行套餐，确保客户能够在不影响质量的情况下享受探险乐趣。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid ""
"It would take too long to describe day by day the sights, sounds, and people"
" that we came across. Ireland was fantastic."
msgstr "如果要逐日描述我们所看到的景象、听到的声音和遇到的人，可以讲上一天一夜。爱尔兰太棒了。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_masonry_block_default_template
msgid "Italy"
msgstr "意大利"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_image_text
msgid "Itinerary Planning"
msgstr "行程规划"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid "Jacques and Marie"
msgstr "Jacques 和 Marie"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_features_wall
msgid ""
"Join expertly guided group tours for exciting cultural, historical, and "
"nature-filled experiences in top destinations worldwide."
msgstr "参加由专业导游带领的旅行团，在全球顶级目的地体验激动人心的文化、历史和自然风光。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_shape_image
msgid "Join the Experience"
msgstr "参与体验"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_images
msgid "Journey to Unforgettable Destinations"
msgstr "难忘的目的地之旅"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cards_grid
msgid ""
"Known for its stunning beaches Thailand is a Southeast Asian country, enjoy "
"its lush forests and vibrant culture."
msgstr "泰国是一个东南亚国家，以其迷人的海滩而闻名，您可尽情享受其郁郁葱葱的森林和充满活力的文化。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_banner
msgid "Let's <br/>travel the world"
msgstr "一起<br/>遨遊世界"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid "Linda and Paulo"
msgstr "Linda 和 Paulo"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_images
msgid "Luxury, comfort, and adventure await you"
msgstr "豪华、舒适、探险等您发掘"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cards_grid
msgid ""
"Marbella is a city on the southern coast of Spain known for its luxurious "
"lifestyle, beautiful beaches, and vibrant nightlife."
msgstr "马贝拉是西班牙南部海岸的一座城市，以奢华的生活方式、美丽的海滩和充满活力的夜生活而闻名。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_company_team
msgid "Meet our travel agents"
msgstr "认识我们的旅行社"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_company_team
msgid ""
"Mich ensures the smooth execution of all travel plans, leveraging his "
"industry expertise to guarantee a seamless client experience."
msgstr "Mich 确保所有旅行计划的顺利执行，利用他的行业专业知识保证客户体验的完美无缺。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_picture
msgid "Old Harry Rocks, Isle of Purbeck"
msgstr "老哈里岩石，波贝克岛"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_benefits
msgid ""
"Our dedicated team is here to assist you at any time, from the moment you "
"book until you return home safely."
msgstr "我们的专业团队随时为您服务，从您预订的那一刻起，直到您安全回家。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cta_box
msgid "Our services"
msgstr "我们的服务"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_text_image
msgid ""
"Our team travels widely the globe to bring our clients first-hand knowledge "
"of upcoming destinations, new \"hot\" hotels and ships, local restaurants, "
"neighborhood hangouts, bars, pubs, the best. beaches and places to visit and"
" not to see."
msgstr ""
"我们的团队走遍全球，为客户提供第一手信息，包括即将到来的目的地、新的 \"热门 "
"\"酒店和船只、当地餐馆、邻里聚会场所、酒吧、最好的海滩以及值得一去和不值得一去的地方。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_image_text
msgid ""
"Our vision is to make travel planning stress free and make travel more fun. "
"No matter the type of trip you have in mind, we have the resources and "
"expertise necessary to create the perfect trip"
msgstr "我们的愿景是让旅行计划毫无压力，让旅行更加有趣。无论您想进行哪种类型的旅行，我们都能提供必要的资源和专业知识，为您打造完美旅行"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid "Paris"
msgstr "帕里斯"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid ""
"Paris' monument-lined boulevards, museums, classical bistros and boutiques "
"are enhanced by a new wave of multimedia galleries, creative wine bars.."
msgstr "巴黎的古迹林荫大道、博物馆、古典小酒馆和精品店在新一波多媒体画廊、创意葡萄酒吧的映衬下更加绚丽多彩。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_benefits
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_showcase
msgid "Personalized Itineraries"
msgstr "个性化行程"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_text_cover
msgid "Plan Trips. <br/>Effortlessly."
msgstr "计划旅行。<br/>轻松搞定。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_empowerment
msgid "Plan Your Trip"
msgstr "计划您的行程"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cover
msgid "Plan your trip with us"
msgstr "与我们一起计划行程"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid "Prague"
msgstr "布拉格"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid ""
"Prague is the equal of Paris in terms of beauty. Its history goes back a "
"millennium. And the beer? The best in Europe. Prague is the perfect city to "
"walk around."
msgstr "布拉格的美可与巴黎媲美。它的历史可以追溯到千年以前。啤酒呢？欧洲最好的啤酒。布拉格是一座适合漫步的城市。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_wavy_grid
msgid "Quality and Experience"
msgstr "品质与卓越"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_carousel_intro
msgid "Refresh your travel plans with our exclusive packages and expert tips."
msgstr "利用我们的独家套餐和专家提示，刷新您的旅行计划。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cards_grid
msgid "Relax in Marbella"
msgstr "在马贝拉放松身心"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cards_grid
msgid "Rest in Finland"
msgstr "在芬兰放松休息"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_masonry_block_default_template
msgid "Romania"
msgstr "罗马尼亚"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid "Rome"
msgstr "罗马"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid "Rose and Peter"
msgstr "Rose 和 Peter"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_features_wall
msgid ""
"Save big on exclusive flight and hotel offers, ensuring comfortable and "
"affordable accommodations throughout your journey."
msgstr "享受独家航班和酒店优惠，节省大笔费用，确保您在整个旅程中享受舒适且实惠的住宿。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_showcase
msgid "Seamless Travel Experience"
msgstr "流畅旅行体验"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_picture
msgid ""
"Set off on an unforgettable adventure to the world's most breathtaking "
"locations, thoughtfully curated just for you."
msgstr "启程前往世界最令人叹为观止的地点，为您精心策划的一次难忘的冒险。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quadrant
msgid "Start Exploring"
msgstr "开始探索"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_freegrid
msgid "Start Your Journey"
msgstr "开始您的旅程"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_wavy_grid
msgid "Sustainable Travel"
msgstr "可持续旅游"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cards_grid
msgid ""
"Switzerland is a mountainous country known its hiking trails. Feel the fresh"
" air and enjoy the beautiful scenery of the Swiss Alps."
msgstr "瑞士是一个多山的国家，以其远足小径而闻名。呼吸新鲜空气，欣赏瑞士阿尔卑斯山的美丽景色。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_images
msgid "Tailor-made experiences for every traveler"
msgstr "为每位游客量身定制体验"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_wavy_grid
msgid "Tailored Journeys"
msgstr "量身定制的旅程"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_features_wall
msgid "Tailored Travel Packages"
msgstr "定制旅游套餐"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cards_grid
msgid "Tan in Thailand"
msgstr "在泰国晒肤"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid ""
"The parts of the country we visited were beautiful, people were friendly, "
"everything we ate was delicious, and there were many fun things to see and "
"do."
msgstr "我们所到之处风景优美，人们热情友好，吃的东西都很美味，还有很多有趣的事情可看、可做。"

#. module: theme_aviato
#: model:ir.model,name:theme_aviato.model_theme_utils
msgid "Theme Utils"
msgstr "主题用途"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_features
msgid "Ticket Booking"
msgstr "订票"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_company_team
msgid ""
"Tony is dedicated to crafting personalized travel experiences, ensuring "
"every client's journey is tailored to their unique preferences."
msgstr "Tony 致力于打造个性化的旅行体验，确保每位客户的旅程都根据他们的独特喜好量身定制。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_title
msgid "Top Destinations"
msgstr "首要目的"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_image_title
msgid ""
"Transform your journey with our exclusive travel experiences, where "
"exploration meets comfort. Elevate your adventures with trips that blend "
"excitement and relaxation seamlessly."
msgstr "我们独一无二的旅行体验让您的旅程焕然一新，让探索与舒适相得益彰。将刺激与放松完美融合的旅行，让您的冒险之旅更上一层楼。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quadrant
msgid "Travel Awaits"
msgstr "旅行在即"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_popup
msgid "Travel Now"
msgstr "立即旅行"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_shape_image
msgid "Travel services"
msgstr "旅行服务"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cover
msgid "Travel to any corner <br/>of the world"
msgstr "前往世界任何角落<br/>"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_image_hexagonal
msgid "Travel to any corner of the world"
msgstr "去世界任何一个角落旅行"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_sidegrid
msgid ""
"Traveling lets you explore new cultures and places while discovering more "
"about yourself. It's a chance to connect with others and create lasting "
"memories."
msgstr "旅行让您在探索新文化和新地方的同时发现更多自我。这是一个与他人建立联系、创造永恒回忆的机会。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_carousel_intro
msgid "Unforgettable journeys tailored for you"
msgstr "为您量身定制的难忘旅程"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cards_grid
msgid "Unravel your next adventure"
msgstr "探索您的下一次旅程"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_images_mosaic
msgid "Unveiling Our Travel Adventures"
msgstr "揭开我们旅途的神秘面纱"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_empowerment
msgid "View Our Offers   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"
msgstr "查看我们的优惠 <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_benefits
msgid ""
"We craft custom travel plans that suit your interests and needs, ensuring "
"every trip is unique and unforgettable."
msgstr "我们根据您的兴趣和需求定制旅行计划，确保每次旅行都独一无二、令人难忘。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_wavy_grid
msgid ""
"We craft personalized itineraries to match your travel dreams. Our team "
"works with you to ensure a seamless adventure from start to finish."
msgstr "我们根据您的旅行梦想精心设计个性化行程。我们的团队与您通力合作，确保您的探险之旅从始至终都能顺利无阻。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_shape_image
msgid ""
"We offer customized travel packages tailored to your preferences. From "
"booking flights and accommodations to organizing tours, our goal is to "
"provide a seamless and enjoyable travel experience."
msgstr "我们根据您的喜好为您量身定制旅行套餐。从预订机票和住宿到组织旅游，我们的目标是为您提供流畅且愉快的旅行体验。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_wavy_grid
msgid ""
"We provide cutting-edge travel solutions to explore the world. Leveraging "
"the latest trends, we help you achieve your travel goals."
msgstr "我们提供前沿的旅行解决方案，助您探索世界。我们利用最新趋势，帮助您实现旅行目标。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_showcase
msgid ""
"We select only the best destinations, ensuring your travel experience is "
"nothing short of extraordinary."
msgstr "我们只选择最好的目的地，确保您的旅行体验无与伦比。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_text_image
msgid "What we can do, <b>for you</b>"
msgstr "我们能<b>为您</b>做什么"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_wavy_grid
msgid "What we deliver to our travelers"
msgstr "多样化读者服务"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_showcase
msgid "Why Choose Us"
msgstr "为什么选择我们"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_popup
msgid "Win 200$"
msgstr "赢取 200 美元"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_wavy_grid
msgid ""
"With vast experience and deep travel knowledge, we offer insights and "
"destinations that keep you ahead of the curve."
msgstr "凭借丰富的经验和深厚的旅游知识，我们为您提供独到的见解和目的地，让您走在时代前沿。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_card_offset
msgid "Your Gateway to Unforgettable Journeys"
msgstr "开启难忘旅程的大门"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_striped_center_top
msgid "Your gateway to the world"
msgstr "通往世界的门户"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_showcase
msgid ""
"Your journey is tailored to match your preferences, making every trip "
"uniquely yours."
msgstr "我们会根据您的喜好为您量身定制旅程，让您的每一次旅行都独一无二。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_images
msgid "Your journey to the world’s wonders starts here"
msgstr "您的世界奇观之旅从这里开始"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_wavy_grid
msgid ""
"Your satisfaction is our priority. Our support team is always ready to "
"assist, ensuring you have a smooth and memorable journey."
msgstr "您的满意是我们的首要任务。我们的支持团队随时准备为您提供帮助，确保您有一个顺利而难忘的旅程。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_big_number
msgid "destinations available"
msgstr "可前往的目的地"
