# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_aviato
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-07 13:29+0000\n"
"PO-Revision-Date: 2024-09-25 18:03+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel_minimal
msgid ""
"\" A trusted partner for adventure. <br/>Professional, reliable, and always "
"delivering unique experiences. \""
msgstr "「值得信賴的歷險夥伴。<br/>專業、可靠，一直提供獨特的體驗。」"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel_minimal
msgid ""
"\" Exceptional service and planning! <br/>They made every trip truly "
"memorable. \""
msgstr "「卓越的服務及規劃！<br/>他們讓每次旅行都變得真正難忘。」"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel_minimal
msgid ""
"\" They crafted unforgettable journeys. Seamless and perfectly tailored "
"trips. \""
msgstr "「他們精心設計難忘的旅程。行程流暢舒適，是完美策劃的旅行。」"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_benefits
msgid "24/7 Travel Support"
msgstr "24/7 全天候旅遊支援"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cover
msgid ""
"<b>A travel agent helps you to plan your <br/> trip from start to finish.</b>\n"
"        <br/>"
msgstr ""
"<b>旅行社幫助你從頭到尾<br/>規劃你的旅行。</b>\n"
"        <br/>"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_text_cover
msgid ""
"<br/>Create and manage travel itineraries online with a user-friendly "
"platform that simplifies all the steps, from setup to itinerary management, "
"making it easy to offer seamless travel experiences to your clients.<br/>"
msgstr ""
"<br/>利用操作簡便的平台，線上制訂及管理旅遊行程。平台為你簡化工作，包括由初始設置到行程管理的所有步驟，讓你能輕鬆為客戶提供無縫、流暢的旅行體驗。<br/>"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_banner
msgid ""
"<br/>Enjoy breathtaking views of nature. Relax and embrace your "
"dreams.<br/><br/>"
msgstr "<br/>享受令人讚嘆的自然美景，放鬆心情，擁抱你的夢想。<br/><br/>"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_empowerment
msgid ""
"<br/>Your gateway to unforgettable adventures and dream "
"destinations.<br/><br/>"
msgstr "<br/>我們是帶你通往難忘歷險旅程及夢想國度的特快航道。<br/><br/>"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_big_number
msgid ""
"<font class=\"text-gradient\" style=\"background-image: linear-"
"gradient(0deg, rgb(244, 168, 26) 0%, rgb(222, 222, 222) 58%);\">100+</font>"
msgstr ""
"<font class=\"text-gradient\" style=\"background-image: linear-"
"gradient(0deg, rgb(244, 168, 26) 0%, rgb(222, 222, 222) 58%);\">100+</font>"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_empowerment
msgid ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" "
"role=\"img\"/>  Exclusive Travel Deals"
msgstr "<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/> 獨家旅遊優惠"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_image_title
msgid "A Deep Dive into Adventure and Discovery"
msgstr "深入冒險與發現之旅"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid ""
"A heady mix of haunting ruins, awe-inspiring art and vibrant street life, "
"Italy’s hot-blooded capital is one of the world’s most romantic and "
"charismatic cities."
msgstr "魂牽夢縈的廢墟、令人驚嘆的藝術和充滿活力的街頭生活，意大利的熱血之都是世界上最浪漫、最具魅力的城市之一."

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_striped_top
msgid "A travel agent helps you to plan your trip from start to finish."
msgstr "旅行代理可以幫助你，從頭到尾規劃行程。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_striped_center_top
msgid "Adventure Awaits"
msgstr "精彩冒險等着你"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_company_team
msgid ""
"Aline designs bespoke travel itineraries, collaborating with a global "
"network of experts to deliver unique experiences tailored to each client's "
"needs."
msgstr "Aline 負責設計度身訂造的旅遊行程，與全球專家網絡合作，根據每位客戶的需要，提供獨特難忘的體驗。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_banner
msgid ""
"An unforgettable experience! Every destination was stunning, and the service"
" was exceptional."
msgstr "體驗難忘！每個目的地都讓人讚嘆，服務也非常出色。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_striped_center_top
msgid "Book Your Trip"
msgstr "預訂你的旅程"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid "Book now"
msgstr "立即預訂"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cta_box
msgid "Book your next holiday<br/>in a minute !"
msgstr "立即預訂<br/>你的下一個旅程！"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_masonry_block_default_template
msgid "Brazil"
msgstr "巴西"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_popup
msgid "Check out now and get $200 off your first booking."
msgstr "現在立即結賬，首次預訂可享 $200 優惠。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_images
msgid "Discover Your Next Adventure with Us"
msgstr "與我們一起，探索你下一次的冒險旅程"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cta_box
msgid "Discover how we can help you for your next trip ! <br/><br/>"
msgstr "了解我們可以如何幫助你計劃下一次旅行！<br/><br/>"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_freegrid
msgid ""
"Discover the beauty of the world through our curated travel experiences. "
"From adventurous excursions to relaxing tours, we tailor your journeys to "
"meet your wildest travel dreams."
msgstr "透過我們精心設計的旅行體驗，發現塵世之美。從歷險探秘到輕鬆遊覽，我們都會為你量身設計旅程，滿足你最瘋狂的旅行夢想。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quadrant
msgid ""
"Discover the world with our bespoke travel services. From plane tours to "
"unique excursions, we create experiences that leave a lasting "
"impression.<br/><br/> Let us be your guide to extraordinary adventures."
msgstr ""
"享用我們量身設計的旅遊服務，環遊全世界。不論是空中觀光團，或是獨特的尋幽探秘，我們都會為你創造難忘體驗。<br/><br/>讓我們充當導遊，帶你進入非凡歷險之旅。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_unveil
msgid ""
"Discover the world with our tailored travel services and exclusive deals."
msgstr "透過我們量身設計的旅遊服務及獨家優惠，盡情探索世界。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_carousel_intro
msgid "Discover your next adventure"
msgstr "發掘下趟精彩旅程"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_striped_top
msgid "Discovering the Ultimate Travel Experience"
msgstr "探索終極旅行體驗"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_card_offset
msgid ""
"Embark on adventures that leave a lasting impression. Our travel agency "
"offers tailored excursions and tours that connect you with the world’s most "
"breathtaking destinations."
msgstr "踏上為你留下深刻印象的冒險之旅。我們的旅行社提供量身設計的旅遊行程及郊遊，將你帶到全世界最令人讚嘆的美景勝地。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_striped_center_top
msgid ""
"Embark on unforgettable journeys with our tailored travel experiences, "
"designed to explore the world’s most breathtaking destinations."
msgstr "透過我們為你量身設計的旅遊體驗，踏上難忘旅程，探索世界上最令人讚嘆的旅遊勝地。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cards_grid
msgid ""
"Enjoy the peace and quiet of Finland's forests and lakes. Rest and relax in "
"one of our best partnered resorts."
msgstr "遊歷芬蘭，感受森林與湖泊的寧靜。入住我們其中一間最佳的合作度假村，好好放鬆休息。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid ""
"Everything was very well organised. Thank you very much for everything!"
msgstr "一切都安排得妥妥當當。非常感謝你們所做的一切！"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_benefits
msgid "Exclusive Experiences"
msgstr "獨家精彩體驗"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_features_wall
msgid ""
"Experience personalized itineraries crafted to suit your interests, "
"preferences, and budget for the perfect vacation."
msgstr "體驗完全按照你興趣、喜好及預算，精心設計的個人化行程，享受完美假期。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_freegrid
msgid "Experience the World with Our Unique Travel Plans"
msgstr "運用我們的獨特旅遊計劃，體驗世界"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_carousel_intro
msgid ""
"Experience the beauty and culture of unique destinations, curated to bring "
"out the traveler in you, every single day."
msgstr "體驗獨特勝地的美景及文化，行程精心策劃，讓你每天都期待探索。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_sidegrid
msgid "Experience the real travel experience"
msgstr "體驗真實的旅遊體驗"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_wavy_grid
msgid "Expertise and Adventure"
msgstr "專業知識結合冒險之旅"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_showcase
msgid "Expertly Curated Trips"
msgstr "專業策劃的精彩旅程"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_empowerment
msgid "Explore Destinations"
msgstr "探索不同目的地"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_unveil
msgid "Explore New Horizons"
msgstr "探索新視野"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_banner
msgid "Explore Now"
msgstr "立即探索"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_picture
msgid "Explore World's Hidden Gems"
msgstr "探索隱世秘景"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_images
msgid "Explore exotic locations with expert guides"
msgstr "跟隨專家導遊，一起探索充滿異國風情的旅遊勝地"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_carousel_intro
msgid ""
"Explore more and find unique destinations that align with your travel "
"dreams, crafted for unforgettable experiences."
msgstr "探索更多內容，找出符合你夢想旅程的獨特目的地，打造難忘體驗。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_images_mosaic
msgid "Explore our latest destinations and unique travel experiences."
msgstr "探索我們最新的目的地，以及獨特的旅遊體驗。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_carousel_intro
msgid "Explore the world"
msgstr "盡情探索世界"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_card_offset
msgid "Explore the world with us today."
msgstr "今天，就與我們一起探索世界。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_empowerment
msgid "Explore with<br/>GlobeTrek Travel"
msgstr "GlobeTrek Travel<br/>與你一起探索世界"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_banner
msgid "Explored Peru in 2024"
msgstr "2024 年：探索秘魯"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_features_wall
msgid "Flight &amp; Hotel Deals"
msgstr "航班及酒店優惠"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_features
msgid "Flight Booking"
msgstr "航班預訂"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_masonry_block_default_template
msgid "France"
msgstr "法國"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_masonry_block_default_template
msgid "From $119"
msgstr "$119 起"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_masonry_block_default_template
msgid "From $149"
msgstr "$149 起"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_masonry_block_default_template
msgid "From $299"
msgstr "$299 起"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_masonry_block_default_template
msgid "From $349"
msgstr "$349 起"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_masonry_block_default_template
msgid "From $699"
msgstr "$699 起"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid "From France"
msgstr "法國出發"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid "From Italy"
msgstr "意大利出發"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid "From United Kingdom"
msgstr "英國出發"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_showcase
msgid ""
"From booking to departure, we handle all the details so you can focus on "
"enjoying your adventure."
msgstr "從預訂到出發，我們都會為你處理所有細節，讓你只需專心享受冒險旅程。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_benefits
msgid ""
"Gain access to curated experiences and hidden gems that go beyond the usual "
"tourist paths, making your journey truly special."
msgstr "探索跨越慣常的旅遊路線，享受精心策劃的體驗及隱世秘景，讓你的旅程真正與眾不同。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_text_cover
msgid "Get started"
msgstr "開始"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_masonry_block_default_template
msgid "Greece"
msgstr "希臘"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_features_wall
msgid "Group Tours &amp; Adventures"
msgstr "團體導賞及探險"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cards_grid
msgid "Hike in Switzerland"
msgstr "瑞士登高遠足"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_features
msgid "Hotel Booking"
msgstr "酒店預訂"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_company_team
msgid ""
"Iris is responsible for creating cost-effective travel packages, ensuring "
"clients can enjoy their adventures without compromising on quality."
msgstr "Iris 負責設計成本效益較高的旅遊套餐及套票，確保客戶享受旅程樂趣時，質素不會因為「將貨就價」而受影響。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid ""
"It would take too long to describe day by day the sights, sounds, and people"
" that we came across. Ireland was fantastic."
msgstr "若要描述我們每一天看到的風景、聽到的聲音及遇到的人和事，不知要說到何年何日！愛爾蘭實在太美妙了。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_masonry_block_default_template
msgid "Italy"
msgstr "意大利"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_image_text
msgid "Itinerary Planning"
msgstr "行程規劃"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid "Jacques and Marie"
msgstr "Jacques 與 Marie"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_features_wall
msgid ""
"Join expertly guided group tours for exciting cultural, historical, and "
"nature-filled experiences in top destinations worldwide."
msgstr "參加由專業導遊領航的導賞團，在全球最頂級的旅遊勝地，親身體驗刺激有趣的異國文化、歷史及自然風光。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_shape_image
msgid "Join the Experience"
msgstr "親身體驗"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_images
msgid "Journey to Unforgettable Destinations"
msgstr "暢遊難忘勝地"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cards_grid
msgid ""
"Known for its stunning beaches Thailand is a Southeast Asian country, enjoy "
"its lush forests and vibrant culture."
msgstr "泰國是東南亞國家，以迷人的海灘而聞名，擁有茂密的森林及充滿活力的文化。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_banner
msgid "Let's <br/>travel the world"
msgstr "一起<br/>遨遊世界"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid "Linda and Paulo"
msgstr "Linda 與 Paulo"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_images
msgid "Luxury, comfort, and adventure await you"
msgstr "奢華、舒適與歷險之旅，等着你！"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cards_grid
msgid ""
"Marbella is a city on the southern coast of Spain known for its luxurious "
"lifestyle, beautiful beaches, and vibrant nightlife."
msgstr "馬貝雅是西班牙南部海岸城市，以奢華生活方式、美麗的海灘，以及充滿活力的夜生活而聞名。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_company_team
msgid "Meet our travel agents"
msgstr "認識我們的旅行代理"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_company_team
msgid ""
"Mich ensures the smooth execution of all travel plans, leveraging his "
"industry expertise to guarantee a seamless client experience."
msgstr "Mich 確保所有旅行計劃都能順利進行，並利用他的行業專業知識，盡力確保客戶的體驗流暢無瑕。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_picture
msgid "Old Harry Rocks, Isle of Purbeck"
msgstr "老哈利岩，英格蘭波貝克島"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_benefits
msgid ""
"Our dedicated team is here to assist you at any time, from the moment you "
"book until you return home safely."
msgstr "從你預訂的一刻，直到你安全回家，我們的專業團隊都會隨時為你提供協助。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cta_box
msgid "Our services"
msgstr "我們的服務"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_text_image
msgid ""
"Our team travels widely the globe to bring our clients first-hand knowledge "
"of upcoming destinations, new \"hot\" hotels and ships, local restaurants, "
"neighborhood hangouts, bars, pubs, the best. beaches and places to visit and"
" not to see."
msgstr ""
"我們的團隊走遍全球，為客戶提供第一手資訊，包括越來越受歡迎的目的地、新的熱門酒店及郵輪、當地餐館、鄰里聚會場所、酒吧、最好的海灘以及值得一去和不值得一去的地方."

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_image_text
msgid ""
"Our vision is to make travel planning stress free and make travel more fun. "
"No matter the type of trip you have in mind, we have the resources and "
"expertise necessary to create the perfect trip"
msgstr "我們的願景是讓旅行計劃毫無壓力，讓旅行更加有趣。無論您想進行哪種類型的旅行，我們都能提供必要的資源和專業知識，為您打造完美旅行."

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid "Paris"
msgstr "巴黎"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid ""
"Paris' monument-lined boulevards, museums, classical bistros and boutiques "
"are enhanced by a new wave of multimedia galleries, creative wine bars.."
msgstr "巴黎的古蹟林立林蔭大道、博物館、古典小酒館及精品店，在新一波多媒體畫廊、創意葡萄酒吧的映襯下，更加絢麗多彩。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_benefits
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_showcase
msgid "Personalized Itineraries"
msgstr "個人化行程"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_text_cover
msgid "Plan Trips. <br/>Effortlessly."
msgstr "規劃行程，<br/>簡單方便。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_empowerment
msgid "Plan Your Trip"
msgstr "規劃你的旅程"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cover
msgid "Plan your trip with us"
msgstr "與我們一起計劃行程"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid "Prague"
msgstr "布拉格"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid ""
"Prague is the equal of Paris in terms of beauty. Its history goes back a "
"millennium. And the beer? The best in Europe. Prague is the perfect city to "
"walk around."
msgstr "布拉格的美可與巴黎媲美。它的歷史可以追溯到千年以前。而啤酒呢？是歐洲最好的啤酒。布拉格是一座適合漫步的城市."

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_wavy_grid
msgid "Quality and Experience"
msgstr "高級品質、難忘體驗"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_carousel_intro
msgid "Refresh your travel plans with our exclusive packages and expert tips."
msgstr "透過我們的獨家套餐及專家秘笈，讓你的旅遊計劃煥然一新。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cards_grid
msgid "Relax in Marbella"
msgstr "馬貝雅放鬆之旅"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cards_grid
msgid "Rest in Finland"
msgstr "芬蘭靜修休養"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_masonry_block_default_template
msgid "Romania"
msgstr "羅馬尼亞"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_three_columns
msgid "Rome"
msgstr "羅馬"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid "Rose and Peter"
msgstr "Rose 與 Peter"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_features_wall
msgid ""
"Save big on exclusive flight and hotel offers, ensuring comfortable and "
"affordable accommodations throughout your journey."
msgstr "尊享獨家航班及酒店優惠，節省大筆費用，確保整個旅程都能享受舒適實惠的住宿。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_showcase
msgid "Seamless Travel Experience"
msgstr "流暢順利的旅行體驗"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_picture
msgid ""
"Set off on an unforgettable adventure to the world's most breathtaking "
"locations, thoughtfully curated just for you."
msgstr "踏上一趟難忘冒險之旅，前往世界上最令人讚嘆的旅遊勝地，享受為你精心策劃的行程。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quadrant
msgid "Start Exploring"
msgstr "啟動探索之旅"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_freegrid
msgid "Start Your Journey"
msgstr "開始你的旅程"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_wavy_grid
msgid "Sustainable Travel"
msgstr "可持續旅遊"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cards_grid
msgid ""
"Switzerland is a mountainous country known its hiking trails. Feel the fresh"
" air and enjoy the beautiful scenery of the Swiss Alps."
msgstr "瑞士是多山國家，以遠足小徑聞名。呼吸新鮮空氣，欣賞阿爾卑斯山的震撼美景。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_images
msgid "Tailor-made experiences for every traveler"
msgstr "每位旅客，都有量身設計的體驗"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_wavy_grid
msgid "Tailored Journeys"
msgstr "度身設計旅程"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_features_wall
msgid "Tailored Travel Packages"
msgstr "客製化旅遊套餐"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cards_grid
msgid "Tan in Thailand"
msgstr "泰國享受日光浴"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quotes_carousel
msgid ""
"The parts of the country we visited were beautiful, people were friendly, "
"everything we ate was delicious, and there were many fun things to see and "
"do."
msgstr "我們行程所到之處，都是景色優美、人們熱情友善、食物美味的好地方，還有很多有趣的事去探索、體驗。"

#. module: theme_aviato
#: model:ir.model,name:theme_aviato.model_theme_utils
msgid "Theme Utils"
msgstr "主題"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_features
msgid "Ticket Booking"
msgstr "訂票"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_company_team
msgid ""
"Tony is dedicated to crafting personalized travel experiences, ensuring "
"every client's journey is tailored to their unique preferences."
msgstr "Tony 用心打造個人化的獨特旅行體驗，確保每位客戶的旅程，都是按照他們的特有喜好去量身設計的。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_title
msgid "Top Destinations"
msgstr "熱門地點"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_image_title
msgid ""
"Transform your journey with our exclusive travel experiences, where "
"exploration meets comfort. Elevate your adventures with trips that blend "
"excitement and relaxation seamlessly."
msgstr "透過我們的獨特旅行體驗，改變你的旅程，讓歷險探索都可以舒適自在。透過完美融合刺激與放鬆的行程，提升你的冒險體驗。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_quadrant
msgid "Travel Awaits"
msgstr "好玩旅程等着你"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_popup
msgid "Travel Now"
msgstr "立即展開旅行"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_shape_image
msgid "Travel services"
msgstr "旅遊服務"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cover
msgid "Travel to any corner <br/>of the world"
msgstr "遊歷世界<br/>任何角落"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_image_hexagonal
msgid "Travel to any corner of the world"
msgstr "遊覽世界任何一個角落"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_sidegrid
msgid ""
"Traveling lets you explore new cultures and places while discovering more "
"about yourself. It's a chance to connect with others and create lasting "
"memories."
msgstr "旅行是一趟探索新文化及地方的體驗，同時讓你更了解自己。出門遠行，是與他人聯繫並創造持久回憶的機會。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_carousel_intro
msgid "Unforgettable journeys tailored for you"
msgstr "為你量身打造難忘旅程"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_cards_grid
msgid "Unravel your next adventure"
msgstr "揭開你的下一趟冒險之旅"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_images_mosaic
msgid "Unveiling Our Travel Adventures"
msgstr "揭開我們的冒險旅程"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_empowerment
msgid "View Our Offers   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"
msgstr "查看我們的優惠 <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_benefits
msgid ""
"We craft custom travel plans that suit your interests and needs, ensuring "
"every trip is unique and unforgettable."
msgstr "我們根據你的興趣及需要，設計客製化旅遊計劃，確保每次旅程都獨特難忘。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_wavy_grid
msgid ""
"We craft personalized itineraries to match your travel dreams. Our team "
"works with you to ensure a seamless adventure from start to finish."
msgstr "我們精心設計個人化行程，以滿足你的旅行夢想。我們的團隊會與你合作，確保從開始到結束，都能享受無縫歷險旅程。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_shape_image
msgid ""
"We offer customized travel packages tailored to your preferences. From "
"booking flights and accommodations to organizing tours, our goal is to "
"provide a seamless and enjoyable travel experience."
msgstr "我們根據你的喜好，提供客製化的旅遊套餐。從預訂機票及住宿，到組織遊覽行程，我們的目標是為你提供流暢、愉快的旅行體驗。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_wavy_grid
msgid ""
"We provide cutting-edge travel solutions to explore the world. Leveraging "
"the latest trends, we help you achieve your travel goals."
msgstr "我們提供先進的旅遊方案，幫助你探索世界。利用最新潮流趨勢，我們會幫助你實現旅行目標。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_showcase
msgid ""
"We select only the best destinations, ensuring your travel experience is "
"nothing short of extraordinary."
msgstr "我們只會挑選最好的目的地，確保你有非凡的旅行體驗。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_text_image
msgid "What we can do, <b>for you</b>"
msgstr "我們能<b>為您</b>做什麼"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_wavy_grid
msgid "What we deliver to our travelers"
msgstr "我們為旅行者提供的服務"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_showcase
msgid "Why Choose Us"
msgstr "為何選擇我們"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_popup
msgid "Win 200$"
msgstr "贏取 $200"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_wavy_grid
msgid ""
"With vast experience and deep travel knowledge, we offer insights and "
"destinations that keep you ahead of the curve."
msgstr "憑藉豐富的經驗及深厚的旅遊知識，我們提供獨有見解及目的地，讓你保持領先地位。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_card_offset
msgid "Your Gateway to Unforgettable Journeys"
msgstr "為你開啟難忘旅程的大門"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_striped_center_top
msgid "Your gateway to the world"
msgstr "帶你探索世界的大門"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_showcase
msgid ""
"Your journey is tailored to match your preferences, making every trip "
"uniquely yours."
msgstr "我們將根據你的喜好，量身設計你的旅程，讓你每一次旅行都獨一無二。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_key_images
msgid "Your journey to the world’s wonders starts here"
msgstr "你的世界奇觀之旅，就在這裏開始！"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_wavy_grid
msgid ""
"Your satisfaction is our priority. Our support team is always ready to "
"assist, ensuring you have a smooth and memorable journey."
msgstr "你稱心滿意，是我們的首要任務。我們的支援團隊隨時準備好提供協助，確保你有順利又難忘的旅程。"

#. module: theme_aviato
#: model_terms:theme.ir.ui.view,arch:theme_aviato.s_big_number
msgid "destinations available"
msgstr "可到達目的地"
