---
description: Always ask clarifying questions when requirements are unclear or ambiguous
globs: **/*
alwaysApply: true
---
# Requirements Clarification Rule

When receiving user requirements that are unclear, ambiguous, or could be interpreted in multiple ways, always ask clarifying questions before proceeding with implementation.

## When to Ask Clarifying Questions

1. **Ambiguous Requirements**: When the requirement could be interpreted in multiple ways
2. **Missing Context**: When important context or constraints are not provided
3. **Unclear Scope**: When it's not clear what should be included or excluded
4. **Technical Decisions**: When multiple technical approaches are possible
5. **User Experience**: When UX/UI decisions could significantly impact the solution

## Types of Clarifying Questions to Ask

### Functional Requirements
- "Should this feature work for all users or specific user groups?"
- "What should happen when [specific scenario] occurs?"
- "Are there any exceptions or edge cases to consider?"

### Technical Requirements
- "Do you prefer [Option A] or [Option B] for this implementation?"
- "Should this be a new module or an extension of existing functionality?"
- "What's the expected performance impact or scale?"

### User Experience
- "Where should this feature be accessible from in the UI?"
- "What should the user see when [specific action] happens?"
- "Are there any existing patterns in the codebase to follow?"

### Business Logic
- "What are the business rules that govern this functionality?"
- "Are there any compliance or security requirements?"
- "What's the priority level for this feature?"

## Question Format

Structure clarifying questions as:

1. **Specific and focused** - One clear question per point
2. **Provide options** - Give concrete alternatives when possible
3. **Explain context** - Why the clarification is needed
4. **Use examples** - Provide concrete scenarios when helpful

## Example Clarifying Questions

### Good Examples:
- "For the delivery assignment feature, should delivery users see only their assigned deliveries, or should they also see unassigned deliveries they could potentially take?"
- "When you say 'portal integration', do you mean a separate portal page, or integration with the existing customer portal?"
- "For the signature capture, should this work on mobile devices, or is desktop-only sufficient?"

### Bad Examples:
- "What do you want?" (too vague)
- "Can you clarify?" (not specific enough)
- "I don't understand" (doesn't help the user provide better information)

## Implementation Guidelines

1. **Ask early** - Don't wait until implementation to clarify
2. **Be specific** - Provide concrete options or scenarios
3. **Explain impact** - Help user understand why clarification matters
4. **Document decisions** - Keep track of clarifications for future reference
5. **Iterate if needed** - Ask follow-up questions if initial answers are still unclear

## When Not to Ask

- When requirements are already clear and complete
- When the user has explicitly stated they want you to make the decision
- When the clarification would significantly delay a simple task

## Follow-up Actions

After receiving clarifications:
1. Summarize the agreed requirements
2. Confirm understanding before proceeding
3. Update any existing documentation
4. Proceed with implementation based on clarified requirements

Remember: It's better to ask one clarifying question now than to implement the wrong solution and have to redo it later.
