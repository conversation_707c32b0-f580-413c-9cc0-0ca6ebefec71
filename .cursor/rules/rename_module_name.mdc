---
description: 
globs: 
alwaysApply: false
---
# Odoo Module Renaming with Prefix Rule

## Task: Rename Odoo modules by adding a prefix (e.g., "modula_")

When renaming Odoo modules by adding a prefix, follow these steps systematically:

### 1. Directory Renaming
- Rename the module directory: `old_module_name` → `prefix_old_module_name`
- Use terminal command: `mv old_module_name prefix_old_module_name`
### 2. Manifest File Updates (`__manifest__.py`)
- Update dependencies: Change `'old_module_name'` to `'prefix_old_module_name'`
- Update assets paths: Change `'old_module_name/static/...'` to `'prefix_old_module_name/static/...'`
- Keep external URLs (like `live_test_url`) unchanged as they reference external resources

### 3. Python Import Updates
- Update all import statements in Python files:
  - Change `from odoo.addons.old_module_name.models.xxx import yyy` to `from odoo.addons.prefix_old_module_name.models.xxx import yyy`
  - Update any internal module references in action calls
  - Check all `.py` files in models/, wizard/, controller/ directories

### 4. XML File Updates
- Update all XML references:
  - Change `old_module_name.action_name` to `prefix_old_module_name.action_name`
  - Update parent menu references: `parent="old_module_name.menu_name"` to `parent="prefix_old_module_name.menu_name"`
  - Update action references in data files
  - Check all `.xml` files in views/, data/, wizard/ directories

### 5. Security File Updates
- Update `ir.model.access.csv` files if they contain module-specific references
- Update security group references if they reference the old module name

### 6. Verification Steps
- Search for any remaining references to old module names:
  - `grep_search` for `old_module_name` in `.py` and `.xml` files
  - Verify no broken imports or references remain
  - Check that all dependencies are correctly updated

### 7. Files to Check
Always examine these file types and directories:
- `__manifest__.py` - dependencies and assets
- `__init__.py` - import statements
- `models/*.py` - model imports and references
- `wizard/*.py` - wizard imports
- `controller/*.py` - controller imports
- `views/*.xml` - view references and actions
- `data/*.xml` - data references
- `security/ir.model.access.csv` - access rights
- `static/src/js/*.js` - JavaScript assets (if any)

### 8. Common Patterns to Update
- Import statements: `from odoo.addons.old_module_name`
- Action references: `old_module_name.action_name`
- Menu references: `parent="old_module_name.menu_name"`
- Asset paths: `'old_module_name/static/...'`
- Dependency lists: `'old_module_name'`

### 9. Code Style
- Follow PEP8 for Python files
- Maintain proper indentation in XML files
- Preserve existing comments and documentation

### 10. Testing
After renaming, verify:
- All imports work correctly
- No broken references remain
- Module structure is intact
- Dependencies are properly updated

This rule ensures consistent and complete module renaming across all Odoo module components.
