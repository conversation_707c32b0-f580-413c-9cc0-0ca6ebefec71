# Odoo 18 XML View Development Guidelines

## 🆕 ODOO 18 SPECIFIC FEATURES

### 1. Path Field in Actions for Better URLs 🔗

**New Feature**: Odoo 18 supports the `path` field in `ir.actions.act_window` records to create nicer, more SEO-friendly URLs.

#### ✅ CORRECT Odoo 18 Pattern with Path Field
```xml
<record id="action_my_model" model="ir.actions.act_window">
    <field name="name">My Records</field>
    <field name="res_model">my.model</field>
    <field name="path">my-records</field>  <!-- ✅ NEW: Better URL -->
    <field name="view_mode">list,form</field>
    <field name="help" type="html">
        <p class="o_view_nocontent_smiling_face">
            Create your first record!
        </p>
    </field>
</record>
```

#### 📋 Path Field Best Practices
- **Use kebab-case**: `postcode-pricelists`, `vendor-bills`, `work-entries`
- **Be descriptive**: Path should clearly indicate the content
- **Keep it short**: Avoid overly long paths
- **Use hyphens**: Not underscores or spaces
- **Be consistent**: Follow the same naming pattern across your module

### 2. Simplified Chatter Implementation 💬

**Breaking Change**: The old `<div class="oe_chatter">` pattern has been replaced with a simple `<chatter/>` tag.

#### ✅ CORRECT Odoo 18 Chatter Pattern
```xml
<form string="My Model">
    <sheet>
        <group>
            <field name="name"/>
            <field name="description"/>
        </group>
    </sheet>
    <chatter/>  <!-- ✅ NEW: Simple chatter tag -->
</form>
```

#### ❌ DEPRECATED Odoo 17 Chatter Pattern
```xml
<!-- WRONG: Old complex chatter structure -->
<form string="My Model">
    <sheet>
        <group>
            <field name="name"/>
            <field name="description"/>
        </group>
    </sheet>
    <!-- ❌ OLD: Complex chatter structure -->
    <div class="oe_chatter">
        <field name="message_follower_ids" widget="mail_followers"/>
        <field name="activity_ids" widget="mail_activity"/>
        <field name="message_ids" widget="mail_thread"/>
    </div>
</form>
```

## ⚠️ CRITICAL: Odoo 18 Breaking Changes

### 1. MANDATORY: Use `<list>` Instead of `<tree>` ❌➡️✅

**BREAKING CHANGE**: Odoo 18 requires `<list>` tags instead of `<tree>` for list views.

#### ✅ CORRECT Odoo 18 Pattern
```xml
<!-- CORRECT: Use <list> tag -->
<record id="view_model_list" model="ir.ui.view">
    <field name="name">model.list</field>
    <field name="model">model.name</field>
    <field name="arch" type="xml">
        <list string="Model List">
            <field name="name"/>
            <field name="date"/>
            <field name="state"/>
        </list>
    </field>
</record>

<!-- CORRECT: Use list in view_mode -->
<record id="action_model" model="ir.actions.act_window">
    <field name="name">Models</field>
    <field name="res_model">model.name</field>
    <field name="view_mode">list,form</field>  <!-- ✅ Use "list" not "tree" -->
</record>
```

#### ❌ WRONG Odoo 17 Pattern (Will Cause Errors)
```xml
<!-- WRONG: <tree> tag is deprecated -->
<record id="view_model_tree" model="ir.ui.view">
    <field name="name">model.tree</field>
    <field name="model">model.name</field>
    <field name="arch" type="xml">
        <tree string="Model Tree">  <!-- ❌ Will cause errors -->
            <field name="name"/>
            <field name="date"/>
            <field name="state"/>
        </tree>  <!-- ❌ Will cause errors -->
    </field>
</record>
```

### 2. MANDATORY: Remove `attrs` and `states` Attributes ❌➡️✅

**BREAKING CHANGE**: Since Odoo 17.0, the `attrs` and `states` attributes are no longer used in XML views.

#### ✅ CORRECT Odoo 18 Pattern
```xml
<!-- CORRECT: Use invisible attribute directly -->
<field name="pattern_url_key" invisible="pattern_type != 'field_based'"/>

<!-- CORRECT: Use readonly attribute directly -->
<field name="name" readonly="state != 'draft'"/>

<!-- CORRECT: Use required attribute directly -->
<field name="suffix_value" required="use_suffix == True"/>
```

#### ❌ WRONG Odoo 17 Pattern (Will Cause Errors)
```xml
<!-- WRONG: attrs attribute is deprecated -->
<field name="pattern_url_key" 
       attrs="{'invisible': [('pattern_type', '!=', 'field_based')]}"/>

<!-- WRONG: states attribute is deprecated -->
<field name="state" states="draft,ready"/>
```

## 🔧 CRITICAL: Common XML Errors and Prevention

### Error 1: External ID Not Found (Most Common)

**Problem**: `External ID not found in the system: module_name.xml_id`

#### ✅ CORRECT External ID Format
```xml
<!-- CORRECT: Use module_name.xml_id format -->
<field name="parent_id" ref="stock.menu_stock_root"/>
<field name="inherit_id" ref="delivery.view_delivery_carrier_form"/>
<field name="product_id" ref="delivery.product_product_delivery"/>
```

#### ❌ Common Mistakes to Avoid
```xml
<!-- WRONG: Missing module prefix -->
<field name="parent_id" ref="menu_stock_root"/>

<!-- WRONG: Incorrect module name -->
<field name="inherit_id" ref="delivery_carrier.view_delivery_carrier_form"/>

<!-- WRONG: Non-existent ID -->
<field name="product_id" ref="delivery.product_product_delivery_normal"/>
```

#### 🔍 How to Find Correct External IDs
1. **Check the source module's XML files**
2. **Use Odoo's developer mode**: Settings > Activate Developer Mode > View metadata
3. **Search in Odoo source code**: `grep -r "id=\"xml_id\"" addons/module_name/`
4. **Common external IDs reference table**:

```xml
<!-- Stock Module -->
stock.menu_stock_root
stock.group_stock_user
stock.group_stock_manager
stock.picking_type_out
stock.stock_location_stock
stock.stock_location_customers

<!-- Delivery Module -->
delivery.view_delivery_carrier_form
delivery.product_product_delivery
delivery.product_category_deliveries

<!-- Sales Module -->
sales_team.group_sale_salesman
sale.group_sale_user
sale.group_sale_manager

<!-- Base Module -->
base.group_user
base.group_system
base.group_no_one
```

### Error 2: XML ID Order Dependencies

**Problem**: `Element with xml_id 'current_module.xml_id' not found`

#### ✅ Correct File Order in __manifest__.py
```python
# CORRECT: Define views before actions, actions before menus
'data': [
    'security/ir.model.access.csv',           # 1. Security first
    'data/initial_data.xml',                  # 2. Initial data
    'views/model_views.xml',                  # 3. Views first
    'views/other_model_views.xml',            # 4. More views
    'views/menu_actions.xml',                 # 5. Actions and menus last
],
```

#### 🔧 File Organization Best Practices
1. **Single file approach** (recommended for small modules):
```python
'data': [
    'security/ir.model.access.csv',
    'views/all_views.xml',  # Contains views, actions, and menus in correct order
    'data/demo_data.xml',
],
```

2. **Multiple files approach** (for larger modules):
```python
'data': [
    'security/ir.model.access.csv',
    'views/model1_views.xml',      # Views only
    'views/model2_views.xml',      # Views only
    'views/actions.xml',           # Actions only
    'views/menus.xml',             # Menus only (references actions)
    'data/initial_data.xml',
],
```

### Error 3: Field Reference Validation

**PROBLEM**: Referencing non-existent fields in XML views causes module loading failures.

#### ✅ CORRECT Field Reference Patterns
```xml
<!-- CORRECT: Reference fields that exist on the model -->
<record id="view_model_form" model="ir.ui.view">
    <field name="model">my.model</field>
    <field name="arch" type="xml">
        <form>
            <!-- ✅ These fields exist on my.model -->
            <field name="name"/>
            <field name="description"/>
            <field name="state"/>

            <!-- ✅ CORRECT: Reference related model fields through relationship -->
            <field name="line_ids">
                <list>
                    <!-- ✅ These fields exist on related model -->
                    <field name="product_id"/>
                    <field name="name"/>
                    <field name="quantity"/>
                </list>
            </field>
        </form>
    </field>
</record>
```

#### ❌ WRONG Field Reference Patterns
```xml
<!-- WRONG: Referencing fields that don't exist on the model -->
<record id="view_model_form_wrong" model="ir.ui.view">
    <field name="model">my.model</field>
    <field name="arch" type="xml">
        <form>
            <!-- ❌ product_id doesn't exist on my.model -->
            <field name="product_id"/>

            <!-- ❌ customer_name doesn't exist on my.model -->
            <field name="customer_name"/>
        </form>
    </field>
</record>
```

### Error 4: View Inheritance XPath Validation

**PROBLEM**: Using XPath expressions that reference non-existent elements in parent views.

#### ✅ CORRECT View Inheritance Patterns
```xml
<!-- CORRECT: Reference fields that exist in parent view -->
<record id="view_order_form_inherit" model="ir.ui.view">
    <field name="name">sale.order.form.inherit</field>
    <field name="model">sale.order</field>
    <field name="inherit_id" ref="sale.view_order_form"/>
    <field name="arch" type="xml">
        <!-- ✅ partner_shipping_id exists in base sale.view_order_form -->
        <xpath expr="//field[@name='partner_shipping_id']" position="after">
            <field name="custom_field"/>
        </xpath>

        <!-- ✅ notebook always exists in form views -->
        <xpath expr="//notebook" position="inside">
            <page string="Custom Tab">
                <!-- Custom content -->
            </page>
        </xpath>
    </field>
</record>
```

#### ❌ WRONG View Inheritance Patterns
```xml
<!-- WRONG: Referencing fields that don't exist in parent view -->
<record id="view_order_form_inherit_wrong" model="ir.ui.view">
    <field name="name">sale.order.form.inherit.wrong</field>
    <field name="model">sale.order</field>
    <field name="inherit_id" ref="sale.view_order_form"/>
    <field name="arch" type="xml">
        <!-- ❌ carrier_id might not exist in base sale view -->
        <xpath expr="//field[@name='carrier_id']" position="after">
            <field name="custom_field"/>
        </xpath>

        <!-- ❌ custom_section doesn't exist in parent view -->
        <xpath expr="//group[@name='custom_section']" position="inside">
            <field name="another_field"/>
        </xpath>
    </field>
</record>
```

#### 📋 Safe XPath Targets for Common Views

**Sale Order Form (`sale.view_order_form`)**
```xml
<!-- ✅ SAFE: These fields typically exist -->
<xpath expr="//field[@name='partner_id']" position="after">
<xpath expr="//field[@name='partner_shipping_id']" position="after">
<xpath expr="//field[@name='partner_invoice_id']" position="after">
<xpath expr="//field[@name='date_order']" position="after">
<xpath expr="//notebook" position="inside">
<xpath expr="//header" position="inside">

<!-- ⚠️ RISKY: These fields may not exist in base view -->
<xpath expr="//field[@name='carrier_id']" position="after">  <!-- Added by delivery module -->
<xpath expr="//field[@name='incoterm']" position="after">     <!-- Added by stock module -->
```

## Basic Structure

All views should be defined using the `record` tag with `model='ir.ui.view'`. Here's the basic structure:

```xml
<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- View Definition -->
    <record id="view_model_name_form" model="ir.ui.view">
        <field name="name">model.name.form</field>
        <field name="model">model.name</field>
        <field name="arch" type="xml">
            <form>
                <!-- Form view content -->
            </form>
        </field>
    </record>

    <!-- List View (using list tag instead of tree) -->
    <record id="view_model_name_list" model="ir.ui.view">
        <field name="name">model.name.list</field>
        <field name="model">model.name</field>
        <field name="arch" type="xml">
            <list>
                <field name="field1"/>
                <field name="field2"/>
            </list>
        </field>
    </record>

    <!-- Action Definition -->
    <record id="action_model_name" model="ir.actions.act_window">
        <field name="name">Model Name</field>
        <field name="res_model">model.name</field>
        <field name="path">model-name</field>  <!-- ✅ Odoo 18: Better URL -->
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first record!
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_model_name"
        name="Model Name"
        action="action_model_name"
        parent="parent_menu_id"
        sequence="10"/>
</odoo>
```

## Complete Example with Odoo 18 Features

```xml
<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Form View -->
    <record id="view_employee_form" model="ir.ui.view">
        <field name="name">employee.form</field>
        <field name="model">hr.employee</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="department_id"/>
                    </group>
                </sheet>
                <chatter/>  <!-- ✅ Odoo 18: Simple chatter -->
            </form>
        </field>
    </record>

    <!-- List View -->
    <record id="view_employee_list" model="ir.ui.view">
        <field name="name">employee.list</field>
        <field name="model">hr.employee</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="department_id"/>
                <field name="job_title"/>
            </list>
        </field>
    </record>

    <!-- Action -->
    <record id="action_employee" model="ir.actions.act_window">
        <field name="name">Employees</field>
        <field name="res_model">hr.employee</field>
        <field name="path">employees</field>  <!-- ✅ Odoo 18: Better URL -->
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first employee!
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_employee"
        name="Employees"
        action="action_employee"
        parent="hr_employee.menu_hr_root"
        sequence="10"/>
</odoo>
```

## 🔍 XML Validation Checklist

### Before Creating XML Files
- [ ] **Check dependencies**: Ensure all referenced modules are in `depends` list in `__manifest__.py`
- [ ] **Verify external IDs**: Use developer mode or source code to confirm external ID existence
- [ ] **Plan file order**: Organize XML files in logical dependency order
- [ ] **Validate parent views**: Check parent view structure before creating inheritance
- [ ] **Avoid deprecated attributes**: Do not use `attrs` or `states` attributes (deprecated since Odoo 17.0)

### External ID Validation ⚠️ **CRITICAL**
- [ ] **Format check**: All external references use `module_name.xml_id` format
- [ ] **Module verification**: Referenced modules exist and are installed
- [ ] **ID existence**: Referenced IDs actually exist in the target module
- [ ] **Source file verification**: Check actual XML files for ID definitions
- [ ] **Inheritance ID validation**: Verify inherit_id references exist before using

### XPath Inheritance Validation ⚠️ **CRITICAL**
- [ ] **Parent view examination**: Check parent view source code for available fields
- [ ] **XPath target verification**: Ensure XPath expressions reference existing elements
- [ ] **XPath chain validation**: Use `list` instead of `tree` in XPath chains for Odoo 18
- [ ] **Safe target usage**: Use commonly available fields (partner_id, notebook, header)
- [ ] **Dependency awareness**: Know which fields are added by optional modules
- [ ] **Test inheritance**: Validate view inheritance loads without errors

### File Order Validation
- [ ] **Manifest order**: Files listed in correct dependency order in `__manifest__.py`
- [ ] **Within file order**: Views before actions, actions before menus
- [ ] **Security first**: `ir.model.access.csv` listed first in data files

## 🚨 Error Prevention Strategies

### Strategy 1: Incremental Development
1. **Start with basic views** (no external references)
2. **Add actions** (reference internal views only)
3. **Add menus** (reference internal actions)
4. **Add inheritance** (reference external IDs last)

### Strategy 2: Validation Testing
```bash
# Test XML syntax
python3 -c "import xml.etree.ElementTree as ET; ET.parse('views/file.xml')"

# Test module loading
odoo-bin -d test_db -i module_name --test-enable --stop-after-init
```

### Strategy 3: Reference Verification
```python
# In Odoo shell, verify external ID exists
self.env.ref('module_name.xml_id')
# Should not raise ValueError
```

## 📋 Migration Commands

```bash
# Find all tree tags in your module (WRONG in Odoo 18)
grep -r "<tree" your_module/views/

# Find all tree view modes (WRONG in Odoo 18)
grep -r "view_mode.*tree" your_module/views/

# Find all attrs usage (WRONG in Odoo 18)
grep -r "attrs=" your_module/views/

# Find all states usage (WRONG in Odoo 18)
grep -r "states=" your_module/views/

# Find old chatter patterns (WRONG in Odoo 18)
grep -r "div class=\"oe_chatter\"" your_module/views/

# Replace patterns:
# <tree>          → <list>
# </tree>         → </list>
# tree,form       → list,form
# form,tree       → form,list
# attrs="{'invisible': [('field', '!=', 'value')]}" → invisible="field != 'value'"
# attrs="{'readonly': [('field', '=', 'value')]}" → readonly="field == 'value'"
# attrs="{'required': [('field', '=', 'value')]}" → required="field == 'value'"
# <div class="oe_chatter"> → <chatter/>
```

## 🎯 AI Development Guidelines

### When Requesting XML Creation from AI

#### ✅ Good Prompts
```
Create XML views for model.name with:
- List view with fields: field1, field2
- Form view with proper grouping and chatter
- Action with path field for better URLs
- Menu under stock.menu_stock_root
- Use proper external ID format: module_name.xml_id
- Ensure correct file order: views before actions before menus
- Follow Odoo 18 patterns: use <list> not <tree>, <chatter/> not div class="oe_chatter"
```

#### ❌ Avoid These Prompts
```
Create views for the model
Add menu somewhere in stock
Make it inherit from delivery views
Use old Odoo 17 patterns
```

#### 🔧 Specific Instructions to Include
1. **Specify exact external IDs**: "Use stock.menu_stock_root as parent menu"
2. **Request validation**: "Verify all external IDs exist in referenced modules"
3. **Specify file order**: "Create views first, then actions, then menus"
4. **Include dependencies**: "Ensure all referenced modules are in depends list"
5. **Odoo 18 compliance**: "Use <list> tags, <chatter/> tag, and path fields"

## Key Points Summary

1. **Odoo 18 Specific Features**
   - ✅ Use `path` field in actions for better URLs
   - ✅ Use `<chatter/>` tag instead of `<div class="oe_chatter">`
   - ✅ Use `<list>` instead of `<tree>` for list views
   - ✅ Use direct attributes instead of `attrs` and `states`

2. **Error Prevention**
   - ✅ Always use proper external ID format: `module_name.xml_id`
   - ✅ Verify external IDs exist before referencing
   - ✅ Check file order in `__manifest__.py`
   - ✅ Validate parent view structure before inheritance
   - ✅ Test XML loading to catch errors early

3. **Best Practices**
   - ✅ Use clear, descriptive naming conventions
   - ✅ Include proper help text with modern styling
   - ✅ Follow security best practices
   - ✅ Consider mobile responsiveness
   - ✅ Document complex inheritance patterns

Remember to:
- ✅ Always use proper XML formatting
- ✅ Follow Odoo's naming conventions
- ✅ **Verify external ID format: module_name.xml_id**
- ✅ **Check file order in __manifest__.py**
- ✅ **Validate external ID existence before use**
- ✅ **Use Odoo 18 specific patterns**
- ✅ Test views in different view modes
- ✅ Consider mobile responsiveness
- ✅ Add proper security groups when needed
alwaysApply: true
---
