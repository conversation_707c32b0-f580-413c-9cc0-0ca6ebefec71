# ConfigMatrix: Complete User Guide

This comprehensive user guide provides step-by-step instructions for all ConfigMatrix users, from administrators to end users.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Administrative Setup](#administrative-setup)
3. [Template Management](#template-management)
4. [Sales Configuration](#sales-configuration)
5. [Online Sales](#online-sales)
6. [Builder Portal](#builder-portal)
7. [Troubleshooting](#troubleshooting)
8. [Best Practices](#best-practices)

## Getting Started

### System Requirements

- **Odoo 18.0+**: Full compatibility with Odoo 18 features
- **User Permissions**: Appropriate access rights for your role
- **Browser**: Modern browser with JavaScript enabled
- **Network**: Stable internet connection for real-time features

### User Roles and Permissions

#### Administrator
- Create and manage configuration templates
- Set up pricing matrices and labor calculations
- Configure security and access controls
- Manage system settings and performance

#### Sales User
- Configure products for customers
- Generate quotes and sales orders
- Access customer configuration history
- Manage pricing and discounts

#### Builder/Contractor
- Access specialized builder portal
- Manage project configurations
- Track multiple configurations per project
- Access advanced measurement tools

#### Customer (Online)
- Configure products through website
- Save and retrieve configurations
- View real-time pricing
- Place orders with configurations

## Administrative Setup

### 1. Initial Configuration

#### Step 1: Install and Configure Module
1. Navigate to **Apps** → **Update Apps List**
2. Search for "ConfigMatrix" and install the module
3. Go to **Settings** → **ConfigMatrix** → **Configuration**
4. Configure basic settings:
   - **Enable Check Measure**: Allow measurement-based configurations
   - **Enable Sales/Quoting**: Enable sales order integration
   - **Enable Online Sales**: Enable customer portal configuration
   - **Default Currency**: Set default currency for pricing
   - **Max Fields per Template**: Set maximum fields (default: 100)

#### Step 2: Set Up Security Groups
1. Go to **Settings** → **Users & Companies** → **Groups**
2. Configure the following groups:
   - **ConfigMatrix User**: Basic configuration access
   - **ConfigMatrix Administrator**: Full administrative access
   - **ConfigMatrix Builder**: Builder portal access
3. Assign users to appropriate groups

#### Step 3: Create Matrix Categories
1. Navigate to **ConfigMatrix** → **Pricing** → **Matrix Categories**
2. Create categories for organizing pricing matrices:
   - **Price Matrix**: For product pricing
   - **Labor Matrix**: For labor time calculations
   - **Material Matrix**: For material costs
   - **Other**: For miscellaneous calculations

### 2. Product Setup

#### Step 1: Configure Products
1. Go to **Inventory** → **Products** → **Products**
2. Select a product to make configurable
3. Check **"Is Configurable"** checkbox
4. Set additional options:
   - **Requires Measurement**: Enable measurement tools
   - **Has Visual Preview**: Enable SVG previews
   - **Use Matrix Pricing**: Enable pricing matrices

#### Step 2: Create Configuration Template
1. Click **"Create Configuration Template"** button
2. Fill in template details:
   - **Template Name**: Descriptive name (e.g., "Standard Door Configuration")
   - **Template Code**: Unique identifier (e.g., "DOOR_STD_001")
   - **Description**: Detailed description of the template
   - **Version**: Template version (e.g., "1.0")
3. Configure use case enablement:
   - **Check Measure**: Enable for measurement scenarios
   - **Sales/Quoting**: Enable for sales orders
   - **Online Sales**: Enable for customer portal
4. Click **"Save"**

### 3. Template Structure Setup

#### Step 1: Create Sections
1. In the template, go to **"Sections"** tab
2. Click **"Add Section"** button
3. Create logical sections:
   - **Door Dimensions**: Height, width, thickness
   - **Hardware**: Hinges, handles, locks
   - **Materials**: Frame, panel, finish
   - **Options**: Additional features and accessories
4. Set section properties:
   - **Section Name**: Clear, descriptive name
   - **Sequence**: Display order
   - **Description**: Help text for users
   - **Use Case Visibility**: Control visibility per use case

#### Step 2: Add Fields
1. Select a section and click **"Add Field"**
2. Configure field properties:
   - **Field Label**: User-friendly name
   - **Technical Name**: Unique identifier (e.g., "door_height")
   - **Field Type**: Text, Number, Selection, Boolean, Date
   - **Sequence**: Display order within section
3. Set field-specific options:
   - **Text Fields**: Min/max length, validation patterns
   - **Number Fields**: Decimal precision, units of measure
   - **Selection Fields**: Options, searchable dropdown
   - **Boolean Fields**: Custom true/false labels

#### Step 3: Configure Field Properties
1. **Use Case Properties**: Set visibility and behavior per use case
2. **Validation Rules**: Configure min/max values, patterns
3. **Help Text**: Provide guidance for users
4. **Default Values**: Set initial values
5. **Error Messages**: Custom error text for validation failures

### 4. Advanced Field Configuration

#### Dynamic Content
1. **Dynamic Help Text**: Use field placeholders for context-sensitive help
   - Template: `"Enter height between {min_height} and {max_height} mm"`
   - Placeholders: `{field_name}` references other field values

2. **Dynamic Default Values**: Calculate defaults based on other fields
   - Template: `"{door_height} * 0.6"` (60% of door height)
   - Expression: Use mathematical formulas and field references

3. **Dynamic Error Messages**: Context-sensitive error messages
   - Template: `"Height must be at least {min_height}mm for this door type"`
   - Condition: Python expression for when to show error

#### Visibility Conditions
1. **Simple Conditions**: Show/hide based on field values
   - Example: `door_type == "sliding"`
   - Operators: `==`, `!=`, `>`, `<`, `>=`, `<=`

2. **Complex Conditions**: Multiple field combinations
   - Example: `door_type == "sliding" and door_width > 1000`
   - Logical operators: `and`, `or`, `not`

3. **Range Conditions**: Show based on value ranges
   - Example: `door_height >= 2000 and door_height <= 2500`
   - Dynamic ranges: `door_height > 2510 ? 1310 : 2000`

### 5. Component Mapping

#### Step 1: Map Components to Fields
1. Go to **"Component Mappings"** tab
2. Click **"Add Component Mapping"**
3. Configure mapping:
   - **Field**: Select the field that triggers the component
   - **Component Product**: Select the product to include
   - **Quantity Formula**: Calculate quantity (e.g., `1`, `door_width / 1000`)
   - **Condition**: Optional condition for inclusion

#### Step 2: Advanced Component Mapping
1. **Multiple Components**: Map multiple components to one field
2. **Dynamic Product Selection**: Select products based on field values
3. **Conditional Inclusion**: Include components based on complex conditions
4. **Quantity Calculations**: Use formulas for dynamic quantities

#### Step 3: Option Component Mapping
1. For selection fields, map components to specific options
2. Configure option-specific quantities and conditions
3. Set up option visibility rules

### 6. Pricing Setup

#### Step 1: Create Price Matrices
1. Go to **ConfigMatrix** → **Pricing** → **Price Matrices**
2. Click **"Create"** button
3. Configure matrix:
   - **Matrix Name**: Descriptive name
   - **Product Template**: Associated product
   - **Matrix Type**: Sale Price or Cost
   - **Category**: Organize by type

#### Step 2: Define Dimension Ranges
1. **Height Ranges**: Define height intervals
   - Example: `[{"min": 345, "max": 495, "label": "345-495"}]`
2. **Width Ranges**: Define width intervals
   - Example: `[{"min": 345, "max": 495, "label": "345-495"}]`

#### Step 3: Populate Matrix Data
1. **Manual Entry**: Enter prices for each cell
2. **Import from Excel**: Upload Excel file with pricing data
3. **Formula-Based**: Use formulas for automatic calculation
4. **Special Conditions**: Handle complex pricing scenarios

#### Step 4: Labor Time Matrices
1. Create labor time matrices for manufacturing
2. Configure labor rates and time calculations
3. Set up special conditions for complex operations

### 7. Visual Components

#### Step 1: Create SVG Components
1. Go to **"SVG Components"** tab
2. Click **"Add SVG Component"**
3. Configure component:
   - **Component Name**: Descriptive name
   - **SVG Content**: Vector graphics content
   - **Dimensions**: Width and height
   - **Layer Configuration**: Z-index and layer name

#### Step 2: Conditional Layers
1. **Visibility Conditions**: Show/hide layers based on configuration
2. **Dynamic Updates**: Update visual elements based on field values
3. **Animation**: Add transitions and effects

#### Step 3: Instructional Images
1. Upload images to guide users
2. Configure display options
3. Set up preview panel integration

## Template Management

### Template Lifecycle

#### Draft Stage
1. **Initial Setup**: Create template structure
2. **Field Configuration**: Add and configure fields
3. **Component Mapping**: Set up component relationships
4. **Basic Testing**: Test individual fields and sections

#### Testing Stage
1. **Comprehensive Testing**: Test all use cases
2. **Validation Testing**: Verify all validation rules
3. **Performance Testing**: Test with large configurations
4. **User Acceptance Testing**: Get feedback from end users

#### Active Stage
1. **Production Use**: Available for all users
2. **Monitoring**: Track usage and performance
3. **Maintenance**: Regular updates and improvements

#### Archived Stage
1. **Deprecation**: Mark for removal
2. **Migration**: Move configurations to new templates
3. **Cleanup**: Remove unused templates

### Template Maintenance

#### Regular Updates
1. **Field Updates**: Modify field properties and validation
2. **Component Updates**: Update component mappings
3. **Pricing Updates**: Update pricing matrices
4. **Visual Updates**: Update SVG components and images

#### Version Control
1. **Version Tracking**: Maintain version history
2. **Change Documentation**: Document all changes
3. **Rollback Capability**: Ability to revert changes
4. **Migration Tools**: Tools for upgrading configurations

## Sales Configuration

### 1. Sales Order Integration

#### Step 1: Add Configurable Product
1. Create or edit a sales order
2. Add a configurable product to the order line
3. Notice the **"Configure"** button appears next to the product

#### Step 2: Open Configuration
1. Click **"Configure"** button
2. The ConfigMatrix interface opens in a new window/tab
3. Template loads with appropriate use case settings

#### Step 3: Complete Configuration
1. **Navigate Sections**: Use section tabs or navigation buttons
2. **Fill Fields**: Enter values for all required fields
3. **Dynamic Updates**: Watch as fields appear/disappear based on your answers
4. **Real-time Validation**: See validation messages as you type
5. **Visual Preview**: View SVG preview updates in real-time

#### Step 4: Save Configuration
1. Click **"Save Configuration"** button
2. Configuration is saved and applied to sales order line
3. **BOM Generation**: Bill of Materials is automatically created
4. **Price Calculation**: Price is calculated from matrices and components
5. Return to sales order with configuration applied

### 2. Configuration Interface Features

#### Progressive Disclosure
- **Smart Questions**: Only relevant questions are shown
- **Contextual Help**: Help text updates based on your answers
- **Visual Guidance**: Instructional images guide you through the process

#### Real-time Feedback
- **Validation Messages**: Immediate feedback on input errors
- **Dynamic Help**: Context-sensitive help text
- **Progress Indicators**: Visual progress through the configuration

#### Advanced Features
- **Searchable Dropdowns**: Type to search in large option lists
- **Unit Conversion**: Automatic unit conversion for measurements
- **Formula Display**: Show calculated values for computed fields
- **Error Highlighting**: Clear indication of validation errors

### 3. Configuration Management

#### Save and Load
1. **Save Draft**: Save incomplete configurations
2. **Load Previous**: Load previously saved configurations
3. **Copy Configuration**: Duplicate existing configurations
4. **Configuration History**: View all configurations for the product

#### Validation and Errors
1. **Field Validation**: Real-time validation of individual fields
2. **Cross-field Validation**: Validation across multiple fields
3. **Business Rule Validation**: Enforce business logic and constraints
4. **Error Resolution**: Clear guidance on fixing validation errors

## Online Sales

### 1. Customer Portal Configuration

#### Step 1: Access Portal
1. Customer logs into their portal account
2. Navigate to **"Product Configuration"** section
3. Browse available configurable products

#### Step 2: Start Configuration
1. Select a configurable product
2. Click **"Configure"** button
3. Portal configuration interface opens

#### Step 3: Complete Configuration
1. **Simplified Interface**: Optimized for customer use
2. **Guided Process**: Step-by-step configuration wizard
3. **Real-time Pricing**: See price updates as you configure
4. **Visual Preview**: View product preview with your selections

#### Step 4: Save and Order
1. **Save Configuration**: Save for future reference
2. **Add to Cart**: Add configured product to shopping cart
3. **Checkout Process**: Complete purchase with configuration

### 2. Portal Features

#### Customer Account Management
1. **Configuration History**: View all saved configurations
2. **Favorites**: Mark frequently used configurations
3. **Sharing**: Share configurations with others
4. **Export**: Export configuration details

#### Advanced Features
1. **Measurement Tools**: Built-in measurement calculators
2. **Visual Aids**: Interactive diagrams and guides
3. **Pricing Transparency**: Detailed price breakdown
4. **Availability Check**: Real-time stock and lead time information

## Builder Portal

### 1. Builder Account Setup

#### Step 1: Account Creation
1. Contact administrator for builder account
2. Receive login credentials
3. Complete profile setup

#### Step 2: Project Management
1. **Create Projects**: Set up customer projects
2. **Project Types**: Residential, commercial, industrial
3. **Configuration Slots**: Multiple configurations per project
4. **Customer Management**: Link projects to customers

### 2. Builder Configuration Workflow

#### Step 1: Project Setup
1. Create new project or select existing project
2. Set project details and customer information
3. Configure project settings and preferences

#### Step 2: Multiple Configurations
1. **Configuration Slots**: Create multiple configurations per project
2. **Product Categories**: Organize by door, screen, etc.
3. **Version Control**: Track configuration changes
4. **Comparison Tools**: Compare different configurations

#### Step 3: Advanced Features
1. **Measurement Tools**: Professional measurement calculators
2. **Technical Specifications**: Detailed technical information
3. **Installation Guides**: Step-by-step installation instructions
4. **Warranty Information**: Product warranty details

### 3. Builder-Specific Features

#### Professional Tools
1. **Bulk Configuration**: Configure multiple products at once
2. **Template Library**: Access to standard configurations
3. **Custom Calculations**: Advanced calculation tools
4. **Technical Support**: Direct access to technical support

#### Project Management
1. **Project Timeline**: Track project progress
2. **Configuration Tracking**: Monitor configuration changes
3. **Customer Communication**: Integrated communication tools
4. **Documentation**: Generate project documentation

## Troubleshooting

### Common Issues and Solutions

#### Configuration Interface Issues

**Problem**: Configuration interface doesn't load
- **Solution**: Check browser compatibility and JavaScript settings
- **Solution**: Verify user permissions and access rights
- **Solution**: Clear browser cache and cookies

**Problem**: Fields not appearing/disappearing correctly
- **Solution**: Check visibility condition syntax
- **Solution**: Verify field dependencies are set correctly
- **Solution**: Test condition evaluation with sample data

**Problem**: Validation errors not showing
- **Solution**: Check field validation rules
- **Solution**: Verify error message configuration
- **Solution**: Test validation with different input values

#### Performance Issues

**Problem**: Slow configuration loading
- **Solution**: Check network connection
- **Solution**: Reduce number of fields in template
- **Solution**: Optimize visibility conditions
- **Solution**: Enable caching if available

**Problem**: Slow field updates
- **Solution**: Simplify complex conditions
- **Solution**: Reduce dependency chains
- **Solution**: Optimize component mappings

#### Data Issues

**Problem**: BOM not generating correctly
- **Solution**: Check component mappings
- **Solution**: Verify quantity formulas
- **Solution**: Test component conditions
- **Solution**: Review BOM generation logs

**Problem**: Price calculation errors
- **Solution**: Check pricing matrix configuration
- **Solution**: Verify matrix data completeness
- **Solution**: Test price calculation formulas
- **Solution**: Review currency settings

### Debugging Tools

#### Template Testing
1. **Test Template**: Use built-in testing tools
2. **Field Testing**: Test individual fields and conditions
3. **Validation Testing**: Test all validation rules
4. **Performance Testing**: Test with large datasets

#### Log Analysis
1. **Error Logs**: Review system error logs
2. **Performance Logs**: Monitor performance metrics
3. **User Activity Logs**: Track user interactions
4. **Debug Mode**: Enable debug mode for detailed information

#### Support Resources
1. **Documentation**: Comprehensive online documentation
2. **Video Tutorials**: Step-by-step video guides
3. **Community Forum**: User community support
4. **Technical Support**: Direct technical support contact

## Best Practices

### Template Design

#### Field Organization
1. **Logical Grouping**: Group related fields in sections
2. **Progressive Disclosure**: Show fields in logical order
3. **Clear Naming**: Use descriptive field names
4. **Consistent Structure**: Maintain consistent field structure

#### Validation Rules
1. **Clear Messages**: Write clear, helpful error messages
2. **Reasonable Limits**: Set realistic min/max values
3. **Business Logic**: Enforce business rules and constraints
4. **User Guidance**: Provide helpful guidance and examples

#### Performance Optimization
1. **Efficient Conditions**: Write efficient visibility conditions
2. **Minimal Dependencies**: Minimize field dependencies
3. **Caching**: Use caching for frequently accessed data
4. **Regular Maintenance**: Regular template maintenance and updates

### User Experience

#### Interface Design
1. **Intuitive Navigation**: Clear, intuitive navigation
2. **Visual Feedback**: Provide clear visual feedback
3. **Progress Indicators**: Show progress through configuration
4. **Help and Guidance**: Provide comprehensive help and guidance

#### Error Handling
1. **Clear Error Messages**: Write clear, actionable error messages
2. **Error Prevention**: Prevent errors where possible
3. **Error Recovery**: Provide clear paths to fix errors
4. **User Support**: Provide support for complex issues

#### Accessibility
1. **Keyboard Navigation**: Full keyboard navigation support
2. **Screen Reader Support**: Compatible with screen readers
3. **High Contrast**: Support for high contrast modes
4. **Font Scaling**: Support for font scaling

### Data Management

#### Configuration Data
1. **Regular Backups**: Regular backup of configuration data
2. **Version Control**: Maintain version history
3. **Data Validation**: Regular data validation and cleanup
4. **Security**: Implement proper security measures

#### Performance Monitoring
1. **Usage Tracking**: Track system usage and performance
2. **Error Monitoring**: Monitor for errors and issues
3. **Performance Metrics**: Track key performance metrics
4. **User Feedback**: Collect and act on user feedback

This comprehensive user guide provides all the information needed to effectively use the ConfigMatrix system, from initial setup to advanced features and troubleshooting.