# ConfigMatrix: Complete Data Model Architecture

This document outlines the comprehensive database models for the ConfigMatrix system, including all advanced features and capabilities.

## Core Models

### 1. Configuration Template (`config.matrix.template`)

The central model that defines a configuration template for a product. Each configurable product has one template that supports multiple use cases.

```python
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    _description = 'Configuration Template'
    _order = 'name'

    # Basic Information
    name = fields.Char("Template Name", required=True)
    product_template_id = fields.Many2one('product.template', "Product Template", required=True)
    description = fields.Text("Description")
    code = fields.Char("Template Code", required=True)
    version = fields.Char("Version", default="1.0")
    
    # Administrative Fields
    active = fields.Boolean("Active", default=True)
    admin_notes = fields.Text("Administrative Notes")
    
    # Use Case Enablement
    enable_check_measure = fields.Boolean("Enable Check Measure", default=True)
    enable_sales = fields.Boolean("Enable Sales/Quoting", default=True)
    enable_online = fields.Boolean("Enable Online Sales", default=True)
    
    # Template Lifecycle
    state = fields.Selection([
        ('draft', 'Draft'),
        ('testing', 'Testing'),
        ('active', 'Active'),
        ('archived', 'Archived')
    ], default='draft', string="Status")
    
    # Structure
    section_ids = fields.One2many('config.matrix.section', 'matrix_id', "Sections")
    
    # Base Components (always included)
    bom_product_ids = fields.Many2many('product.product', string="Base Components")
    
    # Component Mappings
    component_mapping_ids = fields.One2many('config.matrix.component.mapping', 'template_id', string="Component Mappings")
    
    # Visual Components
    svg_component_ids = fields.One2many('config.matrix.svg.component', 'template_id', string="SVG Components")
    has_svg_preview = fields.Boolean("Has SVG Preview", compute="_compute_has_svg_preview", store=True)
    
    # Pricing Integration
    matrix_assignment_ids = fields.One2many('config.matrix.template.matrix.assignment', 'template_id', string="Matrix Assignments")
    category_assignment_ids = fields.One2many('config.matrix.template.category.assignment', 'template_id', string="Matrix Categories")
    labor_operation_matrix_ids = fields.Many2many('config.matrix.labor.time.matrix', string="Pricing and Labor Matrices")
    
    # Statistics
    configuration_count = fields.Integer("Configurations", compute='_compute_configuration_count')
    has_price_matrices = fields.Boolean("Has Price Matrices", compute='_compute_matrix_info', store=True)
    has_labor_matrices = fields.Boolean("Has Labor Matrices", compute='_compute_matrix_info', store=True)
    price_matrix_count = fields.Integer("Price Matrices", compute='_compute_matrix_info', store=True)
    labor_matrix_count = fields.Integer("Labor Matrices", compute='_compute_matrix_info', store=True)
    category_count = fields.Integer("Matrix Categories", compute='_compute_category_info', store=True)
    labor_operation_count = fields.Integer("Pricing and Labor Matrices", compute='_compute_category_info', store=True)
    
    # Visibility Statistics
    visibility_condition_count = fields.Integer("Visibility Conditions", compute='_compute_visibility_stats')
    complex_condition_count = fields.Integer("Complex Conditions", compute='_compute_visibility_stats')
    range_condition_count = fields.Integer("Range Conditions", compute='_compute_visibility_stats')
    
    # Virtual Fields for Visibility Management
    field_visibility_conditions = fields.One2many('config.matrix.visibility.condition', 'matrix_id', 
                                                string="Field Visibility Conditions", compute='_compute_visibility_conditions')
    option_visibility_conditions = fields.One2many('config.matrix.option.visibility', 'matrix_id',
                                                 string="Option Visibility Conditions", compute='_compute_visibility_conditions')
```

### 2. Matrix Section (`config.matrix.section`)

Sections group related fields together (e.g., Door, Hardware, Extrusions).

```python
class ConfigMatrixSection(models.Model):
    _name = 'config.matrix.section'
    _description = 'Configuration Matrix Section'
    _order = 'sequence, id'

    name = fields.Char("Section Name", required=True)
    matrix_id = fields.Many2one('config.matrix.template', "Configuration Matrix", required=True, ondelete='cascade')
    sequence = fields.Integer("Sequence", default=10)
    description = fields.Text("Description")
    
    # Fields in this section
    field_ids = fields.One2many('config.matrix.field', 'section_id', "Fields", order="sequence")
    
    # Use case visibility
    check_measure_visible = fields.Boolean("Check Measure: Visible", default=True)
    sales_visible = fields.Boolean("Sales: Visible", default=True)
    online_visible = fields.Boolean("Online: Visible", default=True)
    
    # Statistics
    field_count = fields.Integer("Field Count", compute='_compute_field_count', store=True)
    visible_field_count = fields.Integer("Visible Fields", compute='_compute_visible_field_count')
```

### 3. Matrix Field (`config.matrix.field`)

Individual questions in the configuration process with advanced features.

```python
class ConfigMatrixField(models.Model):
    _name = 'config.matrix.field'
    _description = 'Configuration Field'
    _order = 'sequence, id'

    # Basic Information
    name = fields.Char('Field Label', required=True, help='The label shown to users')
    technical_name = fields.Char('Technical Name', required=True, help='Identifier used in conditions and formulas')
    section_id = fields.Many2one('config.matrix.section', 'Section', required=True, ondelete='cascade')
    sequence = fields.Integer('Sequence', default=10, help='Display order within section')
    question_number = fields.Integer('Question', compute='_compute_question_number', store=True)
    
    # Field Type and Options
    field_type = fields.Selection([
        ('text', 'Text'),
        ('number', 'Number'),
        ('selection', 'Selection'),
        ('boolean', 'Yes/No'),
        ('date', 'Date'),
    ], required=True, default='text')
    
    # Visibility and Conditions
    visibility_condition = fields.Char('Visibility Condition', help='Python expression that determines if field is visible')
    visibility_condition_ids = fields.One2many('config.matrix.visibility.condition', 'field_id', string='Visibility Conditions')
    
    # Text Field Options
    min_length = fields.Integer('Minimum Length')
    max_length = fields.Integer('Maximum Length')
    pattern = fields.Char('Validation Pattern', help='Regular expression for validation')
    
    # Number Field Options
    decimal_precision = fields.Integer('Decimal Precision', default=3)
    uom_id = fields.Many2one('uom.uom', string='Unit of Measure')
    
    # Selection Field Options
    option_ids = fields.One2many('config.matrix.option', 'field_id', 'Options')
    is_searchable_dropdown = fields.Boolean('Searchable Dropdown', default=False)
    
    # Boolean Field Options
    boolean_true_label = fields.Char('True Label', default='Yes')
    boolean_false_label = fields.Char('False Label', default='No')
    
    # Component Mapping
    component_product_id = fields.Many2one('product.product', 'Component Product', help='Legacy single component')
    quantity_formula = fields.Char('Quantity Formula', default='1', help='Python expression to calculate quantity')
    component_mapping_ids = fields.One2many('config.matrix.field.component.mapping', 'field_id', 'Component Mappings')
    has_multiple_components = fields.Boolean('Has Multiple Components', compute='_compute_has_multiple_components', store=True)
    
    # Dynamic Help Text per Use Case
    check_measure_use_dynamic_help = fields.Boolean('Use Dynamic Help (Check Measure)', default=False)
    check_measure_dynamic_help_template = fields.Text('Dynamic Help Template (Check Measure)')
    sales_use_dynamic_help = fields.Boolean('Use Dynamic Help (Sales)', default=False)
    sales_dynamic_help_template = fields.Text('Dynamic Help Template (Sales)')
    online_use_dynamic_help = fields.Boolean('Use Dynamic Help (Online)', default=False)
    online_dynamic_help_template = fields.Text('Dynamic Help Template (Online)')
    
    # Dynamic Default Values per Use Case
    check_measure_use_dynamic_default = fields.Boolean('Use Dynamic Default (Check Measure)', default=False)
    check_measure_dynamic_default_template = fields.Text('Dynamic Default Template (Check Measure)')
    sales_use_dynamic_default = fields.Boolean('Use Dynamic Default (Sales)', default=False)
    sales_dynamic_default_template = fields.Text('Dynamic Default Template (Sales)')
    online_use_dynamic_default = fields.Boolean('Use Dynamic Default (Online)', default=False)
    online_dynamic_default_template = fields.Text('Dynamic Default Template (Online)')
    
    # Use Case Properties - Check Measure
    check_measure_visible = fields.Boolean('Check Measure: Visible', default=True)
    check_measure_min_value = fields.Char('Check Measure: Min Value')
    check_measure_max_value = fields.Char('Check Measure: Max Value')
    check_measure_default_value = fields.Char('Check Measure: Default Value')
    check_measure_default_option_id = fields.Many2one('config.matrix.option', string='Check Measure: Default Option')
    check_measure_help_text = fields.Text('Check Measure: Help Text')
    check_measure_use_dynamic_error = fields.Boolean('Use Dynamic Error (Check Measure)', default=False)
    check_measure_dynamic_error_template = fields.Text('Dynamic Error Template (Check Measure)')
    check_measure_error_condition = fields.Char('Error Condition (Check Measure)')
    check_measure_error_text = fields.Text('Check Measure: Error Message')
    
    # Use Case Properties - Sales/Quoting
    sales_visible = fields.Boolean('Sales: Visible', default=True)
    sales_min_value = fields.Char('Sales: Min Value')
    sales_max_value = fields.Char('Sales: Max Value')
    sales_default_value = fields.Char('Sales: Default Value')
    sales_default_option_id = fields.Many2one('config.matrix.option', string='Sales: Default Option')
    sales_help_text = fields.Text('Sales: Help Text')
    sales_use_dynamic_error = fields.Boolean('Use Dynamic Error (Sales)', default=False)
    sales_dynamic_error_template = fields.Text('Dynamic Error Template (Sales)')
    sales_error_condition = fields.Char('Error Condition (Sales)')
    sales_error_text = fields.Text('Sales: Error Message')
    
    # Use Case Properties - Online Sales
    online_visible = fields.Boolean('Online: Visible', default=True)
    online_min_value = fields.Char('Online: Min Value')
    online_max_value = fields.Char('Online: Max Value')
    online_default_value = fields.Char('Online: Default Value')
    online_default_option_id = fields.Many2one('config.matrix.option', string='Online: Default Option')
    online_help_text = fields.Text('Online: Help Text')
    online_use_dynamic_error = fields.Boolean('Use Dynamic Error (Online)', default=False)
    online_dynamic_error_template = fields.Text('Dynamic Error Template (Online)')
    online_error_condition = fields.Char('Error Condition (Online)')
    online_error_text = fields.Text('Online: Error Message')
    
    # Instructional Content
    instructional_image = fields.Binary('Instructional Image')
    instructional_image_filename = fields.Char('Image Filename')
    show_in_preview = fields.Boolean('Show in Preview Panel', default=False)
    
    # Dependencies
    dependency_ids = fields.One2many('config.matrix.dependency', 'field_id', string='Dependencies')
    
    # Related Fields
    matrix_id = fields.Many2one('config.matrix.template', related='section_id.matrix_id', string='Template', store=True)
    template_state = fields.Selection(related='matrix_id.state', string='Template State')
```

### 4. Matrix Option (`config.matrix.option`)

Selection choices for fields with individual component mappings.

```python
class ConfigMatrixOption(models.Model):
    _name = 'config.matrix.option'
    _description = 'Configuration Option'
    _order = 'sequence, id'

    name = fields.Char("Option Name", required=True)
    field_id = fields.Many2one('config.matrix.field', "Field", required=True, ondelete='cascade')
    sequence = fields.Integer("Sequence", default=10)
    value = fields.Char("Value", help="The actual value stored when this option is selected")
    description = fields.Text("Description")
    
    # Component Mapping
    component_product_id = fields.Many2one('product.product', "Component Product")
    quantity_formula = fields.Char("Quantity Formula", default="1")
    
    # Use Case Visibility
    check_measure_visible = fields.Boolean("Check Measure: Visible", default=True)
    sales_visible = fields.Boolean("Sales: Visible", default=True)
    online_visible = fields.Boolean("Online: Visible", default=True)
    
    # Related Fields
    matrix_id = fields.Many2one('config.matrix.template', related='field_id.matrix_id', string='Template', store=True)
```

### 5. Configuration (`config.matrix.configuration`)

Stores the actual configuration data and generated BOMs.

```python
class ConfigMatrixConfiguration(models.Model):
    _name = 'config.matrix.configuration'
    _description = 'Product Configuration'
    _order = 'create_date desc'

    name = fields.Char("Configuration Name", compute='_compute_name', store=True)
    product_id = fields.Many2one('product.product', "Product", required=True)
    template_id = fields.Many2one('config.matrix.template', "Configuration Template", required=True)
    sale_order_line_id = fields.Many2one('sale.order.line', "Sales Order Line")
    create_date = fields.Datetime("Created On", readonly=True)
    
    # Configuration Data
    config_data = fields.Text("Configuration Data", help="JSON string with all configuration values")
    
    # Generated BOM
    bom_id = fields.Many2one('mrp.bom', "Generated BOM")
    price_matrix = fields.Float("Price Matrix", digits='Product Price')
    price_component = fields.Float("Price Component", digits='Product Price')
    price = fields.Float("Calculated Price", digits='Product Price')
    
    # Configuration State
    state = fields.Selection([
        ('draft', 'Draft'),
        ('configured', 'Configured'),
        ('applied', 'Applied')
    ], default='draft', string="Status")
    
    # Computed Fields
    configuration_summary = fields.Text("Configuration Summary", compute="_compute_configuration_summary")
    bom_line_ids = fields.One2many('mrp.bom.line', compute='_compute_bom_line_ids', string="BOM Lines")
    
    # Related Fields
    sale_order_id = fields.Many2one('sale.order', related='sale_order_line_id.order_id', string="Sales Order", store=True)
    
    # Configuration Tracking
    parent_config_id = fields.Many2one('config.matrix.configuration', "Parent Configuration", copy=False, ondelete="set null")
    child_config_ids = fields.One2many('config.matrix.configuration', 'parent_config_id', string="Child Configurations")
    
    # Builder Portal Fields
    project_id = fields.Many2one('builder.project', "Builder Project", ondelete="set null", copy=True)
    configuration_slot = fields.Integer("Configuration Slot Number", default=0)
    product_category = fields.Selection([
        ('door', 'Door'),
        ('screen', 'Screen')
    ], string="Product Category")
```

## Advanced Models

### 6. Visibility Conditions (`config.matrix.visibility.condition`)

Complex visibility rules for fields and options.

```python
class ConfigMatrixVisibilityCondition(models.Model):
    _name = 'config.matrix.visibility.condition'
    _description = 'Field Visibility Condition'
    _order = 'sequence, id'

    name = fields.Char("Condition Name", required=True)
    field_id = fields.Many2one('config.matrix.field', "Field", ondelete='cascade')
    matrix_id = fields.Many2one('config.matrix.template', "Template", ondelete='cascade')
    sequence = fields.Integer("Sequence", default=10)
    
    # Condition Configuration
    condition_type = fields.Selection([
        ('simple', 'Simple Condition'),
        ('range', 'Range Condition'),
        ('complex', 'Complex Condition')
    ], default='simple', required=True)
    
    condition_expression = fields.Text("Condition Expression", required=True)
    condition_description = fields.Text("Description")
    
    # Use Case Support
    check_measure_active = fields.Boolean("Check Measure: Active", default=True)
    sales_active = fields.Boolean("Sales: Active", default=True)
    online_active = fields.Boolean("Online: Active", default=True)
    
    # Advanced Features
    is_range_condition = fields.Boolean("Is Range Condition", compute='_compute_is_range_condition')
    referenced_fields = fields.Char("Referenced Fields", compute='_compute_referenced_fields')
```

### 7. Option Visibility (`config.matrix.option.visibility`)

Visibility rules specifically for options within selection fields.

```python
class ConfigMatrixOptionVisibility(models.Model):
    _name = 'config.matrix.option.visibility'
    _description = 'Option Visibility Condition'
    _order = 'sequence, id'

    name = fields.Char("Condition Name", required=True)
    option_id = fields.Many2one('config.matrix.option', "Option", ondelete='cascade')
    matrix_id = fields.Many2one('config.matrix.template', "Template", ondelete='cascade')
    sequence = fields.Integer("Sequence", default=10)
    
    condition_expression = fields.Text("Condition Expression", required=True)
    condition_description = fields.Text("Description")
    
    # Use Case Support
    check_measure_active = fields.Boolean("Check Measure: Active", default=True)
    sales_active = fields.Boolean("Sales: Active", default=True)
    online_active = fields.Boolean("Online: Active", default=True)
```

### 8. Calculated Fields (`config.matrix.calculated.field`)

Formula-based computed fields with dependency tracking.

```python
class ConfigMatrixCalculatedField(models.Model):
    _name = 'config.matrix.calculated.field'
    _description = 'Calculated Field'
    _order = 'sequence, id'

    name = fields.Char("Field Name", required=True)
    technical_name = fields.Char("Technical Name", required=True)
    matrix_id = fields.Many2one('config.matrix.template', "Template", required=True, ondelete='cascade')
    sequence = fields.Integer("Sequence", default=10)
    
    # Formula Configuration
    formula = fields.Text("Formula", required=True, help="Python expression to calculate the field value")
    formula_description = fields.Text("Description")
    
    # Field Type
    field_type = fields.Selection([
        ('number', 'Number'),
        ('text', 'Text'),
        ('boolean', 'Boolean')
    ], default='number', required=True)
    
    # Display Configuration
    decimal_precision = fields.Integer("Decimal Precision", default=2)
    uom_id = fields.Many2one('uom.uom', "Unit of Measure")
    
    # Dependencies
    dependency_fields = fields.Char("Dependency Fields", compute='_compute_dependency_fields')
    
    # Use Case Support
    check_measure_visible = fields.Boolean("Check Measure: Visible", default=True)
    sales_visible = fields.Boolean("Sales: Visible", default=True)
    online_visible = fields.Boolean("Online: Visible", default=True)
```

## Component Mapping Models

### 9. Component Mapping (`config.matrix.component.mapping`)

Legacy single component mapping for templates.

```python
class ConfigMatrixComponentMapping(models.Model):
    _name = 'config.matrix.component.mapping'
    _description = 'Component Mapping'

    template_id = fields.Many2one('config.matrix.template', "Template", required=True, ondelete='cascade')
    component_product_id = fields.Many2one('product.product', "Component Product", required=True)
    quantity_formula = fields.Char("Quantity Formula", default="1", required=True)
    condition = fields.Char("Condition", help="Optional condition for when to include this component")
```

### 10. Field Component Mapping (`config.matrix.field.component.mapping`)

Advanced 1:many component mapping for fields.

```python
class ConfigMatrixFieldComponentMapping(models.Model):
    _name = 'config.matrix.field.component.mapping'
    _description = 'Field Component Mapping'
    _order = 'sequence, id'

    field_id = fields.Many2one('config.matrix.field', "Field", required=True, ondelete='cascade')
    sequence = fields.Integer("Sequence", default=10)
    
    # Component Selection
    component_product_id = fields.Many2one('product.product', "Component Product", required=True)
    quantity_formula = fields.Char("Quantity Formula", default="1", required=True)
    
    # Conditions
    condition = fields.Char("Condition", help="Python expression for when to include this component")
    condition_description = fields.Text("Condition Description")
    
    # Dynamic Product Selection
    use_dynamic_product = fields.Boolean("Use Dynamic Product Selection", default=False)
    reference_field = fields.Char("Reference Field", help="Field name to use for product selection")
    product_filter_domain = fields.Text("Product Filter Domain", help="Domain to filter products")
    
    # Use Case Support
    check_measure_active = fields.Boolean("Check Measure: Active", default=True)
    sales_active = fields.Boolean("Sales: Active", default=True)
    online_active = fields.Boolean("Online: Active", default=True)
```

### 11. Option Component Mapping (`config.matrix.option.component.mapping`)

Component mapping for specific options within selection fields.

```python
class ConfigMatrixOptionComponentMapping(models.Model):
    _name = 'config.matrix.option.component.mapping'
    _description = 'Option Component Mapping'
    _order = 'sequence, id'

    option_id = fields.Many2one('config.matrix.option', "Option", required=True, ondelete='cascade')
    template_id = fields.Many2one('config.matrix.template', "Template", required=True, ondelete='cascade')
    sequence = fields.Integer("Sequence", default=10)
    
    component_product_id = fields.Many2one('product.product', "Component Product", required=True)
    quantity_formula = fields.Char("Quantity Formula", default="1", required=True)
    condition = fields.Char("Condition", help="Additional condition for when to include this component")
    
    # Use Case Support
    check_measure_active = fields.Boolean("Check Measure: Active", default=True)
    sales_active = fields.Boolean("Sales: Active", default=True)
    online_active = fields.Boolean("Online: Active", default=True)
```

## Pricing Models

### 12. Price Matrix (`config.matrix.price.matrix`)

Multi-dimensional pricing based on product dimensions.

```python
class ConfigMatrixPriceMatrix(models.Model):
    _name = 'config.matrix.price.matrix'
    _description = 'Price Matrix for Configurable Products'
    _order = 'name, sequence'

    name = fields.Char("Matrix Name", required=True)
    description = fields.Text("Description")
    product_template_id = fields.Many2one('product.template', "Product Template", required=True)
    sequence = fields.Integer("Sequence", default=10)
    active = fields.Boolean("Active", default=True)
    
    # Matrix Configuration
    matrix_type = fields.Selection([
        ('price', 'Sale Price Matrix'),
        ('cost', 'Cost Matrix')
    ], string="Matrix Type", default='price', required=True)
    
    category_id = fields.Many2one('config.matrix.category', string="Matrix Category")
    
    # Dimension Ranges
    height_ranges = fields.Text("Height Ranges", help="JSON array of height ranges")
    width_ranges = fields.Text("Width Ranges", help="JSON array of width ranges")
    
    # Matrix Data
    matrix_data = fields.Text("Matrix Data", help="JSON object containing the matrix values")
    
    # Currency
    currency_id = fields.Many2one('res.currency', "Currency", default=lambda self: self.env.company.currency_id)
    
    # Special Conditions
    special_conditions = fields.Text("Special Conditions", help="JSON object for special conditions")
    
    # Import/Export
    last_imported = fields.Datetime("Last Imported")
    import_source = fields.Char("Import Source")
    
    # Statistics
    total_cells = fields.Integer("Total Cells", compute='_compute_matrix_stats')
    filled_cells = fields.Integer("Filled Cells", compute='_compute_matrix_stats')
    completion_rate = fields.Float("Completion Rate", compute='_compute_matrix_stats')
    height_count = fields.Integer("Height Count", compute='_compute_dimension_counts')
    width_count = fields.Integer("Width Count", compute='_compute_dimension_counts')
```

### 13. Labor Time Matrix (`config.matrix.labor.time.matrix`)

Labor time and cost calculations.

```python
class ConfigMatrixLaborTimeMatrix(models.Model):
    _name = 'config.matrix.labor.time.matrix'
    _description = 'Labor Time Matrix for Configurable Products'
    _order = 'name, sequence'

    name = fields.Char("Matrix Name", required=True)
    description = fields.Text("Description")
    product_template_id = fields.Many2one('product.template', "Product Template", required=True)
    sequence = fields.Integer("Sequence", default=10)
    active = fields.Boolean("Active", default=True)
    
    # Matrix Configuration
    category_id = fields.Many2one('config.matrix.category', string="Matrix Category")
    
    # Dimension Ranges
    height_ranges = fields.Text("Height Ranges", help="JSON array of height ranges")
    width_ranges = fields.Text("Width Ranges", help="JSON array of width ranges")
    
    # Matrix Data
    labor_time_data = fields.Text("Labor Time Data", help="JSON object containing labor times")
    labor_cost_data = fields.Text("Labor Cost Data", help="JSON object containing labor costs")
    
    # Labor Configuration
    labor_rate = fields.Float("Labor Rate per Hour", digits='Product Price')
    currency_id = fields.Many2one('res.currency', "Currency", default=lambda self: self.env.company.currency_id)
    
    # Statistics
    total_cells = fields.Integer("Total Cells", compute='_compute_matrix_stats')
    filled_cells = fields.Integer("Filled Cells", compute='_compute_matrix_stats')
    completion_rate = fields.Float("Completion Rate", compute='_compute_matrix_stats')
```

### 14. Matrix Category (`config.matrix.category`)

Organization of pricing matrices by type and purpose.

```python
class ConfigMatrixCategory(models.Model):
    _name = 'config.matrix.category'
    _description = 'Matrix Category'
    _order = 'sequence, name'

    name = fields.Char("Category Name", required=True)
    description = fields.Text("Description")
    sequence = fields.Integer("Sequence", default=10)
    active = fields.Boolean("Active", default=True)
    
    # Category Type
    category_type = fields.Selection([
        ('price', 'Price Matrix'),
        ('labor', 'Labor Matrix'),
        ('material', 'Material Matrix'),
        ('other', 'Other')
    ], default='price', required=True)
    
    # Relationships
    price_matrix_ids = fields.One2many('config.matrix.price.matrix', 'category_id', string="Price Matrices")
    labor_matrix_ids = fields.One2many('config.matrix.labor.time.matrix', 'category_id', string="Labor Matrices")
    
    # Statistics
    matrix_count = fields.Integer("Matrix Count", compute='_compute_matrix_count')
```

## Visual Models

### 15. SVG Component (`config.matrix.svg.component`)

Vector-based visual representations for products.

```python
class ConfigMatrixSvgComponent(models.Model):
    _name = 'config.matrix.svg.component'
    _description = 'SVG Component'
    _order = 'sequence, name'

    name = fields.Char("Component Name", required=True)
    template_id = fields.Many2one('config.matrix.template', "Template", required=True, ondelete='cascade')
    sequence = fields.Integer("Sequence", default=10)
    
    # SVG Configuration
    svg_content = fields.Text("SVG Content", required=True)
    svg_width = fields.Integer("SVG Width", default=400)
    svg_height = fields.Integer("SVG Height", default=300)
    
    # Visibility Conditions
    visibility_condition = fields.Char("Visibility Condition", help="Python expression for when to show this component")
    
    # Layer Configuration
    layer_name = fields.Char("Layer Name", help="Name of the SVG layer")
    z_index = fields.Integer("Z-Index", default=0, help="Display order of layers")
    
    # Use Case Support
    check_measure_visible = fields.Boolean("Check Measure: Visible", default=True)
    sales_visible = fields.Boolean("Sales: Visible", default=True)
    online_visible = fields.Boolean("Online: Visible", default=True)
```

## Integration Models

### 16. Sale Order Line Extension (`sale.order.line`)

Integration with sales workflow.

```python
class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    # Configuration Fields
    is_configurable = fields.Boolean("Is Configurable", compute='_compute_is_configurable', store=True)
    is_configured = fields.Boolean("Is Configured", default=False)
    configuration_id = fields.Many2one('config.matrix.configuration', "Configuration")
    
    # Configuration Data
    config_data = fields.Text("Configuration Data", help="JSON string with configuration values")
    configuration_summary = fields.Text("Configuration Summary", compute='_compute_configuration_summary')
    
    # Pricing
    config_price = fields.Float("Configuration Price", digits='Product Price')
    config_cost = fields.Float("Configuration Cost", digits='Product Price')
    
    # BOM Integration
    config_bom_id = fields.Many2one('mrp.bom', "Configuration BOM")
```

### 17. Product Template Extension (`product.template`)

Product configuration flags and relationships.

```python
class ProductTemplate(models.Model):
    _inherit = 'product.template'

    # Configuration Flags
    is_configurable = fields.Boolean("Is Configurable", default=False)
    matrix_id = fields.Many2one('config.matrix.template', "Configuration Template")
    
    # Configuration Properties
    config_requires_measurement = fields.Boolean("Requires Measurement", default=False)
    config_has_visual_preview = fields.Boolean("Has Visual Preview", default=False)
    
    # Pricing Integration
    use_matrix_pricing = fields.Boolean("Use Matrix Pricing", default=False)
    price_matrix_ids = fields.Many2many('config.matrix.price.matrix', string="Price Matrices")
    labor_matrix_ids = fields.Many2many('config.matrix.labor.time.matrix', string="Labor Matrices")
```

## Administrative Models

### 18. Builder Project (`builder.project`)

Professional builder portal projects.

```python
class BuilderProject(models.Model):
    _name = 'builder.project'
    _description = 'Builder Project'
    _order = 'name'

    name = fields.Char("Project Name", required=True)
    description = fields.Text("Description")
    partner_id = fields.Many2one('res.partner', "Customer", required=True)
    
    # Project Configuration
    project_type = fields.Selection([
        ('residential', 'Residential'),
        ('commercial', 'Commercial'),
        ('industrial', 'Industrial')
    ], default='residential')
    
    # Configuration Slots
    configuration_ids = fields.One2many('config.matrix.configuration', 'project_id', string="Configurations")
    max_configurations = fields.Integer("Maximum Configurations", default=10)
    
    # Project State
    state = fields.Selection([
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled')
    ], default='draft')
```

## Model Relationships

### Core Relationships
- **Template** → **Sections** → **Fields** → **Options**
- **Template** → **Component Mappings** (1:many)
- **Template** → **SVG Components** (1:many)
- **Template** → **Price Matrices** (many:many)
- **Template** → **Labor Matrices** (many:many)

### Advanced Relationships
- **Fields** → **Component Mappings** (1:many)
- **Options** → **Component Mappings** (1:many)
- **Fields** → **Visibility Conditions** (1:many)
- **Options** → **Visibility Conditions** (1:many)
- **Template** → **Calculated Fields** (1:many)

### Integration Relationships
- **Product Template** → **Configuration Template** (1:1)
- **Sale Order Line** → **Configuration** (1:1)
- **Configuration** → **BOM** (1:1)
- **Builder Project** → **Configurations** (1:many)

## Data Flow

### Configuration Process
1. **Template Definition**: Admin creates template with sections, fields, and rules
2. **Component Mapping**: Components mapped to fields and options
3. **Pricing Setup**: Price and labor matrices configured
4. **Visual Setup**: SVG components and instructional materials added
5. **Testing**: Template tested across all use cases
6. **Deployment**: Template activated for production use

### Configuration Execution
1. **User Selection**: User selects configurable product
2. **Template Loading**: Relevant template loaded based on use case
3. **Field Rendering**: Fields rendered with visibility conditions applied
4. **User Input**: User provides configuration values
5. **Dynamic Updates**: Fields show/hide based on user input
6. **Validation**: Values validated against rules and constraints
7. **BOM Generation**: Components and quantities calculated
8. **Pricing**: Price calculated from matrices and components
9. **Save**: Configuration saved and applied to sales order

### Manufacturing Integration
1. **Order Processing**: Configured products flow to manufacturing
2. **BOM Creation**: Manufacturing BOM created from configuration
3. **Component Quantities**: Quantities calculated from formulas
4. **Labor Calculation**: Labor times and costs calculated
5. **Production**: Manufacturing order created with all details

This comprehensive data model architecture supports all the advanced features of the ConfigMatrix system while maintaining flexibility for future enhancements.
