# ConfigMatrix: User Interface Architecture

This document outlines the comprehensive user interface architecture for the ConfigMatrix system, including frontend components, OWL framework usage, and user experience patterns.

## Frontend Architecture Overview

The ConfigMatrix frontend is built using Odoo 18's modern OWL (Odoo Web Library) framework, providing a responsive, interactive, and performant user experience across all use cases.

### Core Technologies
- **OWL Framework**: Modern JavaScript component framework
- **Bootstrap 5**: Responsive CSS framework for layout and styling
- **SVG Rendering**: Vector graphics for visual product previews
- **Real-time Validation**: Dynamic form validation and error handling
- **Performance Optimization**: Caching, debouncing, and memory management

## Main Components

### 1. Configurator Widget (`configurator.js`)

The primary configuration interface that handles all user interactions and state management.

```javascript
/** @odoo-module **/
import { registry } from "@web/core/registry";
import { Component, onWillStart, useState, onMounted, onWillUnmount } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";
import { standardWidgetProps } from "@web/views/widgets/standard_widget_props";

class ConfigMatrixConfigurator extends Component {
    setup() {
        // State Management
        this.state = useState({
            template: null,
            sections: [],
            values: {},
            loading: true,
            saving: false,
            error: null,
            currentSection: 0,
            dirty: false,
            fieldVisibility: {},
            hiddenFieldsWithValues: []
        });

        // Performance Optimizations
        this.expressionCache = new Map();
        this.maxCacheSize = 1000;
        this.debouncedUpdateVisibility = this.debounce(this.updateVisibilityAll.bind(this), 100);
        this.debouncedUpdateDynamicLabels = this.debounce(this.updateDynamicLabels.bind(this), 200);
        this.debouncedUpdateDynamicDefaults = this.debounce(this.updateDynamicDefaults.bind(this), 150);

        // Dependency Management
        this.dependencyGraph = new Map();
        this.reverseDependencyGraph = new Map();
        this.allFields = new Map();

        // Performance Monitoring
        this.performanceMetrics = {
            fieldChanges: 0,
            evaluations: 0,
            cacheHits: 0,
            cacheMisses: 0,
            totalTime: 0,
            dependencyUpdates: 0
        };

        onWillStart(async () => {
            await this.loadTemplate();
        });

        onMounted(() => {
            this.buildDependencyGraph();
            this.updateVisibilityAll();
            this.syncStateToDOM();
            this.startMemoryManagement();
        });

        onWillUnmount(() => {
            this.stopMemoryManagement();
            this.clearExpressionCache();
        });
    }

    // Core Methods
    async loadTemplate(useCase = null) {
        // Load template data from backend
    }

    buildDependencyGraph() {
        // Build field dependency relationships
    }

    updateVisibilityAll() {
        // Update visibility for all fields
    }

    onFieldChange(field, value) {
        // Handle field value changes
    }

    async saveConfiguration() {
        // Save configuration to backend
    }
}
```

### 2. Field Rendering Components

#### Text Field Component
```javascript
class ConfigMatrixTextField extends Component {
    setup() {
        this.state = useState({
            value: this.props.field.default_value || '',
            error: null,
            helpText: this.props.field.help_text || ''
        });
    }

    onInput(event) {
        const value = event.target.value;
        this.state.value = value;
        this.validateField(value);
        this.props.onChange(this.props.field.technical_name, value);
    }

    validateField(value) {
        const field = this.props.field;
        
        // Length validation
        if (field.min_length && value.length < field.min_length) {
            this.state.error = `Minimum length is ${field.min_length} characters`;
            return false;
        }
        
        if (field.max_length && value.length > field.max_length) {
            this.state.error = `Maximum length is ${field.max_length} characters`;
            return false;
        }
        
        // Pattern validation
        if (field.pattern && !new RegExp(field.pattern).test(value)) {
            this.state.error = 'Value does not match required pattern';
            return false;
        }
        
        this.state.error = null;
        return true;
    }
}
```

#### Number Field Component
```javascript
class ConfigMatrixNumberField extends Component {
    setup() {
        this.state = useState({
            value: this.props.field.default_value || '',
            error: null,
            formattedValue: ''
        });
    }

    onInput(event) {
        const value = event.target.value;
        this.state.value = value;
        this.formatValue(value);
        this.validateField(value);
        this.props.onChange(this.props.field.technical_name, parseFloat(value) || 0);
    }

    formatValue(value) {
        const field = this.props.field;
        if (field.decimal_precision !== undefined) {
            this.state.formattedValue = parseFloat(value).toFixed(field.decimal_precision);
        } else {
            this.state.formattedValue = value;
        }
    }

    validateField(value) {
        const field = this.props.field;
        const numValue = parseFloat(value);
        
        // Dynamic min/max validation
        const minValue = this.evaluateDynamicValue(field.min_value);
        const maxValue = this.evaluateDynamicValue(field.max_value);
        
        if (minValue !== null && numValue < minValue) {
            this.state.error = `Minimum value is ${minValue}`;
            return false;
        }
        
        if (maxValue !== null && numValue > maxValue) {
            this.state.error = `Maximum value is ${maxValue}`;
            return false;
        }
        
        this.state.error = null;
        return true;
    }
}
```

#### Selection Field Component
```javascript
class ConfigMatrixSelectionField extends Component {
    setup() {
        this.state = useState({
            value: this.props.field.default_value || '',
            error: null,
            filteredOptions: this.props.field.options || []
        });
    }

    onSelect(event) {
        const value = event.target.value;
        this.state.value = value;
        this.props.onChange(this.props.field.technical_name, value);
    }

    onSearch(event) {
        const searchTerm = event.target.value.toLowerCase();
        const options = this.props.field.options || [];
        
        this.state.filteredOptions = options.filter(option => 
            option.name.toLowerCase().includes(searchTerm) ||
            option.value.toLowerCase().includes(searchTerm)
        );
    }

    renderOption(option) {
        const isSelected = this.state.value === option.value;
        const isVisible = this.isOptionVisible(option);
        
        if (!isVisible) return null;
        
        return (
            <option value={option.value} selected={isSelected}>
                {option.name}
            </option>
        );
    }
}
```

#### Boolean Field Component
```javascript
class ConfigMatrixBooleanField extends Component {
    setup() {
        this.state = useState({
            value: this.props.field.default_value || false,
            error: null
        });
    }

    onToggle() {
        this.state.value = !this.state.value;
        this.props.onChange(this.props.field.technical_name, this.state.value);
    }

    render() {
        const field = this.props.field;
        const trueLabel = field.boolean_true_label || 'Yes';
        const falseLabel = field.boolean_false_label || 'No';
        
        return (
            <div class="config-boolean-field">
                <label class="form-label">{field.name}</label>
                <div class="btn-group" role="group">
                    <input type="radio" 
                           class="btn-check" 
                           name={field.technical_name}
                           id={`${field.technical_name}_true`}
                           checked={this.state.value === true}
                           onChange={() => this.onToggle()} />
                    <label class="btn btn-outline-primary" for={`${field.technical_name}_true`}>
                        {trueLabel}
                    </label>
                    
                    <input type="radio" 
                           class="btn-check" 
                           name={field.technical_name}
                           id={`${field.technical_name}_false`}
                           checked={this.state.value === false}
                           onChange={() => this.onToggle()} />
                    <label class="btn btn-outline-secondary" for={`${field.technical_name}_false`}>
                        {falseLabel}
                    </label>
                </div>
            </div>
        );
    }
}
```

### 3. Visibility Conditions Engine

Advanced visibility management with dependency tracking and performance optimization.

```javascript
class VisibilityEngine {
    constructor() {
        this.conditionCache = new Map();
        this.dependencyGraph = new Map();
        this.reverseDependencyGraph = new Map();
    }

    evaluateVisibility(field, fieldValues) {
        const cacheKey = this.getCacheKey(field, fieldValues);
        
        // Check cache first
        if (this.conditionCache.has(cacheKey)) {
            return this.conditionCache.get(cacheKey);
        }

        // Evaluate condition
        const result = this.evaluateCondition(field.visibility_condition, fieldValues);
        
        // Cache result
        this.conditionCache.set(cacheKey, result);
        
        // Cleanup cache if too large
        if (this.conditionCache.size > 1000) {
            this.cleanupCache();
        }
        
        return result;
    }

    evaluateCondition(condition, fieldValues) {
        if (!condition) return true;
        
        try {
            // Create safe evaluation context
            const context = {
                ...fieldValues,
                // Add math functions
                abs: Math.abs,
                ceil: Math.ceil,
                floor: Math.floor,
                round: Math.round,
                min: Math.min,
                max: Math.max,
                // Add string functions
                len: (str) => str ? str.length : 0,
                upper: (str) => str ? str.toUpperCase() : '',
                lower: (str) => str ? str.toLowerCase() : '',
                // Add logical functions
                and: (...args) => args.every(Boolean),
                or: (...args) => args.some(Boolean),
                not: (val) => !val
            };
            
            // Convert condition to safe JavaScript
            const safeCondition = this.convertToSafeJS(condition);
            return eval(safeCondition);
        } catch (error) {
            console.error('Error evaluating visibility condition:', error);
            return true; // Default to visible
        }
    }

    convertToSafeJS(condition) {
        // Convert Python-like syntax to JavaScript
        return condition
            .replace(/\band\b/g, '&&')
            .replace(/\bor\b/g, '||')
            .replace(/\bnot\b/g, '!')
            .replace(/\bTrue\b/g, 'true')
            .replace(/\bFalse\b/g, 'false')
            .replace(/\bNone\b/g, 'null');
    }

    updateDependencies(changedField, newValue) {
        const dependentFields = this.dependencyGraph.get(changedField) || [];
        
        for (const field of dependentFields) {
            // Clear cache for dependent fields
            this.clearFieldCache(field);
            
            // Trigger visibility update
            this.triggerVisibilityUpdate(field);
        }
    }
}
```

### 4. Dynamic Content Engine

Handles dynamic help text, error messages, and default values using field placeholders.

```javascript
class DynamicContentEngine {
    constructor() {
        this.templateCache = new Map();
    }

    generateDynamicHelp(field, fieldValues, useCase) {
        const template = this.getHelpTemplate(field, useCase);
        if (!template) return field.help_text || '';
        
        return this.evaluateTemplate(template, fieldValues);
    }

    generateDynamicError(field, fieldValues, useCase) {
        const template = this.getErrorTemplate(field, useCase);
        if (!template) return field.error_text || '';
        
        return this.evaluateTemplate(template, fieldValues);
    }

    generateDynamicDefault(field, fieldValues, useCase) {
        const template = this.getDefaultTemplate(field, useCase);
        if (!template) return field.default_value || '';
        
        return this.evaluateTemplate(template, fieldValues);
    }

    evaluateTemplate(template, fieldValues) {
        return template.replace(/\{([^}]+)\}/g, (match, fieldName) => {
            const value = fieldValues[fieldName];
            return value !== undefined ? value : match;
        });
    }

    getHelpTemplate(field, useCase) {
        switch (useCase) {
            case 'check_measure':
                return field.check_measure_use_dynamic_help ? field.check_measure_dynamic_help_template : null;
            case 'sales':
                return field.sales_use_dynamic_help ? field.sales_dynamic_help_template : null;
            case 'online':
                return field.online_use_dynamic_help ? field.online_dynamic_help_template : null;
            default:
                return null;
        }
    }
}
```

### 5. SVG Rendering Engine

Vector-based visual components with conditional layers and dynamic updates.

```javascript
class SVGRenderer {
    constructor(container) {
        this.container = container;
        this.svgElement = null;
        this.layers = new Map();
        this.conditionalLayers = new Map();
    }

    initializeSVG(width, height) {
        this.svgElement = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        this.svgElement.setAttribute('width', width);
        this.svgElement.setAttribute('height', height);
        this.svgElement.setAttribute('viewBox', `0 0 ${width} ${height}`);
        this.container.appendChild(this.svgElement);
    }

    addLayer(layerName, svgContent, zIndex = 0) {
        const layer = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        layer.setAttribute('id', `layer-${layerName}`);
        layer.setAttribute('style', `z-index: ${zIndex}`);
        layer.innerHTML = svgContent;
        this.svgElement.appendChild(layer);
        this.layers.set(layerName, layer);
    }

    addConditionalLayer(layerName, svgContent, condition, zIndex = 0) {
        this.conditionalLayers.set(layerName, {
            content: svgContent,
            condition: condition,
            zIndex: zIndex
        });
    }

    updateConditionalLayers(fieldValues) {
        // Remove existing conditional layers
        this.conditionalLayers.forEach((layerInfo, layerName) => {
            const existingLayer = this.layers.get(layerName);
            if (existingLayer) {
                existingLayer.remove();
                this.layers.delete(layerName);
            }
        });

        // Add visible conditional layers
        this.conditionalLayers.forEach((layerInfo, layerName) => {
            if (this.evaluateCondition(layerInfo.condition, fieldValues)) {
                this.addLayer(layerName, layerInfo.content, layerInfo.zIndex);
            }
        });
    }

    updateLayerVisibility(layerName, visible) {
        const layer = this.layers.get(layerName);
        if (layer) {
            layer.style.display = visible ? 'block' : 'none';
        }
    }

    animateLayer(layerName, animation) {
        const layer = this.layers.get(layerName);
        if (layer) {
            layer.style.transition = animation.transition || 'all 0.3s ease';
            layer.style.transform = animation.transform || '';
            layer.style.opacity = animation.opacity || 1;
        }
    }
}
```

### 6. Performance Optimization Components

#### Expression Caching
```javascript
class ExpressionCache {
    constructor(maxSize = 1000) {
        this.cache = new Map();
        this.maxSize = maxSize;
        this.accessTimes = new Map();
    }

    get(key) {
        if (this.cache.has(key)) {
            this.accessTimes.set(key, Date.now());
            return this.cache.get(key);
        }
        return null;
    }

    set(key, value) {
        if (this.cache.size >= this.maxSize) {
            this.evictOldest();
        }
        
        this.cache.set(key, value);
        this.accessTimes.set(key, Date.now());
    }

    evictOldest() {
        let oldestKey = null;
        let oldestTime = Date.now();
        
        for (const [key, time] of this.accessTimes) {
            if (time < oldestTime) {
                oldestTime = time;
                oldestKey = key;
            }
        }
        
        if (oldestKey) {
            this.cache.delete(oldestKey);
            this.accessTimes.delete(oldestKey);
        }
    }

    clear() {
        this.cache.clear();
        this.accessTimes.clear();
    }
}
```

#### Debounced Updates
```javascript
class DebouncedUpdater {
    constructor() {
        this.timers = new Map();
    }

    debounce(key, func, delay) {
        if (this.timers.has(key)) {
            clearTimeout(this.timers.get(key));
        }
        
        const timer = setTimeout(() => {
            func();
            this.timers.delete(key);
        }, delay);
        
        this.timers.set(key, timer);
    }

    cancel(key) {
        if (this.timers.has(key)) {
            clearTimeout(this.timers.get(key));
            this.timers.delete(key);
        }
    }

    cancelAll() {
        for (const timer of this.timers.values()) {
            clearTimeout(timer);
        }
        this.timers.clear();
    }
}
```

### 7. Form Validation Engine

Comprehensive validation with real-time feedback and error handling.

```javascript
class ValidationEngine {
    constructor() {
        this.validators = new Map();
        this.errors = new Map();
        this.warnings = new Map();
    }

    addValidator(fieldName, validator) {
        if (!this.validators.has(fieldName)) {
            this.validators.set(fieldName, []);
        }
        this.validators.get(fieldName).push(validator);
    }

    validateField(fieldName, value, fieldConfig) {
        const validators = this.validators.get(fieldName) || [];
        const errors = [];
        const warnings = [];

        for (const validator of validators) {
            const result = validator(value, fieldConfig);
            if (result.error) {
                errors.push(result.error);
            }
            if (result.warning) {
                warnings.push(result.warning);
            }
        }

        // Store results
        this.errors.set(fieldName, errors);
        this.warnings.set(fieldName, warnings);

        return {
            isValid: errors.length === 0,
            errors: errors,
            warnings: warnings
        };
    }

    validateAll(fieldValues, fieldConfigs) {
        const results = {};
        let isValid = true;

        for (const [fieldName, value] of Object.entries(fieldValues)) {
            const fieldConfig = fieldConfigs[fieldName];
            const result = this.validateField(fieldName, value, fieldConfig);
            results[fieldName] = result;
            
            if (!result.isValid) {
                isValid = false;
            }
        }

        return {
            isValid: isValid,
            results: results
        };
    }

    getFieldErrors(fieldName) {
        return this.errors.get(fieldName) || [];
    }

    getFieldWarnings(fieldName) {
        return this.warnings.get(fieldName) || [];
    }

    clearFieldValidation(fieldName) {
        this.errors.delete(fieldName);
        this.warnings.delete(fieldName);
    }

    clearAllValidation() {
        this.errors.clear();
        this.warnings.clear();
    }
}
```

## User Experience Patterns

### 1. Progressive Disclosure

Fields are shown/hidden based on user input, creating a guided experience:

```javascript
class ProgressiveDisclosure {
    constructor(configurator) {
        this.configurator = configurator;
        this.visibleFields = new Set();
        this.pendingFields = new Set();
    }

    updateFieldVisibility(fieldName, isVisible) {
        const fieldElement = document.querySelector(`[data-field="${fieldName}"]`);
        if (fieldElement) {
            if (isVisible) {
                fieldElement.style.display = 'block';
                fieldElement.classList.add('field-visible');
                fieldElement.classList.remove('field-hidden');
                this.visibleFields.add(fieldName);
            } else {
                fieldElement.style.display = 'none';
                fieldElement.classList.add('field-hidden');
                fieldElement.classList.remove('field-visible');
                this.visibleFields.delete(fieldName);
            }
        }
    }

    animateFieldTransition(fieldName, isVisible) {
        const fieldElement = document.querySelector(`[data-field="${fieldName}"]`);
        if (fieldElement) {
            if (isVisible) {
                fieldElement.style.opacity = '0';
                fieldElement.style.display = 'block';
                fieldElement.style.transform = 'translateY(-10px)';
                
                setTimeout(() => {
                    fieldElement.style.transition = 'all 0.3s ease';
                    fieldElement.style.opacity = '1';
                    fieldElement.style.transform = 'translateY(0)';
                }, 10);
            } else {
                fieldElement.style.transition = 'all 0.3s ease';
                fieldElement.style.opacity = '0';
                fieldElement.style.transform = 'translateY(-10px)';
                
                setTimeout(() => {
                    fieldElement.style.display = 'none';
                }, 300);
            }
        }
    }
}
```

### 2. Real-time Feedback

Immediate validation and feedback as users interact with fields:

```javascript
class RealTimeFeedback {
    constructor(validationEngine) {
        this.validationEngine = validationEngine;
        this.feedbackElements = new Map();
    }

    showFieldError(fieldName, error) {
        const fieldElement = document.querySelector(`[data-field="${fieldName}"]`);
        if (fieldElement) {
            const errorElement = fieldElement.querySelector('.field-error') || 
                               this.createErrorElement(fieldElement);
            
            errorElement.textContent = error;
            errorElement.style.display = 'block';
            fieldElement.classList.add('has-error');
        }
    }

    showFieldWarning(fieldName, warning) {
        const fieldElement = document.querySelector(`[data-field="${fieldName}"]`);
        if (fieldElement) {
            const warningElement = fieldElement.querySelector('.field-warning') || 
                                 this.createWarningElement(fieldElement);
            
            warningElement.textContent = warning;
            warningElement.style.display = 'block';
            fieldElement.classList.add('has-warning');
        }
    }

    clearFieldFeedback(fieldName) {
        const fieldElement = document.querySelector(`[data-field="${fieldName}"]`);
        if (fieldElement) {
            const errorElement = fieldElement.querySelector('.field-error');
            const warningElement = fieldElement.querySelector('.field-warning');
            
            if (errorElement) {
                errorElement.style.display = 'none';
            }
            if (warningElement) {
                warningElement.style.display = 'none';
            }
            
            fieldElement.classList.remove('has-error', 'has-warning');
        }
    }

    createErrorElement(fieldElement) {
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error text-danger mt-1';
        errorElement.style.fontSize = '0.875rem';
        fieldElement.appendChild(errorElement);
        return errorElement;
    }

    createWarningElement(fieldElement) {
        const warningElement = document.createElement('div');
        warningElement.className = 'field-warning text-warning mt-1';
        warningElement.style.fontSize = '0.875rem';
        fieldElement.appendChild(warningElement);
        return warningElement;
    }
}
```

### 3. Visual Progress Indicators

Show configuration progress and guide users through the process:

```javascript
class ProgressIndicator {
    constructor(container) {
        this.container = container;
        this.currentStep = 0;
        this.totalSteps = 0;
        this.progressBar = null;
        this.stepIndicators = [];
    }

    initialize(totalSteps) {
        this.totalSteps = totalSteps;
        this.createProgressBar();
        this.createStepIndicators();
    }

    createProgressBar() {
        this.progressBar = document.createElement('div');
        this.progressBar.className = 'progress mb-3';
        this.progressBar.innerHTML = `
            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
        `;
        this.container.appendChild(this.progressBar);
    }

    createStepIndicators() {
        const stepContainer = document.createElement('div');
        stepContainer.className = 'step-indicators d-flex justify-content-between mb-4';
        
        for (let i = 0; i < this.totalSteps; i++) {
            const stepIndicator = document.createElement('div');
            stepIndicator.className = 'step-indicator';
            stepIndicator.innerHTML = `
                <div class="step-number">${i + 1}</div>
                <div class="step-label">Step ${i + 1}</div>
            `;
            stepContainer.appendChild(stepIndicator);
            this.stepIndicators.push(stepIndicator);
        }
        
        this.container.appendChild(stepContainer);
    }

    updateProgress(currentStep) {
        this.currentStep = currentStep;
        const progress = (currentStep / this.totalSteps) * 100;
        
        // Update progress bar
        const progressBarElement = this.progressBar.querySelector('.progress-bar');
        progressBarElement.style.width = `${progress}%`;
        progressBarElement.setAttribute('aria-valuenow', progress);
        
        // Update step indicators
        this.stepIndicators.forEach((indicator, index) => {
            if (index < currentStep) {
                indicator.classList.add('completed');
                indicator.classList.remove('current', 'pending');
            } else if (index === currentStep) {
                indicator.classList.add('current');
                indicator.classList.remove('completed', 'pending');
            } else {
                indicator.classList.add('pending');
                indicator.classList.remove('completed', 'current');
            }
        });
    }
}
```

## Responsive Design

### Mobile-First Approach
```css
/* Base styles for mobile */
.configurator-container {
    padding: 1rem;
}

.config-field {
    margin-bottom: 1.5rem;
}

.config-field label {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.config-field input,
.config-field select,
.config-field textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    font-size: 1rem;
}

/* Tablet styles */
@media (min-width: 768px) {
    .configurator-container {
        padding: 2rem;
    }
    
    .config-field {
        margin-bottom: 2rem;
    }
}

/* Desktop styles */
@media (min-width: 1024px) {
    .configurator-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 3rem;
    }
    
    .config-field {
        margin-bottom: 2.5rem;
    }
    
    .config-field input,
    .config-field select,
    .config-field textarea {
        max-width: 400px;
    }
}
```

### Accessibility Features
```javascript
class AccessibilityManager {
    constructor() {
        this.focusableElements = [];
        this.currentFocusIndex = 0;
    }

    setupKeyboardNavigation() {
        document.addEventListener('keydown', (event) => {
            switch (event.key) {
                case 'Tab':
                    this.handleTabNavigation(event);
                    break;
                case 'Enter':
                    this.handleEnterKey(event);
                    break;
                case 'Escape':
                    this.handleEscapeKey(event);
                    break;
            }
        });
    }

    handleTabNavigation(event) {
        const focusableElements = this.getFocusableElements();
        const currentIndex = focusableElements.indexOf(document.activeElement);
        
        if (event.shiftKey) {
            // Shift+Tab: move backward
            const prevIndex = currentIndex > 0 ? currentIndex - 1 : focusableElements.length - 1;
            focusableElements[prevIndex].focus();
        } else {
            // Tab: move forward
            const nextIndex = currentIndex < focusableElements.length - 1 ? currentIndex + 1 : 0;
            focusableElements[nextIndex].focus();
        }
        
        event.preventDefault();
    }

    getFocusableElements() {
        return Array.from(document.querySelectorAll(
            'input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), [tabindex]:not([tabindex="-1"])'
        )).filter(el => el.offsetParent !== null); // Only visible elements
    }

    announceToScreenReader(message) {
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', 'polite');
        announcement.setAttribute('aria-atomic', 'true');
        announcement.className = 'sr-only';
        announcement.textContent = message;
        
        document.body.appendChild(announcement);
        
        setTimeout(() => {
            document.body.removeChild(announcement);
        }, 1000);
    }
}
```

## Performance Monitoring

### Real-time Performance Tracking
```javascript
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            renderTime: 0,
            updateTime: 0,
            memoryUsage: 0,
            cacheHitRate: 0,
            fieldChanges: 0,
            visibilityUpdates: 0
        };
        
        this.startTime = Date.now();
        this.measurements = [];
    }

    startMeasurement(name) {
        return {
            name: name,
            startTime: performance.now()
        };
    }

    endMeasurement(measurement) {
        const duration = performance.now() - measurement.startTime;
        this.measurements.push({
            name: measurement.name,
            duration: duration,
            timestamp: Date.now()
        });
        
        // Update metrics
        this.updateMetrics(measurement.name, duration);
        
        // Log slow operations
        if (duration > 100) {
            console.warn(`Slow operation detected: ${measurement.name} took ${duration.toFixed(2)}ms`);
        }
    }

    updateMetrics(operation, duration) {
        switch (operation) {
            case 'render':
                this.metrics.renderTime = duration;
                break;
            case 'update':
                this.metrics.updateTime = duration;
                break;
            case 'visibility':
                this.metrics.visibilityUpdates++;
                break;
            case 'fieldChange':
                this.metrics.fieldChanges++;
                break;
        }
    }

    getPerformanceReport() {
        const uptime = Date.now() - this.startTime;
        const avgRenderTime = this.measurements
            .filter(m => m.name === 'render')
            .reduce((sum, m) => sum + m.duration, 0) / this.measurements.length || 0;
        
        return {
            uptime: uptime,
            totalOperations: this.measurements.length,
            averageRenderTime: avgRenderTime,
            memoryUsage: this.metrics.memoryUsage,
            cacheHitRate: this.metrics.cacheHitRate,
            fieldChanges: this.metrics.fieldChanges,
            visibilityUpdates: this.metrics.visibilityUpdates
        };
    }
}
```

This comprehensive user interface architecture provides a modern, responsive, and performant experience for the ConfigMatrix system, supporting all advanced features while maintaining excellent usability across different devices and use cases. 