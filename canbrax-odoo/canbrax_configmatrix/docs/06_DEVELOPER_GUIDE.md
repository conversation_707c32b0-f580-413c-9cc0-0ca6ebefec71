# ConfigMatrix: Developer Guide

This comprehensive developer guide provides technical information for extending and customizing the ConfigMatrix system.

## Architecture Overview

ConfigMatrix is built on a modular architecture with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Layer (OWL)                     │
├─────────────────────────────────────────────────────────────┤
│  Configurator Widget  │  Field Components  │  SVG Renderer  │
├─────────────────────────────────────────────────────────────┤
│                   Controller Layer                          │
├─────────────────────────────────────────────────────────────┤
│  Main Controller  │  Portal Controller  │  Website Controller │
├─────────────────────────────────────────────────────────────┤
│                    Model Layer                              │
├─────────────────────────────────────────────────────────────┤
│  Core Models  │  Pricing Models  │  Component Models  │  Visual Models │
├─────────────────────────────────────────────────────────────┤
│                   Integration Layer                         │
├─────────────────────────────────────────────────────────────┤
│  Sales Integration  │  Manufacturing Integration  │  Portal Integration │
└─────────────────────────────────────────────────────────────┘
```

## Core Development Patterns

### 1. Model Inheritance Patterns

#### Template Extension
```python
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    
    # Use computed fields for performance
    @api.depends('section_ids', 'section_ids.field_ids')
    def _compute_field_count(self):
        for template in self:
            template.field_count = sum(len(section.field_ids) for section in template.section_ids)
    
    # Use selection_add for extending existing selections
    state = fields.Selection(selection_add=[
        ('archived', 'Archived')
    ], ondelete={'archived': 'set draft'})
    
    # Use related fields for efficient queries
    product_name = fields.Char(related='product_template_id.name', store=True)
    
    # Use constraints for data integrity
    _sql_constraints = [
        ('unique_code', 'unique(code)', 'Template code must be unique!')
    ]
```

#### Field Extension
```python
class ConfigMatrixField(models.Model):
    _name = 'config.matrix.field'
    
    # Use computed fields with proper dependencies
    @api.depends('sequence', 'section_id.sequence')
    def _compute_question_number(self):
        for field in self:
            if field.section_id:
                # Calculate question number based on sequence
                field.question_number = field.section_id.sequence * 100 + field.sequence
    
    # Use onchange for immediate UI updates
    @api.onchange('field_type')
    def _onchange_field_type(self):
        if self.field_type == 'selection':
            self.is_searchable_dropdown = True
        else:
            self.is_searchable_dropdown = False
    
    # Use constraints for validation
    @api.constrains('technical_name')
    def _check_technical_name(self):
        for field in self:
            if not field.technical_name.isidentifier():
                raise ValidationError(_("Technical name must be a valid Python identifier"))
```

### 2. Controller Patterns

#### RESTful API Design
```python
class ConfigMatrixController(http.Controller):
    
    @http.route('/config_matrix/api/template/<int:template_id>', 
                type='json', auth='user', methods=['GET'])
    def get_template(self, template_id, **kw):
        """Get template data with caching"""
        try:
            template = request.env['config.matrix.template'].browse(template_id)
            if not template.exists():
                return {'error': 'Template not found'}, 404
            
            # Use caching for performance
            cache_key = f'template_{template_id}_{template.write_date}'
            cached_data = request.env['ir.cache'].get(cache_key)
            if cached_data:
                return cached_data
            
            data = template.get_template_structure()
            request.env['ir.cache'].set(cache_key, data, timeout=3600)
            return data
            
        except Exception as e:
            _logger.error(f"Error getting template {template_id}: {str(e)}")
            return {'error': 'Internal server error'}, 500
    
    @http.route('/config_matrix/api/configuration', 
                type='json', auth='user', methods=['POST'])
    def save_configuration(self, **kw):
        """Save configuration with validation"""
        try:
            # Validate input data
            required_fields = ['template_id', 'config_data']
            for field in required_fields:
                if field not in kw:
                    return {'error': f'Missing required field: {field}'}, 400
            
            # Create configuration
            config_data = {
                'template_id': kw['template_id'],
                'config_data': json.dumps(kw['config_data']),
                'state': 'configured'
            }
            
            configuration = request.env['config.matrix.configuration'].create(config_data)
            
            # Generate BOM
            bom = configuration.generate_bom()
            
            return {
                'success': True,
                'configuration_id': configuration.id,
                'bom_id': bom.id if bom else None
            }
            
        except ValidationError as e:
            return {'error': str(e)}, 400
        except Exception as e:
            _logger.error(f"Error saving configuration: {str(e)}")
            return {'error': 'Internal server error'}, 500
```

#### Portal Integration
```python
class ConfigMatrixPortalController(http.Controller):
    
    @http.route(['/my/configurations'], type='http', auth="user", website=True)
    def portal_my_configurations(self, **kw):
        """Portal view for user configurations"""
        try:
            # Get user's configurations
            configurations = request.env['config.matrix.configuration'].search([
                ('create_uid', '=', request.env.user.id)
            ])
            
            values = {
                'configurations': configurations,
                'page_name': 'configurations',
            }
            
            return request.render('canbrax_configmatrix.portal_my_configurations', values)
            
        except Exception as e:
            _logger.error(f"Error in portal configurations: {str(e)}")
            return request.redirect('/my')
```

### 3. Frontend Component Patterns

#### OWL Component Structure
```javascript
/** @odoo-module **/
import { Component, onWillStart, useState, onMounted, onWillUnmount } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";

export class ConfigMatrixField extends Component {
    static template = 'canbrax_configmatrix.FieldTemplate';
    static props = {
        field: { type: Object },
        value: { type: [String, Number, Boolean], optional: true },
        onChange: { type: Function },
        onValidation: { type: Function, optional: true }
    };

    setup() {
        // State management
        this.state = useState({
            value: this.props.value || '',
            error: null,
            helpText: '',
            isVisible: true
        });

        // Services
        this.rpc = useService("rpc");
        this.notification = useService("notification");

        // Performance optimization
        this.debouncedValidation = this.debounce(this.validateField.bind(this), 300);
        this.cachedValidation = new Map();

        onWillStart(async () => {
            await this.initializeField();
        });

        onMounted(() => {
            this.setupEventListeners();
        });

        onWillUnmount(() => {
            this.cleanupEventListeners();
        });
    }

    async initializeField() {
        try {
            // Load dynamic content
            if (this.props.field.use_dynamic_help) {
                this.state.helpText = await this.generateDynamicHelp();
            }

            // Set initial visibility
            this.state.isVisible = this.evaluateVisibility();
        } catch (error) {
            console.error('Error initializing field:', error);
        }
    }

    onInput(event) {
        const value = event.target.value;
        this.state.value = value;
        
        // Trigger change callback
        this.props.onChange(this.props.field.technical_name, value);
        
        // Debounced validation
        this.debouncedValidation(value);
    }

    async validateField(value) {
        const cacheKey = `${this.props.field.id}_${value}`;
        
        // Check cache first
        if (this.cachedValidation.has(cacheKey)) {
            this.state.error = this.cachedValidation.get(cacheKey);
            return;
        }

        try {
            const result = await this.rpc('/config_matrix/validate_field', {
                field_id: this.props.field.id,
                value: value
            });

            this.state.error = result.error || null;
            this.cachedValidation.set(cacheKey, this.state.error);
        } catch (error) {
            console.error('Validation error:', error);
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}
```

#### Template Structure
```xml
<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="canbrax_configmatrix.FieldTemplate">
        <div class="config-field" t-att-class="{'field-hidden': !state.isVisible}">
            <!-- Field Label -->
            <label class="form-label" t-esc="props.field.name"/>
            
            <!-- Help Text -->
            <div class="field-help" t-if="state.helpText">
                <i class="fa fa-info-circle"/>
                <span t-esc="state.helpText"/>
            </div>
            
            <!-- Field Input -->
            <div class="field-input">
                <t t-call="canbrax_configmatrix.FieldInput"/>
            </div>
            
            <!-- Error Message -->
            <div class="field-error text-danger" t-if="state.error">
                <i class="fa fa-exclamation-triangle"/>
                <span t-esc="state.error"/>
            </div>
        </div>
    </t>
</templates>
```

### 4. Performance Optimization Patterns

#### Database Query Optimization
```python
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    
    def get_template_structure_optimized(self, use_case=None):
        """Optimized template structure retrieval with prefetching"""
        self.ensure_one()
        
        # Use prefetch to avoid N+1 queries
        self.env.cr.execute("""
            SELECT 
                t.id as template_id,
                t.name as template_name,
                s.id as section_id,
                s.name as section_name,
                s.sequence as section_sequence,
                f.id as field_id,
                f.name as field_name,
                f.technical_name,
                f.field_type,
                f.sequence as field_sequence,
                o.id as option_id,
                o.name as option_name,
                o.value as option_value
            FROM config_matrix_template t
            LEFT JOIN config_matrix_section s ON s.matrix_id = t.id
            LEFT JOIN config_matrix_field f ON f.section_id = s.id
            LEFT JOIN config_matrix_option o ON o.field_id = f.id
            WHERE t.id = %s
            ORDER BY s.sequence, f.sequence, o.sequence
        """, (self.id,))
        
        # Process results efficiently
        sections = {}
        for row in self.env.cr.dictfetchall():
            section_id = row['section_id']
            if section_id not in sections:
                sections[section_id] = {
                    'id': section_id,
                    'name': row['section_name'],
                    'sequence': row['section_sequence'],
                    'fields': {}
                }
            
            field_id = row['field_id']
            if field_id and field_id not in sections[section_id]['fields']:
                sections[section_id]['fields'][field_id] = {
                    'id': field_id,
                    'name': row['field_name'],
                    'technical_name': row['technical_name'],
                    'field_type': row['field_type'],
                    'sequence': row['field_sequence'],
                    'options': []
                }
            
            if row['option_id']:
                sections[section_id]['fields'][field_id]['options'].append({
                    'id': row['option_id'],
                    'name': row['option_name'],
                    'value': row['option_value']
                })
        
        return {
            'id': self.id,
            'name': self.name,
            'sections': list(sections.values())
        }
```

#### Caching Strategies
```python
from odoo.tools import ormcache

class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    
    @ormcache('template_id', 'use_case')
    def get_template_structure_cached(self, template_id, use_case=None):
        """Cached template structure retrieval"""
        template = self.browse(template_id)
        return template.get_template_structure_optimized(use_case)
    
    @ormcache('field_id')
    def get_field_options_cached(self, field_id):
        """Cached field options retrieval"""
        field = self.env['config.matrix.field'].browse(field_id)
        return [(opt.id, opt.name, opt.value) for opt in field.option_ids]
    
    def clear_template_cache(self, template_id):
        """Clear cache when template is modified"""
        self.get_template_structure_cached.clear_cache(self)
        self.get_field_options_cached.clear_cache(self)
```

#### Frontend Performance
```javascript
class PerformanceOptimizer {
    constructor() {
        this.cache = new Map();
        this.debounceTimers = new Map();
        this.observer = null;
    }

    // Virtual scrolling for large lists
    setupVirtualScrolling(container, items, itemHeight = 50) {
        const visibleCount = Math.ceil(container.clientHeight / itemHeight);
        let startIndex = 0;
        
        const renderVisibleItems = () => {
            const endIndex = Math.min(startIndex + visibleCount, items.length);
            const visibleItems = items.slice(startIndex, endIndex);
            
            container.innerHTML = '';
            visibleItems.forEach((item, index) => {
                const element = this.createItemElement(item, startIndex + index);
                element.style.position = 'absolute';
                element.style.top = `${(startIndex + index) * itemHeight}px`;
                container.appendChild(element);
            });
        };
        
        container.addEventListener('scroll', () => {
            const scrollTop = container.scrollTop;
            startIndex = Math.floor(scrollTop / itemHeight);
            renderVisibleItems();
        });
        
        renderVisibleItems();
    }

    // Lazy loading for images and heavy content
    setupLazyLoading() {
        this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    const src = element.dataset.src;
                    if (src) {
                        element.src = src;
                        element.removeAttribute('data-src');
                        this.observer.unobserve(element);
                    }
                }
            });
        });

        document.querySelectorAll('[data-src]').forEach(element => {
            this.observer.observe(element);
        });
    }

    // Memory management
    cleanup() {
        if (this.observer) {
            this.observer.disconnect();
        }
        this.cache.clear();
        this.debounceTimers.forEach(timer => clearTimeout(timer));
        this.debounceTimers.clear();
    }
}
```

### 5. Security Patterns

#### Access Control
```python
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    
    # Record rules for access control
    def _check_access_rights(self, operation):
        """Custom access control logic"""
        if operation == 'read':
            return True  # Everyone can read templates
        elif operation == 'write':
            # Only admins and template owners can modify
            return self.env.user.has_group('canbrax_configmatrix.group_config_matrix_admin') or \
                   self.create_uid == self.env.user
        elif operation == 'create':
            # Only admins can create templates
            return self.env.user.has_group('canbrax_configmatrix.group_config_matrix_admin')
        return False
    
    @api.model
    def search(self, domain, offset=0, limit=None, order=None, count=False):
        """Override search to apply access control"""
        if not self.env.user.has_group('canbrax_configmatrix.group_config_matrix_admin'):
            # Regular users can only see active templates
            domain = expression.AND([domain, [('state', '=', 'active')]])
        
        return super().search(domain, offset=offset, limit=limit, order=order, count=count)
```

#### Input Validation
```python
import re
from odoo.exceptions import ValidationError

class ConfigMatrixField(models.Model):
    _name = 'config.matrix.field'
    
    @api.constrains('technical_name')
    def _validate_technical_name(self):
        """Validate technical name format"""
        for field in self:
            if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', field.technical_name):
                raise ValidationError(_("Technical name must be a valid Python identifier"))
    
    @api.constrains('visibility_condition')
    def _validate_visibility_condition(self):
        """Validate visibility condition syntax"""
        for field in self:
            if field.visibility_condition:
                try:
                    # Test compilation of condition
                    compile(field.visibility_condition, '<string>', 'eval')
                except SyntaxError:
                    raise ValidationError(_("Invalid visibility condition syntax"))
    
    def sanitize_input(self, value):
        """Sanitize user input"""
        if isinstance(value, str):
            # Remove potentially dangerous characters
            value = re.sub(r'[<>"\']', '', value)
            # Limit length
            if len(value) > 1000:
                value = value[:1000]
        return value
```

#### CSRF Protection
```python
class ConfigMatrixController(http.Controller):
    
    @http.route('/config_matrix/save_config', 
                type='http', auth='user', methods=['POST'], csrf=True)
    def save_configuration(self, **kw):
        """Save configuration with CSRF protection"""
        try:
            # Validate CSRF token
            if not request.httprequest.headers.get('X-CSRFToken'):
                return {'error': 'CSRF token missing'}, 403
            
            # Process configuration
            config_data = self._validate_config_data(kw)
            configuration = self._create_configuration(config_data)
            
            return {'success': True, 'id': configuration.id}
            
        except ValidationError as e:
            return {'error': str(e)}, 400
        except Exception as e:
            _logger.error(f"Error saving configuration: {str(e)}")
            return {'error': 'Internal server error'}, 500
    
    def _validate_config_data(self, data):
        """Validate configuration data"""
        required_fields = ['template_id', 'values']
        for field in required_fields:
            if field not in data:
                raise ValidationError(f"Missing required field: {field}")
        
        # Validate template exists and user has access
        template = request.env['config.matrix.template'].browse(int(data['template_id']))
        if not template.exists():
            raise ValidationError("Template not found")
        
        if not template._check_access_rights('read'):
            raise ValidationError("Access denied")
        
        return data
```

### 6. Testing Patterns

#### Unit Tests
```python
from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError

class TestConfigMatrixTemplate(TransactionCase):
    
    def setUp(self):
        super().setUp()
        self.template = self.env['config.matrix.template'].create({
            'name': 'Test Template',
            'code': 'TEST001',
            'product_template_id': self.env['product.template'].create({
                'name': 'Test Product'
            }).id
        })
    
    def test_template_creation(self):
        """Test template creation with valid data"""
        self.assertEqual(self.template.name, 'Test Template')
        self.assertEqual(self.template.code, 'TEST001')
        self.assertEqual(self.template.state, 'draft')
    
    def test_template_validation(self):
        """Test template validation rules"""
        # Test duplicate code
        with self.assertRaises(ValidationError):
            self.env['config.matrix.template'].create({
                'name': 'Duplicate Template',
                'code': 'TEST001',  # Same code
                'product_template_id': self.env['product.template'].create({
                    'name': 'Another Product'
                }).id
            })
    
    def test_template_lifecycle(self):
        """Test template state transitions"""
        # Draft -> Testing
        self.template.action_set_to_testing()
        self.assertEqual(self.template.state, 'testing')
        
        # Testing -> Active
        self.template.action_set_to_active()
        self.assertEqual(self.template.state, 'active')
        
        # Active -> Archived
        self.template.action_archive()
        self.assertEqual(self.template.state, 'archived')
```

#### Integration Tests
```python
class TestConfigMatrixIntegration(TransactionCase):
    
    def setUp(self):
        super().setUp()
        self.setup_test_data()
    
    def setup_test_data(self):
        """Setup test data for integration tests"""
        # Create product
        self.product = self.env['product.template'].create({
            'name': 'Test Product',
            'is_configurable': True
        })
        
        # Create template
        self.template = self.env['config.matrix.template'].create({
            'name': 'Test Template',
            'code': 'TEST001',
            'product_template_id': self.product.id
        })
        
        # Create section
        self.section = self.env['config.matrix.section'].create({
            'name': 'Test Section',
            'matrix_id': self.template.id
        })
        
        # Create field
        self.field = self.env['config.matrix.field'].create({
            'name': 'Test Field',
            'technical_name': 'test_field',
            'field_type': 'text',
            'section_id': self.section.id
        })
    
    def test_configuration_workflow(self):
        """Test complete configuration workflow"""
        # Create configuration
        config_data = {
            'test_field': 'Test Value'
        }
        
        configuration = self.env['config.matrix.configuration'].create({
            'product_id': self.product.product_variant_id.id,
            'template_id': self.template.id,
            'config_data': json.dumps(config_data)
        })
        
        # Test BOM generation
        bom = configuration.generate_bom()
        self.assertIsNotNone(bom)
        
        # Test price calculation
        price = configuration.calculate_price()
        self.assertIsInstance(price, float)
    
    def test_visibility_conditions(self):
        """Test field visibility conditions"""
        # Create dependent field
        dependent_field = self.env['config.matrix.field'].create({
            'name': 'Dependent Field',
            'technical_name': 'dependent_field',
            'field_type': 'text',
            'section_id': self.section.id,
            'visibility_condition': 'test_field == "show"'
        })
        
        # Test visibility logic
        config_values = {'test_field': 'show'}
        is_visible = dependent_field.evaluate_visibility(config_values)
        self.assertTrue(is_visible)
        
        config_values = {'test_field': 'hide'}
        is_visible = dependent_field.evaluate_visibility(config_values)
        self.assertFalse(is_visible)
```

#### Frontend Tests
```javascript
/** @odoo-module **/
import { makeTestEnv } from "@web/../tests/helpers/mock_env";
import { mount } from "@web/../tests/helpers/utils";
import { ConfigMatrixField } from "../src/js/configurator";

QUnit.module("ConfigMatrix", () => {
    QUnit.test("Field component renders correctly", async (assert) => {
        const env = await makeTestEnv();
        
        const field = {
            id: 1,
            name: "Test Field",
            technical_name: "test_field",
            field_type: "text",
            help_text: "Test help text"
        };
        
        const component = await mount(ConfigMatrixField, {
            env,
            props: {
                field: field,
                value: "",
                onChange: () => {}
            }
        });
        
        assert.containsOnce(component, ".config-field");
        assert.containsOnce(component, ".form-label");
        assert.strictEqual(component.querySelector(".form-label").textContent, "Test Field");
    });
    
    QUnit.test("Field validation works", async (assert) => {
        const env = await makeTestEnv();
        
        const field = {
            id: 1,
            name: "Test Field",
            technical_name: "test_field",
            field_type: "text",
            min_length: 3,
            max_length: 10
        };
        
        const component = await mount(ConfigMatrixField, {
            env,
            props: {
                field: field,
                value: "",
                onChange: () => {},
                onValidation: () => {}
            }
        });
        
        // Test too short
        await component.querySelector("input").setValue("ab");
        assert.containsOnce(component, ".field-error");
        
        // Test valid length
        await component.querySelector("input").setValue("valid");
        assert.containsNone(component, ".field-error");
        
        // Test too long
        await component.querySelector("input").setValue("toolongvalue");
        assert.containsOnce(component, ".field-error");
    });
});
```

### 7. Extension Patterns

#### Custom Field Types
```python
class ConfigMatrixCustomField(models.Model):
    _name = 'config.matrix.custom.field'
    _inherit = 'config.matrix.field'
    
    # Add custom field properties
    custom_property = fields.Char("Custom Property")
    custom_config = fields.Text("Custom Configuration")
    
    def get_custom_widget_data(self):
        """Return data for custom widget"""
        return {
            'type': 'custom',
            'custom_property': self.custom_property,
            'custom_config': json.loads(self.custom_config) if self.custom_config else {}
        }
```

#### Custom Widget Component
```javascript
/** @odoo-module **/
import { ConfigMatrixField } from "./configurator";

export class ConfigMatrixCustomField extends ConfigMatrixField {
    static template = 'canbrax_configmatrix.CustomFieldTemplate';
    
    setup() {
        super.setup();
        this.customConfig = this.props.field.custom_config || {};
    }
    
    async onCustomAction() {
        // Custom action logic
        const result = await this.rpc('/config_matrix/custom_action', {
            field_id: this.props.field.id,
            data: this.state.value
        });
        
        if (result.success) {
            this.notification.add(result.message, { type: 'success' });
        }
    }
}
```

#### Custom Template
```xml
<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="canbrax_configmatrix.CustomFieldTemplate" t-inherit="canbrax_configmatrix.FieldTemplate" t-inherit-mode="extension">
        <xpath expr="//div[@class='field-input']" position="replace">
            <div class="field-input">
                <div class="custom-widget">
                    <button class="btn btn-primary" t-on-click="onCustomAction">
                        Custom Action
                    </button>
                    <div class="custom-display" t-esc="state.value"/>
                </div>
            </div>
        </xpath>
    </t>
</templates>
```

### 8. Deployment Patterns

#### Environment Configuration
```python
# config.py
class ConfigMatrixConfig:
    """Configuration management for ConfigMatrix"""
    
    def __init__(self, env):
        self.env = env
    
    @property
    def max_fields_per_template(self):
        """Maximum fields per template"""
        return int(self.env['ir.config_parameter'].sudo().get_param(
            'canbrax_configmatrix.max_fields_per_template', '100'
        ))
    
    @property
    def enable_caching(self):
        """Enable caching for performance"""
        return self.env['ir.config_parameter'].sudo().get_param(
            'canbrax_configmatrix.enable_caching', 'True'
        ).lower() == 'true'
    
    @property
    def cache_timeout(self):
        """Cache timeout in seconds"""
        return int(self.env['ir.config_parameter'].sudo().get_param(
            'canbrax_configmatrix.cache_timeout', '3600'
        ))
```

#### Performance Monitoring
```python
import time
import logging
from contextlib import contextmanager

_logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """Performance monitoring for ConfigMatrix"""
    
    def __init__(self):
        self.metrics = {}
    
    @contextmanager
    def measure(self, operation_name):
        """Context manager for measuring operation performance"""
        start_time = time.time()
        try:
            yield
        finally:
            duration = time.time() - start_time
            self.record_metric(operation_name, duration)
    
    def record_metric(self, operation, duration):
        """Record performance metric"""
        if operation not in self.metrics:
            self.metrics[operation] = []
        
        self.metrics[operation].append(duration)
        
        # Log slow operations
        if duration > 1.0:  # More than 1 second
            _logger.warning(f"Slow operation detected: {operation} took {duration:.2f}s")
    
    def get_average_time(self, operation):
        """Get average time for operation"""
        if operation not in self.metrics:
            return 0
        
        times = self.metrics[operation]
        return sum(times) / len(times) if times else 0
    
    def get_performance_report(self):
        """Generate performance report"""
        report = {}
        for operation, times in self.metrics.items():
            report[operation] = {
                'count': len(times),
                'average': sum(times) / len(times),
                'min': min(times),
                'max': max(times)
            }
        return report

# Global performance monitor
performance_monitor = PerformanceMonitor()
```

This comprehensive developer guide provides the technical foundation for extending and customizing the ConfigMatrix system, following Odoo 18 best practices and modern development patterns.
