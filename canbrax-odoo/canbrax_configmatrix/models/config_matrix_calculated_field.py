# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.tools.safe_eval import safe_eval
import json
import logging

_logger = logging.getLogger(__name__)


class ConfigMatrixCalculatedField(models.Model):
    _name = 'config.matrix.calculated.field'
    _description = 'Calculated Field Definition'
    _order = 'sequence, name'

    name = fields.Char("Field Name", required=True, help="The calculated field name (e.g., _CALCULATED_smallest_door_height)")
    description = fields.Text("Description", help="Description of what this field calculates")
    formula = fields.Text("Formula", required=True, help="JavaScript expression to calculate the field value")
    sequence = fields.Integer("Sequence", default=10, help="Order of calculation (lower numbers calculated first)")
    active = fields.Boolean("Active", default=True)
    
    # Categorization
    category = fields.Selection([
        ('input', 'Input Fields'),
        ('basic', 'Basic Derived Values'),
        ('threshold', 'Height Thresholds'),
        ('range', 'Range Validity'),
        ('condition', 'Lock Height Conditions'),
        ('composite', 'Composite Conditions'),
        ('kit', 'Kit Results'),
        ('final', 'Final Results')
    ], string="Category", default='basic', help="Category for organization")
    
    # Dependencies
    depends_on = fields.Text("Depends On", help="Comma-separated list of field names this calculation depends on")
    
    # Template association (Many2Many)
    template_ids = fields.Many2many('config.matrix.template', 'calc_field_template_rel',
                                   'calc_field_id', 'template_id',
                                   string="Templates", help="Templates this field applies to (leave empty for global)")
    
    # Data type
    data_type = fields.Selection([
        ('number', 'Number'),
        ('boolean', 'Boolean'),
        ('string', 'String')
    ], string="Data Type", default='number', help="Expected data type of the result")

    # Price Matrix fields
    is_price_matrix = fields.Boolean('Is Price Matrix', default=False,
                                     help='If checked, this field is a price matrix. It will be displayed in the price matrices panel.')


    @api.constrains('name')
    def _check_name_format(self):
        for record in self:
            if not record.name.startswith('_CALCULATED_'):
                raise ValueError("Calculated field names must start with '_CALCULATED_'")

    def action_load_configuration_data(self):
        """Load data from selected configuration"""
        self.ensure_one()
        if not self.test_configuration_id:
            return

        try:
            # Load configuration data
            config_data = json.loads(self.test_configuration_id.config_data or '{}')

            # Convert field IDs to technical names for better readability
            converted_data = self._convert_field_ids_to_technical_names(config_data)

            # Format as pretty JSON
            self.test_values = json.dumps(converted_data, indent=2, sort_keys=True)
            self.test_error = False

        except json.JSONDecodeError as e:
            self.test_error = f"Invalid configuration data: {str(e)}"
        except Exception as e:
            self.test_error = f"Error loading configuration: {str(e)}"

    def _convert_field_ids_to_technical_names(self, config_data):
        """Convert field IDs to technical names for better readability"""
        if not config_data:
            return {}

        converted_data = {}

        # Get the template from the test configuration
        template = self.test_configuration_id.template_id if self.test_configuration_id else None
        if not template:
            # If no template, return original data
            return config_data

        # Create a mapping of field IDs to technical names
        field_mapping = {}
        for section in template.section_ids:
            for field in section.field_ids:
                field_mapping[str(field.id)] = field.technical_name

        # Convert the data
        for key, value in config_data.items():
            if key in field_mapping:
                # Use technical name instead of field ID
                technical_name = field_mapping[key]
                converted_data[technical_name] = value
                print(f"🔄 Converted field ID {key} → {technical_name} = {value}")
            else:
                # Keep original key if no mapping found
                converted_data[key] = value

        return converted_data

    def _convert_js_to_python(self, js_formula):
        """Convert JavaScript formula syntax to Python syntax"""
        if not js_formula:
            return js_formula

        python_formula = js_formula

        # Convert JavaScript operators to Python
        python_formula = python_formula.replace('===', '==')
        python_formula = python_formula.replace('!==', '!=')
        python_formula = python_formula.replace('&&', ' and ')
        python_formula = python_formula.replace('||', ' or ')

        # Convert JavaScript Math.min/max to Python min/max
        python_formula = python_formula.replace('Math.min(', 'min(')
        python_formula = python_formula.replace('Math.max(', 'max(')
        python_formula = python_formula.replace('Math.', '')

        # Convert JavaScript logical OR with default value (|| 0) to Python equivalent
        # Note: This needs to be after the general || conversion
        python_formula = python_formula.replace(' or 0', ' or 0')  # This is already correct

        # Convert JavaScript string concatenation to Python
        python_formula = self._convert_string_concatenation(python_formula)

        # Convert ternary operator using a more robust approach
        python_formula = self._convert_ternary_operators(python_formula)

        return python_formula

    def _convert_string_concatenation(self, formula):
        """Convert JavaScript string concatenation to Python string formatting"""
        import re
        
        # More comprehensive approach: find all variables that are being concatenated
        # and wrap them with str()
        
        # First, let's find all variable names in the formula
        variable_pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*)\b'
        variables = re.findall(variable_pattern, formula)
        
        # For each variable, check if it's being concatenated with a string
        for variable in variables:
            # Skip if it's already wrapped with str()
            if f'str({variable})' in formula:
                continue
                
            # Check if this variable is being concatenated with a string
            # Pattern: variable + 'string' or 'string' + variable
            concat_pattern = rf'\b{variable}\b\s*\+\s*[\'"][^\'"]*[\'"]|[\'"][^\'"]*[\'"]\s*\+\s*\b{variable}\b'
            
            if re.search(concat_pattern, formula):
                # Replace the variable with str(variable)
                formula = re.sub(rf'\b{variable}\b', f'str({variable})', formula)
        
        return formula

    def _convert_ternary_operators(self, formula):
        """Convert JavaScript ternary operators to Python if-else expressions"""
        
        def convert_nested_ternary_manual(expr):
            """Convert nested ternary operators manually for the specific pattern"""
            expr = expr.strip()
            
            # Handle the specific pattern: condition1 ? true1 : (condition2 ? true2 : false2)
            # For the specific case we're dealing with, let's handle it manually
            
            # Look for the pattern: " ? " followed by something, then " : " followed by something with another " ? "
            import re
            
            # First, let's try to find the nested ternary pattern
            # Look for: condition ? true : (nested_condition ? nested_true : nested_false)
            
            # Find the first question mark
            first_question = expr.find('?')
            if first_question == -1:
                return expr
            
            # Find the colon after the first question mark
            first_colon = expr.find(':', first_question)
            if first_colon == -1:
                return expr
            
            # Check if there's a nested ternary in the false part
            false_part_start = first_colon + 1
            false_part = expr[false_part_start:].strip()
            
            # Look for nested ternary in the false part
            if false_part.startswith('(') and '?' in false_part and ':' in false_part:
                # Extract the parts
                condition1 = expr[:first_question].strip()
                true1 = expr[first_question + 1:first_colon].strip()
                
                # Remove the outer parentheses from the false part
                nested_expr = false_part[1:-1] if false_part.startswith('(') and false_part.endswith(')') else false_part
                
                # Find the nested ternary
                nested_question = nested_expr.find('?')
                nested_colon = nested_expr.find(':', nested_question)
                
                if nested_question != -1 and nested_colon != -1:
                    condition2 = nested_expr[:nested_question].strip()
                    true2 = nested_expr[nested_question + 1:nested_colon].strip()
                    false2 = nested_expr[nested_colon + 1:].strip()
                    
                    # Convert to Python if-else
                    python_expr = f"({true1} if ({condition1}) else ({true2} if ({condition2}) else {false2}))"
                    return python_expr
            
            # If no nested pattern found, try simple ternary
            condition = expr[:first_question].strip()
            true_part = expr[first_question + 1:first_colon].strip()
            false_part = expr[first_colon + 1:].strip()
            
            # Convert to Python if-else
            python_expr = f"({true_part} if ({condition}) else {false_part})"
            return python_expr
        
        # Only process if there are ternary operators
        if '?' in formula and ':' in formula:
            try:
                return convert_nested_ternary_manual(formula)
            except Exception as e:
                print(f"⚠️ Ternary conversion failed: {e}, using fallback")
                # Fallback to simple replacement
                formula = formula.replace(' ? ', ' if ')
                formula = formula.replace(' : ', ' else ')
                return formula
        
        return formula









    @api.model
    def get_calculated_fields_for_template(self, template_id=None):
        """Get all calculated fields for a template, ordered by sequence"""
        domain = [('active', '=', True)]
        if template_id:
            domain.append('|')
            domain.append(('template_ids', 'in', [template_id]))
            domain.append(('template_ids', '=', False))
        else:
            domain.append(('template_ids', '=', False))

        fields = self.search(domain, order='sequence, name')

        # Return field data for JavaScript
        return [{
            'name': field.name,
            'formula': field.formula,
            'description': field.description,
            'sequence': field.sequence,
            'category': field.category,
            'data_type': field.data_type,
            'is_price_matrix': field.is_price_matrix
        } for field in fields]

    @api.model
    def calculate_fields(self, field_values, template_id=None):
        """Calculate all calculated fields for given input values"""
        calculated_fields = self.get_calculated_fields_for_template(template_id)
        results = {}
        
        # Create evaluation context
        context = dict(field_values)
        
        # Add math functions (safe_eval compatible - no module objects)
        import math
        math_context = {
            'min': min,
            'max': max,
            'abs': abs,
            'round': round,
            'ceil': math.ceil,
            'floor': math.floor,
            'sqrt': math.sqrt,
            'pow': math.pow,
            'log': math.log,
            'log10': math.log10,
            'exp': math.exp,
            'sin': math.sin,
            'cos': math.cos,
            'tan': math.tan,
            'pi': math.pi,
            'e': math.e
        }
        # Add individual math functions (no module object)
        for attr_name in dir(math):
            if not attr_name.startswith('_'):
                attr_value = getattr(math, attr_name)
                if callable(attr_value) or isinstance(attr_value, (int, float)):
                    try:
                        math_context[attr_name] = attr_value
                    except:
                        pass  # Skip any problematic attributes
        context.update(math_context)
        
        # Add JavaScript-compatible functions
        def parseFloat(value):
            """JavaScript parseFloat equivalent"""
            if value is None or value == '':
                return 0
            try:
                return float(str(value))
            except:
                return 0

        def parseInt(value):
            """JavaScript parseInt equivalent"""
            if value is None or value == '':
                return 0
            try:
                return int(float(str(value)))
            except:
                return 0

        # Add JavaScript functions to context
        context.update({
            'parseFloat': parseFloat,
            'parseInt': parseInt,
        })

        # Calculate fields in sequence order
        for calc_field in calculated_fields:
            try:
                # Add previously calculated fields to context
                context.update(results)

                # Convert JavaScript formula to Python syntax
                python_formula = self._convert_js_to_python(calc_field['formula'])

                # Evaluate the formula
                result = safe_eval(python_formula, context)
                results[calc_field['name']] = result

                _logger.debug(f"Calculated {calc_field['name']} = {result}")

            except Exception as e:
                _logger.error(f"Error calculating {calc_field['name']}: {str(e)}")
                results[calc_field['name']] = None
                
        return results

    @api.model
    def import_calculated_fields_from_json(self, json_data, template_ids=None):
        """Import calculated fields from JSON definition"""
        try:
            if isinstance(json_data, str):
                data = json.loads(json_data)
            else:
                data = json_data

            created_count = 0
            updated_count = 0

            for field_name, field_def in data.items():
                # Check if field already exists (global fields)
                existing = self.search([
                    ('name', '=', field_name),
                    ('template_ids', '=', False)
                ], limit=1)

                # Determine category and sequence from field name
                category = 'basic'
                sequence = 10  # Default sequence

                # Set sequence based on dependencies to ensure proper calculation order
                if field_name in ['_CALCULATED_manual_left_height', '_CALCULATED_manual_right_height',
                                '_CALCULATED_lock_height', '_CALCULATED_deduction_assistance',
                                '_CALCULATED_door_split_type']:
                    # Input fields - calculate first
                    category = 'input'
                    sequence = 1
                elif field_name in ['_CALCULATED_height_calculation_method']:
                    # Method calculation - depends on input fields
                    category = 'input'
                    sequence = 2
                elif field_name in ['_CALCULATED_smallest_door_height']:
                    # Basic derived values - depends on input fields
                    category = 'basic'
                    sequence = 3
                elif field_name in ['_CALCULATED_halfway_point']:
                    # Depends on smallest_door_height
                    category = 'basic'
                    sequence = 4
                elif 'halfway' in field_name.lower() and ('plus' in field_name or 'minus' in field_name):
                    # Depends on halfway_point
                    category = 'threshold'
                    sequence = 5
                elif 'height_minus_' in field_name or 'height_plus_' in field_name:
                    # Depends on smallest_door_height
                    category = 'threshold'
                    sequence = 6
                elif field_name.startswith('_CALCULATED_is_'):
                    # Boolean conditions - can be calculated early
                    category = 'condition'
                    sequence = 2
                elif 'formula' in field_name.lower():
                    # String formulas - calculate last
                    category = 'final'
                    sequence = 10
                elif 'range' in field_name.lower() or 'valid' in field_name.lower():
                    category = 'range'
                    sequence = 7
                elif 'kit' in field_name.lower():
                    category = 'kit'
                    sequence = 8
                elif 'FINAL' in field_name:
                    category = 'final'
                    sequence = 9

                vals = {
                    'name': field_name,
                    'description': field_def.get('description', ''),
                    'formula': field_def.get('formula', ''),
                    'category': category,
                    'sequence': sequence,
                    'template_ids': [(6, 0, template_ids)] if template_ids else False,
                    'data_type': 'boolean' if 'Boolean:' in field_def.get('description', '') else 'number',
                    'is_price_matrix': field_def.get('is_price_matrix', False)
                }

                if existing:
                    # Update existing field and add templates if specified
                    if template_ids:
                        existing_template_ids = existing.template_ids.ids
                        new_template_ids = list(set(existing_template_ids + template_ids))
                        vals['template_ids'] = [(6, 0, new_template_ids)]
                    existing.write(vals)
                    updated_count += 1
                else:
                    self.create(vals)
                    created_count += 1

            return {
                'created': created_count,
                'updated': updated_count,
                'total': len(data)
            }

        except Exception as e:
            _logger.error(f"Error importing calculated fields: {str(e)}")
            raise


