<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Calculated Field Tree View -->
    <record id="view_config_matrix_calculated_field_tree" model="ir.ui.view">
        <field name="name">config.matrix.calculated.field.tree</field>
        <field name="model">config.matrix.calculated.field</field>
        <field name="arch" type="xml">
            <list string="Calculated Fields" default_order="sequence,name">
                <field name="sequence"/>
                <field name="name"/>
                <field name="description"/>
                <field name="category"/>
                <field name="data_type"/>
                <field name="template_ids" widget="many2many_tags"/>
                <field name="active"/>
            </list>
        </field>
    </record>

    <!-- Calculated Field Form View -->
    <record id="view_config_matrix_calculated_field_form" model="ir.ui.view">
        <field name="name">config.matrix.calculated.field.form</field>
        <field name="model">config.matrix.calculated.field</field>
        <field name="arch" type="xml">
            <form string="Calculated Field">
                <header>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <field name="active" widget="boolean_button"
                               options="{'terminology': 'archive'}"/>
                    </div>
                    
                    <group>
                        <group>
                            <field name="name" placeholder="_CALCULATED_field_name"/>
                            <field name="description" placeholder="Description of what this field calculates"/>
                            <field name="category"/>
                            <field name="data_type"/>
                        </group>
                        <group>
                            <field name="sequence"/>
                            <field name="template_ids" widget="many2many_tags" options="{'no_create': True}"/>
                            <field name="depends_on" placeholder="field1, field2, _CALCULATED_other_field"/>
                            <field name="is_price_matrix" widget="boolean_toggle"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Formula" name="formula">
                            <group>
                                <field name="formula" widget="code" options="{'mode': 'javascript'}" 
                                       placeholder="JavaScript expression, e.g., Math.min(field1, field2)"/>
                            </group>
                            <div class="alert alert-info" role="alert">
                                <strong>Formula Help:</strong>
                                <ul>
                                    <li>Use field technical names directly (e.g., bx_dbl_hinge_door_height)</li>
                                    <li>Reference other calculated fields (e.g., _CALCULATED_smallest_door_height)</li>
                                    <li>Available functions: Math.min, Math.max, Math.abs, Math.round, etc.</li>
                                    <li>Boolean expressions: field1 === 'value' &amp;&amp; field2 > 100</li>
                                    <li>String concatenation: 'Result: ' + field1 + ' = ' + result</li>
                                </ul>
                            </div>
                        </page>
                        

                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Calculated Field Search View -->
    <record id="view_config_matrix_calculated_field_search" model="ir.ui.view">
        <field name="name">config.matrix.calculated.field.search</field>
        <field name="model">config.matrix.calculated.field</field>
        <field name="arch" type="xml">
            <search string="Search Calculated Fields">
                <field name="name"/>
                <field name="description"/>
                <field name="formula"/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="Global" name="global" domain="[('template_ids', '=', False)]"/>
                <filter string="Template Specific" name="template_specific" domain="[('template_ids', '!=', False)]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Category" name="group_category" context="{'group_by': 'category'}"/>
                    <filter string="Template" name="group_template" context="{'group_by': 'template_ids'}"/>
                    <filter string="Data Type" name="group_data_type" context="{'group_by': 'data_type'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Import Wizard Action (defined first so it can be referenced) -->
    <record id="action_config_matrix_calculated_field_import" model="ir.actions.act_window">
        <field name="name">Import Calculated Fields</field>
        <field name="res_model">config.matrix.calculated.field.import.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <!-- Calculated Field Action -->
    <record id="action_config_matrix_calculated_field" model="ir.actions.act_window">
        <field name="name">Calculated Fields</field>
        <field name="res_model">config.matrix.calculated.field</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first calculated field!
            </p>
            <p>
                Calculated fields allow you to define dynamic values that are computed
                based on user inputs and other calculated fields. They are essential
                for complex visibility conditions and business logic.
            </p>
            <p>
                <a type="action" name="%(action_config_matrix_calculated_field_import)d" class="btn btn-primary">
                    Import from JSON
                </a>
            </p>
        </field>
    </record>

    <!-- Import Wizard for Calculated Fields -->
    <record id="view_config_matrix_calculated_field_import_wizard" model="ir.ui.view">
        <field name="name">config.matrix.calculated.field.import.wizard</field>
        <field name="model">config.matrix.calculated.field.import.wizard</field>
        <field name="arch" type="xml">
            <form string="Import Calculated Fields">
                <sheet>
                    <group>
                        <field name="template_ids" widget="many2many_tags" options="{'no_create': True}"/>
                        <field name="json_data" widget="code" options="{'mode': 'javascript'}"
                               placeholder='{"_CALCULATED_field_name": {"description": "...", "formula": "..."}}'/>
                    </group>
                </sheet>
                <footer>
                    <button name="action_import" type="object" string="Import" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Menu Items (defined at end to ensure all dependencies are loaded) -->
    <menuitem id="menu_config_matrix_calculated_field"
              name="Calculated Fields"
              parent="menu_config_matrix_tools"
              action="action_config_matrix_calculated_field"
              sequence="25"/>

    <menuitem id="menu_config_matrix_calculated_field_import"
              name="Import Calculated Fields"
              parent="menu_config_matrix_tools"
              action="action_config_matrix_calculated_field_import"
              sequence="26"/>

</odoo>
