/**
 * Components List - v2.0 - CACHE BUST 2025-05-28
 *
 * This script manages the components list in the right-hand column of the configurator.
 * It shows components and prices based on answered questions.
 * Updated with badge support and internal reference display.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Global variables
    let templateId = null;
    let componentsList = null;
    let configurationPrice = null;
    let configurationPriceMatrix = null;
    let components = [];
    let fieldValues = {};

    // PERFORMANCE FIX: Async BOM updates with loading states
    let isUpdatingBOM = false;
    let pendingBOMUpdate = false;
    let bomUpdateTimer = null;
    const BOM_UPDATE_DEBOUNCE = 300; // 300ms debounce for BOM updates

    // PERFORMANCE FIX: Track original field values to detect actual changes
    let originalFieldValues = {};

    // Initialize the components list
    function initialize() {
        // Get the components list container
        componentsList = document.getElementById('components-list');
        if (!componentsList) {
            console.error('[COMPONENTS] Components list container not found');
            return;
        }

        // Get the configuration price element
        configurationPrice = document.getElementById('configuration-price');
        configurationPriceMatrix = document.getElementById('configuration-price-matrix');
        if (!configurationPrice) {
            console.error('[COMPONENTS] Configuration price element not found');
            return;
        }

        // Get the template ID
        const templateIdInput = document.querySelector('input[name="template_id"]');
        if (!templateIdInput) {
            console.error('[COMPONENTS] Template ID input not found');
            return;
        }
        templateId = parseInt(templateIdInput.value) || 0;

        // Add event listeners to all configuration fields
        addFieldEventListeners();

        // Initial update of components list
        updateComponentsList();
    }

    // Add event listeners to all configuration fields
    function addFieldEventListeners() {
        // Find all configuration fields
        const configFields = document.querySelectorAll('.config-field');

        // Add event listeners to each field
        configFields.forEach(field => {
            // Get field information
            const fieldId = field.getAttribute('data-field-id');
            const technicalName = field.getAttribute('data-technical-name');
            const fieldType = field.getAttribute('data-field-type');

            if (!fieldId || !technicalName) {
                console.warn('[COMPONENTS] Field missing ID or technical name:', field);
                return;
            }

            // Add focus event listener to capture original value
            field.addEventListener('focus', function () {
                // Capture the original value when field gets focus
                originalFieldValues[technicalName] = getFieldValue(field, fieldType);
                console.log(`[COMPONENTS] Captured original value for ${technicalName}:`, originalFieldValues[technicalName]);
            });

            // Add change event listener for immediate value updates
            field.addEventListener('change', async function() {
                // Get the field value
                let value = getFieldValue(field, fieldType);

                // Update the field value in the global object immediately
                fieldValues[technicalName] = value;
            });

            // PERFORMANCE FIX: Add blur event listener for async BOM updates
            // This triggers non-blocking BOM generation only when field value actually changed
            field.addEventListener('blur', async function () {
                // Get the current field value
                let currentValue = getFieldValue(field, fieldType);
                let originalValue = originalFieldValues[technicalName];

                // Check if the value actually changed
                if (currentValue === originalValue) {
                    console.log(`[COMPONENTS] No change detected for ${technicalName}, skipping update`);
                    return; // Skip update if no change
                }

                // Update the field value in the global object immediately
                fieldValues[technicalName] = currentValue;

                // PERFORMANCE FIX: Use debounced BOM update to prevent rapid successive calls
                console.log(`[COMPONENTS] Value changed for ${technicalName}: ${originalValue} → ${currentValue}`);
                debouncedBOMUpdate(); // Debounced non-blocking call
            });
        });
    }

    // Get the value of a field based on its type
    function getFieldValue(field, fieldType) {
        switch (fieldType) {
            case 'boolean':
                return field.checked;
            case 'number':
                return parseFloat(field.value) || 0;
            case 'selection':
                // Check if this is an unselected option
                if (!field.value || field.value === '-- Select an option --' || field.selectedIndex === 0) {
                    return '';
                }
                return field.value;
            default:
                return field.value;
        }
    }

    // PERFORMANCE FIX: Collect current values of visible fields only
    function collectFieldValues() {
        const startTime = performance.now();

        // Clear existing values
        fieldValues = {};

        // Find all configuration fields
        const configFields = document.querySelectorAll('.config-field');
        let visibleCount = 0;
        let totalCount = 0;

        // Collect values from each field
        configFields.forEach(field => {
            totalCount++;

            // Get field information
            const technicalName = field.getAttribute('data-technical-name');
            const fieldType = field.getAttribute('data-field-type');

            if (!technicalName) {
                return;
            }

            // PERFORMANCE FIX: Skip hidden fields early to reduce processing
            const container = field.closest('.config-field-container');
            if (container && (container.style.display === 'none' || container.classList.contains('conditional-field-pending'))) {
                return; // Skip hidden fields
            }

            visibleCount++;

            // Get the field value
            const value = getFieldValue(field, fieldType);

            // Store the value
            fieldValues[technicalName] = value;
        });

        const endTime = performance.now();
        console.log(`[COMPONENTS] Collected ${visibleCount}/${totalCount} field values in ${(endTime - startTime).toFixed(2)}ms`);
    }

    // PERFORMANCE FIX: Debounced BOM update to prevent rapid successive calls
    function debouncedBOMUpdate() {
        // Clear existing timer
        if (bomUpdateTimer) {
            clearTimeout(bomUpdateTimer);
        }

        // Set new timer
        bomUpdateTimer = setTimeout(() => {
            console.log('[COMPONENTS] Debounced BOM update triggered');
            updateComponentsListAsync();
            bomUpdateTimer = null;
        }, BOM_UPDATE_DEBOUNCE);
    }

    // PERFORMANCE FIX: Asynchronous BOM update function with optimizations
    async function updateComponentsListAsync() {
        // If already updating, mark that another update is pending
        if (isUpdatingBOM) {
            pendingBOMUpdate = true;
            console.log('[COMPONENTS] BOM update already in progress, marking as pending');
            return;
        }

        isUpdatingBOM = true;
        pendingBOMUpdate = false;

        try {
            // Show loading indicator
            if (componentsList) {
                componentsList.innerHTML = '<div class="text-muted small p-3 text-center"><i class="fas fa-spinner fa-spin"></i> Updating components...</div>';
            }

            // PERFORMANCE FIX: Add longer delay to allow UI to update and batch multiple rapid changes
            await new Promise(resolve => setTimeout(resolve, 100));

            // PERFORMANCE FIX: Check if another update is pending before proceeding
            if (pendingBOMUpdate) {
                console.log('[COMPONENTS] Newer update pending, skipping this one');
                isUpdatingBOM = false;
                updateComponentsListAsync(); // Process the newer update
                return;
            }

            console.log('[COMPONENTS] Starting BOM generation...');
            const startTime = performance.now();

            // Call the actual BOM update
            await updateComponentsList();

            const endTime = performance.now();
            console.log(`[COMPONENTS] BOM generation completed in ${(endTime - startTime).toFixed(2)}ms`);

            // If another update was requested while we were processing, run it
            if (pendingBOMUpdate) {
                console.log('[COMPONENTS] Running pending BOM update');
                isUpdatingBOM = false;
                updateComponentsListAsync(); // Recursive call for pending update
            } else {
                isUpdatingBOM = false;
            }
        } catch (error) {
            console.error('[COMPONENTS] Error in async BOM update:', error);
            isUpdatingBOM = false;
            if (componentsList) {
                componentsList.innerHTML = '<div class="text-danger small p-3 text-center">Error updating components</div>';
            }
        }
    }

    // Update the components list based on current field values
    function updateComponentsList() {
        // Collect current field values
        collectFieldValues();

        // Check if we have enough field values to generate a BOM
        const filledFields = Object.values(fieldValues).filter(v => v).length;

        // If we don't have enough field values, don't try to generate a BOM yet
        if (filledFields < 3) {  // Arbitrary threshold - adjust as needed
            componentsList.innerHTML = '<div class="text-muted small p-3 text-center">Fill in more fields to see components</div>';
            priceMatrixList.innerHTML = '<div class="text-muted small p-3 text-center">No Price Matrices selected yet</div>';
            if (configurationPriceMatrix) {
                configurationPriceMatrix.textContent = '$0.00';
                const fieldPriceMatrix = document.getElementById('field-price-matrix');
                if (fieldPriceMatrix) {
                    fieldPriceMatrix.value = '0.00';
                    console.log('[COMPONENTS] Reset field-price-matrix input to 0.00');
                }
            }
            if (configurationPrice) {
                configurationPrice.textContent = '$0.00';
                
                // Update hidden input fields
                const fieldPrice = document.getElementById('field-price');
                
                if (fieldPrice) {
                    fieldPrice.value = '0.00';
                    console.log('[COMPONENTS] Reset field-price input to 0.00');
                }
            }
            return;
        }

        // Generate configuration data in the same format as the backend
        const configData = generateConfigData();

        // Call the backend to get the components
        fetch('/config_matrix/generate_bom_preview', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                jsonrpc: "2.0",
                method: "call",
                params: {
                    template_id: templateId,
                    config_data: configData
                },
                id: new Date().getTime()
            })
        })
        .then(response => response.json())
        .then(data => {
            // DEBUGGING: Log the raw components data to see what we're getting
            if (data.result && data.result.components) {

                // Create a summary of legacy component sources
                const legacyComponents = data.result.components.filter(comp => comp.source_info && comp.source_info.startsWith('Q'));
                if (legacyComponents.length > 0) {
                    // Legacy components detected - could add UI notification here if needed
                }
            }

            if (data.result && data.result.success) {
                // Update components list
                components = data.result.components || [];

                // Render components list
                renderComponentsList();

                // Update total price
                updateTotalPrice(data.result.total_price || 0);

                // If there's a message, log it
                if (data.result.message) {
                    // Message logged for debugging if needed
                }
            } else {
                console.error('[COMPONENTS] Error loading components:', data);
                console.error('[COMPONENTS] Full response data:', JSON.stringify(data, null, 2));
                console.error('[COMPONENTS] Result exists:', !!data.result);
                console.error('[COMPONENTS] Result success:', data.result ? data.result.success : 'no result');
                console.error('[COMPONENTS] Result error:', data.result ? data.result.error : 'no result');

                if (data.result && data.result.error) {
                    console.error('[COMPONENTS] Server error:', data.result.error);
                    showError(`Error: ${data.result.error}`);
                } else if (data.error) {
                    console.error('[COMPONENTS] JSON-RPC error:', data.error);
                    showError(`Error: ${data.error.message || data.error}`);
                } else {
                    console.error('[COMPONENTS] Unknown error format');
                    showError('Error loading components - check console for details');
                }
            }
        })
        .catch(error => {
            console.error('[COMPONENTS] Error fetching components:', error);
            showError('Error loading components');
        });
    }

    // Generate configuration data in the same format as the backend
    // IMPORTANT: Includes ALL field default values, but sets hidden fields to null
    // This ensures proper default values while preventing hidden fields from affecting BOM generation
    function generateConfigData() {
        // Convert field values to the format expected by the backend
        const configData = {};

        // Get field visibility state from multiple sources for robustness
        const fieldVisibility = window.configuratorComponent?.state?.fieldVisibility || {};

        // Also check DOM visibility as a fallback (handles visibility_conditions.js updates)
        function isFieldVisibleInDOM(fieldName) {
            const container = document.querySelector(`[data-technical-name="${fieldName}"]`)?.closest('.config-field-container');
            if (!container) return true; // If container not found, assume visible for backward compatibility

            // Check if the container is hidden
            const isHidden = container.style.display === 'none' ||
                container.classList.contains('conditional-field-pending');
            return !isHidden;
        }

        // STEP 1: Get ALL field values (including defaults for hidden fields)
        // Priority: local fieldValues > window.fieldValues > configurator state values > DOM defaults
        const allFieldValues = {
            ...(window.configuratorComponent?.state?.values || {}),
            ...(window.fieldValues || {}),
            ...fieldValues
        };

        // STEP 2: Also collect default values from DOM for any missing fields
        document.querySelectorAll('.config-field').forEach(field => {
            const technicalName = field.getAttribute('data-technical-name');
            if (technicalName && !allFieldValues.hasOwnProperty(technicalName)) {
                const defaultValue = field.getAttribute('data-default-value') || '';
                const fieldType = field.getAttribute('data-field-type');

                // Set appropriate default based on field type
                if (fieldType === 'boolean') {
                    allFieldValues[technicalName] = defaultValue === 'true' || defaultValue === '1';
                } else if (fieldType === 'number') {
                    allFieldValues[technicalName] = parseFloat(defaultValue) || 0;
                } else {
                    allFieldValues[technicalName] = defaultValue;
                }
            }
        });

        // STEP 3: Process all fields - include visible values, set hidden to null
        let visibleCount = 0;
        let hiddenCount = 0;

        for (const [key, value] of Object.entries(allFieldValues)) {
            // Check visibility from multiple sources:
            // 1. Configurator component state (primary source)
            // 2. DOM visibility state (fallback for visibility_conditions.js)
            const isVisibleInState = fieldVisibility[key] !== false;
            const isVisibleInDOM = isFieldVisibleInDOM(key);

            // Field is considered visible if BOTH sources indicate it's visible
            const isVisible = isVisibleInState && isVisibleInDOM;

            if (isVisible) {
                // Include the actual value for visible fields
                configData[key] = value;
                visibleCount++;
            } else {
                // Set hidden fields to null to prevent them from affecting BOM generation
                configData[key] = null;
                hiddenCount++;
                console.log(`[COMPONENTS] Setting hidden field '${key}' to null (was: ${value})`);
            }
        }

        console.log(`[COMPONENTS] Generated config data: ${visibleCount} visible fields, ${hiddenCount} hidden fields (set to null), ${Object.keys(allFieldValues).length} total fields`);
        return JSON.stringify(configData);
    }

    // Render the components list
    function renderComponentsList() {
        if (!componentsList) {
            console.error('[COMPONENTS] Components list container not found');
            return;
        }

        if (components.length === 0) {
            componentsList.innerHTML = '<div class="text-muted small p-3 text-center">No components selected yet</div>';
            return;
        }

        // Count components by type
        const componentCounts = {};
        components.forEach(component => {
            const type = component.type || 'unknown';
            componentCounts[type] = (componentCounts[type] || 0) + 1;
        });

        // Create summary
        let summaryHtml = '';
        if (Object.keys(componentCounts).length > 1) {
            const countTexts = [];
            if (componentCounts.extended_mapping) {
                const dynamicCount = components.filter(c => c.type === 'extended_mapping' && c.mapping_type === 'Dynamic').length;
                const staticCount = components.filter(c => c.type === 'extended_mapping' && c.mapping_type === 'Static').length;
                if (dynamicCount > 0) countTexts.push(`${dynamicCount} Dynamic`);
                if (staticCount > 0) countTexts.push(`${staticCount} Static`);
            }
            if (componentCounts.option) countTexts.push(`${componentCounts.option} Option`);
            if (componentCounts.field) countTexts.push(`${componentCounts.field} Field`);
            if (componentCounts.mapping) countTexts.push(`${componentCounts.mapping} Template`);
            if (componentCounts.base) countTexts.push(`${componentCounts.base} Base`);

            if (countTexts.length > 0) {
                summaryHtml = `
                    <div class="alert alert-info py-2 mb-2">
                        <small><strong>${components.length} components:</strong> ${countTexts.join(', ')}</small>
                    </div>
                `;
            }
        }

        // Create HTML for components list
        let html = summaryHtml + '<ul class="list-group list-group-flush">';

        components.forEach(component => {
            // Create question number badge instead of type badge
            let typeBadge = '';

            if (component.source_info) {
                // Show question number if available - use square badge style for better readability
                typeBadge = `<span class="badge bg-secondary me-2" style="min-width: 32px; height: 24px; display: inline-flex; align-items: center; justify-content: center; font-size: 11px; font-weight: bold; border-radius: 4px;" title="From ${component.source_info}">${component.source_info}</span>`;
            } else if (component.type === 'base') {
                // Keep base product badge
                typeBadge = '<span class="badge bg-light text-dark me-1" title="Base product">Base</span>';
            } else {
                // For components without question numbers, don't show a badge
                typeBadge = '';
            }

            // Build description with additional info
            let description = component.description || '';
            if (component.field_name) {
                description = `From: ${component.field_name}`;
                if (component.option_name) {
                    description += ` → ${component.option_name}`;
                }
                if (component.mapping_name && component.type === 'extended_mapping') {
                    description += ` (${component.mapping_name})`;
                }
            }

            // Add internal reference if available
            let internalRef = '';
            if (component.default_code) {
                internalRef = `<br><small class="text-muted">Code: ${component.default_code}</small>`;
            }

            html += `
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        ${typeBadge}
                        <div>
                            <div class="fw-bold">${component.name}${internalRef}</div>
                            <div class="text-muted small">
                                ${description}
                            </div>
                        </div>
                    </div>
                    <div>
                        <span class="badge bg-secondary rounded-pill">${component.quantity}</span>
                        <span class="ms-2 text-success">${formatCurrency(component.price)}</span>
                    </div>
                </li>
            `;
        });

        html += '</ul>';

        // Update the components list
        componentsList.innerHTML = html;
    }

    // Update the total price
    function updateTotalPrice(totalPrice) {
        if (!configurationPrice) {
            console.error('[COMPONENTS] Configuration price element not found');
            return;
        }

        // Format the price
        configurationPrice.textContent = formatCurrency(totalPrice);
        // Update hidden input fields
        const fieldPrice = document.getElementById('field-price');
        
        if (fieldPrice) {
            fieldPrice.value = totalPrice;
            console.log(`[COMPONENTS] Updated field-price input: ${totalPrice}`);
        }
    
    }

    // Format currency
    function formatCurrency(amount) {
        return '$' + parseFloat(amount).toFixed(2);
    }

    // Show error message
    function showError(message) {
        if (!componentsList) {
            console.error('[COMPONENTS] Components list container not found');
            return;
        }

        componentsList.innerHTML = `<div class="alert alert-danger m-2">${message}</div>`;
    }

    // Use the price matrix handler if available
    async function updatePriceFromMatrix() {
        if (window.priceMatrixHandler && window.priceMatrixHandler.updatePriceFromMatrix) {
            return await window.priceMatrixHandler.updatePriceFromMatrix();
        }
        return false;
    }

    // Initialize after a short delay to ensure DOM is ready
    setTimeout(initialize, 500);  // Reduced delay from 1500ms to 500ms

    // Add a button to clear legacy components
    function addClearLegacyComponentsButton(legacyComponents) {
        // Check if button already exists
        if (document.getElementById('clear-legacy-components-btn')) {
            return;
        }

        // Find the components list container
        const componentsCard = componentsList.closest('.card');

        if (!componentsCard) {
            // Try alternative method - find by ID
            const alternativeCard = document.querySelector('#components-list')?.closest('.card');

            if (alternativeCard) {
                addButtonToCard(alternativeCard, legacyComponents);
            } else {
                console.error('🚨 [LEGACY COMPONENTS] No card found at all - cannot add button');
            }
            return;
        }

        addButtonToCard(componentsCard, legacyComponents);
    }

    function addButtonToCard(card, legacyComponents) {
        // Create the button
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'p-3 border-top bg-warning';
        buttonContainer.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong class="text-dark">⚠️ Legacy Components Detected</strong>
                    <br><small class="text-dark">${legacyComponents.length} automatic component(s) from old mappings</small>
                </div>
                <button id="clear-legacy-components-btn" class="btn btn-danger btn-sm">
                    <i class="fas fa-trash"></i> Clear Legacy
                </button>
            </div>
        `;

        // Add the button container to the card
        card.appendChild(buttonContainer);

        // Add click handler
        const clearButton = document.getElementById('clear-legacy-components-btn');
        if (clearButton) {
            clearButton.addEventListener('click', function() {
                clearLegacyComponents(legacyComponents);
            });
        } else {
            console.error('🚨 [LEGACY COMPONENTS] ❌ Could not find clear button after adding to DOM');
        }
    }

    // Clear legacy components via backend call
    function clearLegacyComponents(legacyComponents) {
        if (!confirm(`Are you sure you want to clear ${legacyComponents.length} legacy component mapping(s)?\n\nThis will remove:\n${legacyComponents.map(c => `• ${c.name} (${c.source_info})`).join('\n')}\n\nThis action cannot be undone.`)) {
            return;
        }

        // Show loading state
        const clearButton = document.getElementById('clear-legacy-components-btn');
        if (clearButton) {
            clearButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Clearing...';
            clearButton.disabled = true;
        }

        // Call backend to clear legacy components
        fetch('/config_matrix/clear_legacy_components', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                jsonrpc: "2.0",
                method: "call",
                params: {
                    template_id: templateId,
                    legacy_components: legacyComponents.map(c => ({
                        product_id: c.id,
                        question_number: c.question_number,
                        source_info: c.source_info
                    }))
                },
                id: new Date().getTime()
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.result && data.result.success) {

                // Show success message
                alert(`✅ Successfully cleared ${data.result.cleared_count} legacy component mapping(s)!\n\nThe components will disappear when you refresh or change a field.`);

                // Remove the warning banner
                const buttonContainer = document.getElementById('clear-legacy-components-btn')?.closest('.bg-warning');
                if (buttonContainer) {
                    buttonContainer.remove();
                }

                // Refresh components list (debounced async update after clearing legacy components)
                debouncedBOMUpdate();

            } else {
                console.error('🚨 [LEGACY COMPONENTS] ❌ Error clearing components:', data);
                alert(`❌ Error clearing legacy components: ${data.result?.error || data.error?.message || 'Unknown error'}`);

                // Reset button
                if (clearButton) {
                    clearButton.innerHTML = '<i class="fas fa-trash"></i> Clear Legacy';
                    clearButton.disabled = false;
                }
            }
        })
        .catch(error => {
            console.error('🚨 [LEGACY COMPONENTS] ❌ Network error:', error);
            alert('❌ Network error clearing legacy components. Please try again.');

            // Reset button
            if (clearButton) {
                clearButton.innerHTML = '<i class="fas fa-trash"></i> Clear Legacy';
                clearButton.disabled = false;
            }
        });
    }

    // Make functions available globally
    window.componentsList = {
        initialize,
        updateComponentsList
    };
});
