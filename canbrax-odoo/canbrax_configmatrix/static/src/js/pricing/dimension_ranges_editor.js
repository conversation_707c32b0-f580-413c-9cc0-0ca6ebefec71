/** @odoo-module **/

import { Component, onMounted, useState, useRef } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";

/**
 * Dimension Ranges Editor Widget
 * Provides visual editing of height/width ranges for price matrices
 */
export class DimensionRangesEditor extends Component {
    static template = "canbrax_configmatrix.DimensionRangesEditor";

    setup() {
        this.state = useState({
            ranges: [],
            editingIndex: null,
            editValues: { min: '', max: '', label: '' }
        });

        this.notification = useService("notification");
        this.tableRef = useRef("rangesTable");

        onMounted(() => {
            this.loadRanges();
        });
    }

    loadRanges() {
        try {
            const fieldValue = this.props.record.data[this.props.name] || "[]";
            this.state.ranges = JSON.parse(fieldValue);
        } catch (error) {
            console.error("Error loading ranges:", error);
            this.state.ranges = [];
        }
    }

    updateRecord() {
        try {
            const jsonString = JSON.stringify(this.state.ranges);
            this.props.record.update({ [this.props.name]: jsonString });
        } catch (error) {
            console.error('Failed to update record:', error);
        }
    }

    addRange() {
        // Determine next range values
        let newMin, newMax;
        if (this.state.ranges.length > 0) {
            const lastMax = Math.max(...this.state.ranges.map(r => r.max));
            newMin = lastMax + 1;
            newMax = lastMax + 200; // Default 200mm increment
        } else {
            newMin = 300;
            newMax = 500;
        }

        const newRange = {
            min: newMin,
            max: newMax,
            label: newMax.toString()
        };

        this.state.ranges.push(newRange);
        this.sortRanges();
        this.updateRecord();

        this.notification.add(`Added range ${newMin}-${newMax}`, { type: "success" });
    }

    removeRange(index) {
        if (confirm('Are you sure you want to remove this range?')) {
            this.state.ranges.splice(index, 1);
            this.updateRecord();
            this.notification.add("Range removed", { type: "info" });
        }
    }

    startEdit(index) {
        const range = this.state.ranges[index];
        this.state.editingIndex = index;
        this.state.editValues = {
            min: range.min.toString(),
            max: range.max.toString(),
            label: range.label
        };
    }

    cancelEdit() {
        this.state.editingIndex = null;
        this.state.editValues = { min: '', max: '', label: '' };
    }

    saveEdit() {
        const index = this.state.editingIndex;
        const min = parseInt(this.state.editValues.min);
        const max = parseInt(this.state.editValues.max);
        const label = this.state.editValues.label.trim();

        // Validation
        if (isNaN(min) || isNaN(max) || min <= 0 || max <= 0) {
            this.notification.add("Please enter valid positive numbers", { type: "danger" });
            return;
        }

        if (min >= max) {
            this.notification.add("Minimum must be less than maximum", { type: "danger" });
            return;
        }

        if (!label) {
            this.notification.add("Label cannot be empty", { type: "danger" });
            return;
        }

        // Update range
        this.state.ranges[index] = { min, max, label };
        this.sortRanges();
        this.updateRecord();
        this.cancelEdit();

        this.notification.add("Range updated successfully", { type: "success" });
    }

    sortRanges() {
        this.state.ranges.sort((a, b) => a.max - b.max);
    }

    onInputChange(field, event) {
        this.state.editValues[field] = event.target.value;
    }

    onKeydown(event) {
        if (event.key === 'Enter') {
            this.saveEdit();
        } else if (event.key === 'Escape') {
            this.cancelEdit();
        }
    }

    getDimensionType() {
        return this.props.context?.dimension_type || 'dimension';
    }

    getUnit() {
        return 'mm'; // Default unit
    }
}

// Register the widget
registry.category("fields").add("dimension_ranges_editor", {
    component: DimensionRangesEditor,
});
