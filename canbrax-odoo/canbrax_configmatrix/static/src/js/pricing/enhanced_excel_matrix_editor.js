/** @odoo-module **/

import { Component, useState, onMounted, onWillUnmount, useRef } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";
import { _t } from "@web/core/l10n/translation";

/**
 * Enhanced Excel-like Matrix Editor Component
 * Provides spreadsheet-like functionality for editing pricing matrices
 */
export class EnhancedExcelMatrixEditor extends Component {
    static template = "canbrax_configmatrix.EnhancedExcelMatrixEditor";
    
    setup() {
        this.state = useState({
            matrix: {
                name: '',
                heights: [],
                widths: [],
                data: {},
                currency: 'USD'
            },
            selectedCell: null,
            editingCell: null,
            editValue: '',
            rangeSelection: {
                start: null,
                end: null,
                active: false
            },
            loading: false,
            progress: 0,
            stats: {
                totalCells: 0,
                filledCells: 0,
                completionRate: 0
            }
        });
        
        this.containerRef = useRef("matrixContainer");
        this.contextMenuRef = useRef("contextMenu");
        
        // Services
        this.rpc = useService("rpc");
        this.notification = useService("notification");
        this.dialog = useService("dialog");
        
        // Event handlers
        this.keyHandler = null;
        this.clickHandler = null;
        this.contextMenuHandler = null;
        
        // Clipboard data
        this.clipboard = {
            data: null,
            range: null,
            operation: null // 'copy' or 'cut'
        };
        
        // Undo/Redo stack
        this.undoStack = [];
        this.redoStack = [];
        this.maxUndoStack = 50;
        
        onMounted(() => {
            this.setupEventHandlers();
            this.loadMatrix();
            this.calculateStats();
        });
        
        onWillUnmount(() => {
            this.cleanupEventHandlers();
        });
    }
    
    /**
     * Setup keyboard and mouse event handlers
     */
    setupEventHandlers() {
        // Keyboard navigation and shortcuts
        this.keyHandler = (event) => {
            if (!this.containerRef.el?.contains(event.target)) return;
            
            switch(event.key) {
                case 'ArrowUp':
                    event.preventDefault();
                    this.navigateCell(-1, 0);
                    break;
                case 'ArrowDown':
                    event.preventDefault();
                    this.navigateCell(1, 0);
                    break;
                case 'ArrowLeft':
                    event.preventDefault();
                    this.navigateCell(0, -1);
                    break;
                case 'ArrowRight':
                    event.preventDefault();
                    this.navigateCell(0, 1);
                    break;
                case 'Tab':
                    event.preventDefault();
                    this.navigateCell(0, event.shiftKey ? -1 : 1);
                    break;
                case 'Enter':
                    event.preventDefault();
                    if (this.state.editingCell) {
                        this.confirmEdit();
                    } else if (this.state.selectedCell) {
                        this.startEdit();
                    }
                    break;
                case 'Escape':
                    event.preventDefault();
                    this.cancelEdit();
                    break;
                case 'Delete':
                case 'Backspace':
                    event.preventDefault();
                    this.deleteSelectedCells();
                    break;
                case 'F2':
                    event.preventDefault();
                    this.startEdit();
                    break;
                    
                // Keyboard shortcuts with modifiers
                default:
                    if (event.ctrlKey || event.metaKey) {
                        switch(event.key.toLowerCase()) {
                            case 'c':
                                event.preventDefault();
                                this.copySelection();
                                break;
                            case 'v':
                                event.preventDefault();
                                this.pasteSelection();
                                break;
                            case 'x':
                                event.preventDefault();
                                this.cutSelection();
                                break;
                            case 'z':
                                event.preventDefault();
                                if (event.shiftKey) {
                                    this.redo();
                                } else {
                                    this.undo();
                                }
                                break;
                            case 'y':
                                event.preventDefault();
                                this.redo();
                                break;
                            case 'a':
                                event.preventDefault();
                                this.selectAll();
                                break;
                            case 's':
                                event.preventDefault();
                                this.saveMatrix();
                                break;
                        }
                    }
                    break;
            }
        };
        
        // Global click handler to hide context menu
        this.clickHandler = (event) => {
            if (!this.contextMenuRef.el?.contains(event.target)) {
                this.hideContextMenu();
            }
        };
        
        // Context menu handler
        this.contextMenuHandler = (event) => {
            if (this.containerRef.el?.contains(event.target)) {
                event.preventDefault();
                this.showContextMenu(event);
            }
        };
        
        document.addEventListener('keydown', this.keyHandler);
        document.addEventListener('click', this.clickHandler);
        document.addEventListener('contextmenu', this.contextMenuHandler);
    }
    
    /**
     * Cleanup event handlers
     */
    cleanupEventHandlers() {
        if (this.keyHandler) {
            document.removeEventListener('keydown', this.keyHandler);
        }
        if (this.clickHandler) {
            document.removeEventListener('click', this.clickHandler);
        }
        if (this.contextMenuHandler) {
            document.removeEventListener('contextmenu', this.contextMenuHandler);
        }
    }
    
    /**
     * Load matrix data from server
     */
    async loadMatrix() {
        if (!this.props.matrixId) return;
        
        this.state.loading = true;
        try {
            const result = await this.rpc("/web/dataset/call_kw", {
                model: "config.matrix.price.matrix",
                method: "read",
                args: [[this.props.matrixId], ['name', 'height_ranges', 'width_ranges', 'matrix_data', 'currency_id']],
                kwargs: {}
            });
            
            if (result && result[0]) {
                const matrixRecord = result[0];
                this.state.matrix = {
                    name: matrixRecord.name,
                    heights: JSON.parse(matrixRecord.height_ranges || '[]').map(h => h.label),
                    widths: JSON.parse(matrixRecord.width_ranges || '[]').map(w => w.label),
                    data: JSON.parse(matrixRecord.matrix_data || '{}'),
                    currency: matrixRecord.currency_id ? matrixRecord.currency_id[1] : 'USD'
                };
                this.calculateStats();
            }
        } catch (error) {
            this.notification.add(_t("Failed to load matrix: ") + error.message, { type: "danger" });
        } finally {
            this.state.loading = false;
        }
    }
    
    /**
     * Save matrix data to server
     */
    async saveMatrix() {
        if (!this.props.matrixId) return;
        
        this.state.loading = true;
        this.state.progress = 0;
        
        try {
            // Prepare height and width ranges
            const heightRanges = this.state.matrix.heights.map((label, index) => ({
                min: index === 0 ? parseInt(label) : parseInt(this.state.matrix.heights[index - 1]) + 1,
                max: parseInt(label),
                label: label
            }));
            
            const widthRanges = this.state.matrix.widths.map((label, index) => ({
                min: index === 0 ? parseInt(label) : parseInt(this.state.matrix.widths[index - 1]) + 1,
                max: parseInt(label),
                label: label
            }));
            
            this.state.progress = 50;
            
            await this.rpc("/web/dataset/call_kw", {
                model: "config.matrix.price.matrix",
                method: "write",
                args: [[this.props.matrixId], {
                    name: this.state.matrix.name,
                    height_ranges: JSON.stringify(heightRanges),
                    width_ranges: JSON.stringify(widthRanges),
                    matrix_data: JSON.stringify(this.state.matrix.data)
                }],
                kwargs: {}
            });
            
            this.state.progress = 100;
            this.notification.add(_t("Matrix saved successfully"), { type: "success" });
            
        } catch (error) {
            this.notification.add(_t("Failed to save matrix: ") + error.message, { type: "danger" });
        } finally {
            this.state.loading = false;
            this.state.progress = 0;
        }
    }
    
    /**
     * Calculate matrix statistics
     */
    calculateStats() {
        const totalCells = this.state.matrix.heights.length * this.state.matrix.widths.length;
        const filledCells = Object.keys(this.state.matrix.data).length;
        const completionRate = totalCells > 0 ? Math.round((filledCells / totalCells) * 100) : 0;
        
        this.state.stats = {
            totalCells,
            filledCells,
            completionRate
        };
    }
    
    /**
     * Navigate to adjacent cell
     */
    navigateCell(deltaRow, deltaCol) {
        if (!this.state.selectedCell) {
            // Select first cell if none selected
            if (this.state.matrix.heights.length && this.state.matrix.widths.length) {
                this.selectCell(this.state.matrix.heights[0], this.state.matrix.widths[0]);
            }
            return;
        }
        
        const [currentHeight, currentWidth] = this.state.selectedCell;
        const heightIndex = this.state.matrix.heights.indexOf(currentHeight);
        const widthIndex = this.state.matrix.widths.indexOf(currentWidth);
        
        const newHeightIndex = Math.max(0, Math.min(this.state.matrix.heights.length - 1, heightIndex + deltaRow));
        const newWidthIndex = Math.max(0, Math.min(this.state.matrix.widths.length - 1, widthIndex + deltaCol));
        
        const newHeight = this.state.matrix.heights[newHeightIndex];
        const newWidth = this.state.matrix.widths[newWidthIndex];
        
        this.selectCell(newHeight, newWidth);
    }
    
    /**
     * Select a cell
     */
    selectCell(height, width) {
        this.state.selectedCell = [height, width];
        this.state.rangeSelection.active = false;
        
        // Scroll to cell if needed
        this.scrollToCell(height, width);
    }
    
    /**
     * Start editing a cell
     */
    startEdit() {
        if (!this.state.selectedCell) return;
        
        const [height, width] = this.state.selectedCell;
        const cellKey = `${height}_${width}`;
        const currentValue = this.state.matrix.data[cellKey] || '';
        
        this.state.editingCell = [height, width];
        this.state.editValue = currentValue.toString();
        
        // Focus the input after render
        setTimeout(() => {
            const input = this.containerRef.el?.querySelector('.cell-input');
            if (input) {
                input.focus();
                input.select();
            }
        }, 0);
    }
    
    /**
     * Confirm cell edit
     */
    confirmEdit() {
        if (!this.state.editingCell) return;
        
        const [height, width] = this.state.editingCell;
        const cellKey = `${height}_${width}`;
        const oldValue = this.state.matrix.data[cellKey] || '';
        const newValue = parseFloat(this.state.editValue) || 0;
        
        // Save for undo
        this.saveUndoState();
        
        // Update matrix data
        if (newValue === 0) {
            delete this.state.matrix.data[cellKey];
        } else {
            this.state.matrix.data[cellKey] = newValue;
        }
        
        this.state.editingCell = null;
        this.state.editValue = '';
        
        // Recalculate stats
        this.calculateStats();
        
        // Show visual feedback
        this.showCellUpdate(height, width);
    }
    
    /**
     * Cancel cell edit
     */
    cancelEdit() {
        this.state.editingCell = null;
        this.state.editValue = '';
    }
    
    /**
     * Delete selected cells
     */
    deleteSelectedCells() {
        if (!this.state.selectedCell) return;
        
        this.saveUndoState();
        
        if (this.state.rangeSelection.active) {
            // Delete range
            this.deleteRange();
        } else {
            // Delete single cell
            const [height, width] = this.state.selectedCell;
            const cellKey = `${height}_${width}`;
            delete this.state.matrix.data[cellKey];
        }
        
        this.calculateStats();
    }
    
    /**
     * Copy selection to clipboard
     */
    copySelection() {
        if (!this.state.selectedCell) return;
        
        this.clipboard.operation = 'copy';
        
        if (this.state.rangeSelection.active) {
            this.clipboard.data = this.getRangeData();
            this.clipboard.range = { ...this.state.rangeSelection };
        } else {
            const [height, width] = this.state.selectedCell;
            const cellKey = `${height}_${width}`;
            this.clipboard.data = this.state.matrix.data[cellKey] || '';
            this.clipboard.range = null;
        }
        
        this.showToast(_t("Copied to clipboard"), "success");
    }
    
    /**
     * Cut selection to clipboard
     */
    cutSelection() {
        this.copySelection();
        this.clipboard.operation = 'cut';
        this.deleteSelectedCells();
    }
    
    /**
     * Paste from clipboard
     */
    pasteSelection() {
        if (!this.clipboard.data || !this.state.selectedCell) return;
        
        this.saveUndoState();
        
        const [height, width] = this.state.selectedCell;
        
        if (Array.isArray(this.clipboard.data)) {
            // Paste range data
            this.pasteRangeData(height, width, this.clipboard.data);
        } else {
            // Paste single value
            const cellKey = `${height}_${width}`;
            if (this.clipboard.data === '') {
                delete this.state.matrix.data[cellKey];
            } else {
                this.state.matrix.data[cellKey] = this.clipboard.data;
            }
        }
        
        this.calculateStats();
        this.showToast(_t("Pasted successfully"), "success");
    }
    
    /**
     * Undo last action
     */
    undo() {
        if (this.undoStack.length === 0) return;
        
        const state = this.undoStack.pop();
        this.redoStack.push({ ...this.state.matrix.data });
        
        this.state.matrix.data = state;
        this.calculateStats();
        
        this.showToast(_t("Undo successful"), "info");
    }
    
    /**
     * Redo last undone action
     */
    redo() {
        if (this.redoStack.length === 0) return;
        
        const state = this.redoStack.pop();
        this.undoStack.push({ ...this.state.matrix.data });
        
        this.state.matrix.data = state;
        this.calculateStats();
        
        this.showToast(_t("Redo successful"), "info");
    }
    
    /**
     * Save current state for undo
     */
    saveUndoState() {
        this.undoStack.push({ ...this.state.matrix.data });
        if (this.undoStack.length > this.maxUndoStack) {
            this.undoStack.shift();
        }
        // Clear redo stack when new action is performed
        this.redoStack = [];
    }
    
    /**
     * Show context menu
     */
    showContextMenu(event) {
        // Implementation for context menu would go here
        // For now, we'll keep it simple
    }
    
    /**
     * Hide context menu
     */
    hideContextMenu() {
        // Implementation for hiding context menu
    }
    
    /**
     * Show toast notification
     */
    showToast(message, type = "info") {
        this.notification.add(message, { type });
    }
    
    /**
     * Show visual feedback for cell update
     */
    showCellUpdate(height, width) {
        const cell = this.containerRef.el?.querySelector(`[data-cell="${height}_${width}"]`);
        if (cell) {
            cell.classList.add('updated');
            setTimeout(() => {
                cell.classList.remove('updated');
            }, 500);
        }
    }
    
    /**
     * Scroll to specific cell
     */
    scrollToCell(height, width) {
        const cell = this.containerRef.el?.querySelector(`[data-cell="${height}_${width}"]`);
        if (cell) {
            cell.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'nearest' });
        }
    }
    
    /**
     * Get cell value for display
     */
    getCellValue(height, width) {
        const cellKey = `${height}_${width}`;
        const value = this.state.matrix.data[cellKey];
        return value ? parseFloat(value).toFixed(2) : '';
    }
    
    /**
     * Format cell value for display
     */
    formatCellValue(height, width) {
        const value = this.getCellValue(height, width);
        return value ? `${this.state.matrix.currency} ${value}` : '';
    }
    
    /**
     * Check if cell is selected
     */
    isSelected(height, width) {
        return this.state.selectedCell && 
               this.state.selectedCell[0] === height && 
               this.state.selectedCell[1] === width;
    }
    
    /**
     * Check if cell is being edited
     */
    isEditing(height, width) {
        return this.state.editingCell && 
               this.state.editingCell[0] === height && 
               this.state.editingCell[1] === width;
    }
    
    /**
     * Handle cell click
     */
    onCellClick(height, width, event) {
        event.stopPropagation();
        this.selectCell(height, width);
    }
    
    /**
     * Handle cell double click
     */
    onCellDoubleClick(height, width, event) {
        event.stopPropagation();
        this.selectCell(height, width);
        this.startEdit();
    }
    
    /**
     * Handle input change during editing
     */
    onInputChange(event) {
        this.state.editValue = event.target.value;
    }
    
    /**
     * Handle input keydown during editing
     */
    onInputKeydown(event) {
        switch(event.key) {
            case 'Enter':
                event.preventDefault();
                this.confirmEdit();
                this.navigateCell(1, 0); // Move down after enter
                break;
            case 'Tab':
                event.preventDefault();
                this.confirmEdit();
                this.navigateCell(0, event.shiftKey ? -1 : 1); // Move left/right after tab
                break;
            case 'Escape':
                event.preventDefault();
                this.cancelEdit();
                break;
        }
    }
    
    /**
     * Fill sample data for testing
     */
    fillSampleData() {
        this.saveUndoState();
        
        this.state.matrix.heights.forEach((height, hIndex) => {
            this.state.matrix.widths.forEach((width, wIndex) => {
                const cellKey = `${height}_${width}`;
                // Generate sample prices based on dimensions
                const basePrice = 100;
                const heightFactor = (hIndex + 1) * 0.2;
                const widthFactor = (wIndex + 1) * 0.15;
                const price = basePrice * (1 + heightFactor + widthFactor);
                this.state.matrix.data[cellKey] = Math.round(price * 100) / 100;
            });
        });
        
        this.calculateStats();
        this.showToast(_t("Sample data filled"), "success");
    }
    
    /**
     * Clear all matrix data
     */
    clearMatrix() {
        this.dialog.add({
            title: _t("Clear Matrix"),
            body: _t("Are you sure you want to clear all matrix data? This action cannot be undone."),
            confirmLabel: _t("Clear"),
            cancelLabel: _t("Cancel"),
            confirm: () => {
                this.saveUndoState();
                this.state.matrix.data = {};
                this.calculateStats();
                this.showToast(_t("Matrix cleared"), "success");
            }
        });
    }
    
    /**
     * Test price lookup
     */
    async testLookup() {
        if (!this.props.matrixId) return;
        
        // Simple test with middle values
        const midHeight = this.state.matrix.heights[Math.floor(this.state.matrix.heights.length / 2)];
        const midWidth = this.state.matrix.widths[Math.floor(this.state.matrix.widths.length / 2)];
        
        if (!midHeight || !midWidth) {
            this.showToast(_t("No dimensions available for testing"), "warning");
            return;
        }
        
        try {
            const result = await this.rpc("/web/dataset/call_kw", {
                model: "config.matrix.price.matrix",
                method: "get_price_for_dimensions",
                args: [this.props.matrixId, parseInt(midHeight), parseInt(midWidth)],
                kwargs: {}
            });
            
            if (result) {
                this.showToast(_t("Test lookup successful: ") + `${this.state.matrix.currency} ${result}`, "success");
            } else {
                this.showToast(_t("No price found for test dimensions"), "warning");
            }
        } catch (error) {
            this.showToast(_t("Test lookup failed: ") + error.message, "danger");
        }
    }
}

// Register the component
EnhancedExcelMatrixEditor.components = {};
