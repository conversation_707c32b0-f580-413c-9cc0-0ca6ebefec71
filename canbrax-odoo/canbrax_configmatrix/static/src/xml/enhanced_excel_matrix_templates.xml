<templates id="template" xml:space="preserve">

    <!-- Enhanced Excel Matrix Editor Template -->
    <t t-name="canbrax_configmatrix.EnhancedExcelMatrixEditor" owl="1">
        <div class="excel-matrix-editor" t-ref="matrixContainer">

            <!-- Loading Overlay -->
            <div t-if="state.loading" class="matrix-loading">
                <div class="loading-spinner"></div>
            </div>

            <!-- Progress Bar -->
            <div t-if="state.progress &gt; 0" class="matrix-progress">
                <div class="matrix-progress-bar" t-att-style="`width: ${state.progress}%`"></div>
            </div>

            <!-- Matrix Toolbar -->
            <div class="matrix-toolbar">
                <div class="toolbar-left">
                    <button class="btn btn-primary" t-on-click="saveMatrix" t-att-disabled="state.loading">
                        <i class="fa fa-save"/> Save Matrix
                    </button>
                    <button class="btn btn-secondary" t-on-click="testLookup" t-att-disabled="state.loading">
                        <i class="fa fa-search"/> Test Lookup
                    </button>
                    <button class="btn btn-info" t-on-click="fillSampleData" t-att-disabled="state.loading">
                        <i class="fa fa-magic"/> Fill Sample
                    </button>
                    <button class="btn btn-warning" t-on-click="clearMatrix" t-att-disabled="state.loading">
                        <i class="fa fa-trash"/> Clear All
                    </button>
                </div>
                <div class="toolbar-right">
                    <div class="matrix-stats">
                        <div class="stat-item">
                            <i class="fa fa-table text-primary"/>
                            <span class="stat-value" t-esc="state.stats.totalCells"/>
                            <span>cells</span>
                        </div>
                        <div class="stat-item">
                            <i class="fa fa-check-circle text-success"/>
                            <span class="stat-value" t-esc="state.stats.filledCells"/>
                            <span>filled</span>
                        </div>
                        <div class="stat-item">
                            <i class="fa fa-pie-chart text-info"/>
                            <span class="stat-value" t-esc="state.stats.completionRate"/>%
                            <span>complete</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Matrix Configuration Panel -->
            <div class="matrix-config-panel">
                <div class="config-row">
                    <div class="config-field">
                        <label>Matrix Name</label>
                        <input type="text"
                               t-model="state.matrix.name"
                               placeholder="Enter matrix name"
                               t-att-disabled="state.loading"/>
                    </div>
                    <div class="config-field">
                        <label>Currency</label>
                        <input type="text"
                               t-model="state.matrix.currency"
                               placeholder="USD"
                               t-att-disabled="state.loading"/>
                    </div>
                </div>
                <div class="config-row">
                    <div class="config-field">
                        <label>Heights (comma-separated)</label>
                        <input type="text"
                               t-att-value="state.matrix.heights.join(',')"
                               placeholder="1000,1200,1500,1800"
                               t-att-disabled="state.loading"/>
                    </div>
                    <div class="config-field">
                        <label>Widths (comma-separated)</label>
                        <input type="text"
                               t-att-value="state.matrix.widths.join(',')"
                               placeholder="600,800,1000,1200"
                               t-att-disabled="state.loading"/>
                    </div>
                </div>
            </div>

            <!-- Matrix Grid Container -->
            <div class="matrix-grid-container">
                <table class="matrix-grid">
                    <thead>
                        <tr>
                            <th class="corner-cell">H\W</th>
                            <th t-foreach="state.matrix.widths" t-as="width" t-key="width_index">
                                <t t-esc="width"/>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr t-foreach="state.matrix.heights" t-as="height" t-key="height_index">
                            <th class="row-header">
                                <t t-esc="height"/>
                            </th>
                            <td t-foreach="state.matrix.widths" t-as="width" t-key="width_index"
                                class="matrix-cell"
                                t-att-class="{
                                    'selected': isSelected(height, width),
                                    'editing': isEditing(height, width),
                                    'edited': getCellValue(height, width) !== '',
                                    'empty': getCellValue(height, width) === ''
                                }"
                                t-att-data-cell="`${height}_${width}`"
                                t-on-click="(ev) => this.onCellClick(height, width, ev)"
                                t-on-dblclick="(ev) => this.onCellDoubleClick(height, width, ev)">

                                <!-- Edit Input -->
                                <input t-if="isEditing(height, width)"
                                       type="number"
                                       step="0.01"
                                       class="cell-input"
                                       t-model="state.editValue"
                                       t-on-keydown="onInputKeydown"
                                       t-on-change="onInputChange"/>

                                <!-- Display Value -->
                                <div t-else="" class="cell-value" t-att-class="{'empty': !getCellValue(height, width)}">
                                    <t t-if="getCellValue(height, width)" t-esc="formatCellValue(height, width)"/>
                                    <t t-else="">-</t>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Context Menu -->
            <div t-ref="contextMenu" class="matrix-context-menu" style="display: none;">
                <div class="context-menu-item" t-on-click="copySelection">
                    <i class="fa fa-copy"/> Copy
                </div>
                <div class="context-menu-item" t-on-click="pasteSelection">
                    <i class="fa fa-paste"/> Paste
                </div>
                <div class="context-menu-item" t-on-click="cutSelection">
                    <i class="fa fa-cut"/> Cut
                </div>
                <div class="context-menu-separator"></div>
                <div class="context-menu-item" t-on-click="deleteSelectedCells">
                    <i class="fa fa-trash"/> Delete
                </div>
                <div class="context-menu-separator"></div>
                <div class="context-menu-item" t-on-click="fillSampleData">
                    <i class="fa fa-magic"/> Fill Sample Data
                </div>
            </div>

            <!-- Keyboard Hints -->
            <div class="keyboard-hint">
                <div><strong>Ctrl+S</strong> Save | <strong>F2/Enter</strong> Edit | <strong>Arrows</strong> Navigate</div>
                <div><strong>Ctrl+C/V/X</strong> Copy/Paste/Cut | <strong>Del</strong> Delete | <strong>Ctrl+Z</strong> Undo</div>
            </div>
        </div>
    </t>

    <!-- Matrix Category Management Template -->
    <t t-name="canbrax_configmatrix.MatrixCategoryCard" owl="1">
        <div class="matrix-category-card">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i t-att-class="props.category.icon"/>
                        <t t-esc="props.category.name"/>
                    </h5>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-primary" t-on-click="onAddMatrix">
                            <i class="fa fa-plus"/> Add Matrix
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="category-stats">
                        <div class="stat-item">
                            <span class="stat-label">Matrices:</span>
                            <span class="stat-value" t-esc="props.category.matrix_count"/>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Completion:</span>
                            <span class="stat-value" t-esc="props.category.completion_rate"/>%
                        </div>
                    </div>

                    <div t-if="props.category.matrices.length &gt; 0" class="matrix-list">
                        <div t-foreach="props.category.matrices" t-as="matrix" t-key="matrix.id"
                             class="matrix-item">
                            <div class="matrix-info">
                                <strong t-esc="matrix.name"/>
                                <small class="text-muted" t-esc="matrix.last_updated"/>
                            </div>
                            <div class="matrix-actions">
                                <button class="btn btn-sm btn-secondary" t-on-click="() => this.onEditMatrix(matrix)">
                                    <i class="fa fa-edit"/> Edit
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" t-on-click="() => this.onViewMatrix(matrix)">
                                    <i class="fa fa-eye"/> View
                                </button>
                            </div>
                        </div>
                    </div>

                    <div t-else="" class="empty-state">
                        <i class="fa fa-table fa-2x text-muted"/>
                        <p class="text-muted">No matrices in this category</p>
                        <button class="btn btn-outline-primary" t-on-click="onAddMatrix">
                            <i class="fa fa-plus"/> Create First Matrix
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </t>

    <!-- Template Statistics Dashboard -->
    <t t-name="canbrax_configmatrix.TemplateStatsDashboard" owl="1">
        <div class="template-stats-dashboard">
            <div class="row">
                <div class="col-md-3" t-foreach="props.stats" t-as="stat" t-key="stat.key">
                    <div class="stat-card">
                        <div class="card">
                            <div class="card-body text-center">
                                <div class="stat-icon">
                                    <i t-att-class="stat.icon + ' fa-2x ' + stat.color"/>
                                </div>
                                <h3 class="stat-number" t-att-class="stat.color" t-esc="stat.value"/>
                                <p class="stat-label" t-esc="stat.label"/>
                                <div t-if="stat.progress !== undefined" class="progress mt-2">
                                    <div class="progress-bar"
                                         t-att-class="stat.color.replace('text-', 'bg-')"
                                         t-att-style="`width: ${stat.progress}%`">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>

    <!-- Import Progress Modal -->
    <t t-name="canbrax_configmatrix.ImportProgressModal" owl="1">
        <div class="modal fade show" style="display: block;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fa fa-upload"/> Importing Excel Matrices
                        </h5>
                    </div>
                    <div class="modal-body">
                        <div class="import-progress">
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     t-att-style="`width: ${state.progress}%`">
                                    <t t-esc="state.progress"/>%
                                </div>
                            </div>

                            <div class="import-status">
                                <p><strong>Current Step:</strong> <span t-esc="state.currentStep"/></p>
                                <p><strong>Processing:</strong> <span t-esc="state.currentFile"/></p>
                            </div>

                            <div t-if="state.logs.length &gt; 0" class="import-logs">
                                <h6>Import Log:</h6>
                                <div class="log-container">
                                    <div t-foreach="state.logs" t-as="log" t-key="log_index"
                                         class="log-entry" t-att-class="log.level">
                                        <small class="timestamp" t-esc="log.timestamp"/>
                                        <span class="message" t-esc="log.message"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button t-if="state.completed"
                                class="btn btn-primary"
                                t-on-click="onComplete">
                            <i class="fa fa-check"/> Complete
                        </button>
                        <button t-else=""
                                class="btn btn-secondary"
                                t-on-click="onCancel"
                                t-att-disabled="state.cancelling">
                            <i class="fa fa-times"/> Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </t>

    <!-- Matrix Configuration Wizard -->
    <t t-name="canbrax_configmatrix.MatrixConfigWizard" owl="1">
        <div class="matrix-config-wizard">
            <div class="wizard-header">
                <h4>Matrix Configuration Wizard</h4>
                <div class="wizard-steps">
                    <div class="step" t-att-class="{'active': state.currentStep === 1, 'completed': state.currentStep &gt; 1}">
                        <span class="step-number">1</span>
                        <span class="step-label">Basic Info</span>
                    </div>
                    <div class="step" t-att-class="{'active': state.currentStep === 2, 'completed': state.currentStep &gt; 2}">
                        <span class="step-number">2</span>
                        <span class="step-label">Dimensions</span>
                    </div>
                    <div class="step" t-att-class="{'active': state.currentStep === 3, 'completed': state.currentStep &gt; 3}">
                        <span class="step-number">3</span>
                        <span class="step-label">Data Entry</span>
                    </div>
                    <div class="step" t-att-class="{'active': state.currentStep === 4}">
                        <span class="step-number">4</span>
                        <span class="step-label">Review</span>
                    </div>
                </div>
            </div>

            <div class="wizard-content">
                <!-- Step 1: Basic Information -->
                <div t-if="state.currentStep === 1" class="wizard-step">
                    <h5>Basic Matrix Information</h5>
                    <div class="form-group">
                        <label>Matrix Name</label>
                        <input type="text" class="form-control" t-model="state.matrix.name"/>
                    </div>
                    <div class="form-group">
                        <label>Category</label>
                        <select class="form-control" t-model="state.matrix.category_id">
                            <option value="">Select a category</option>
                            <option t-foreach="props.categories" t-as="category" t-key="category.id"
                                    t-att-value="category.id" t-esc="category.name"/>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Description</label>
                        <textarea class="form-control" t-model="state.matrix.description"/>
                    </div>
                </div>

                <!-- Step 2: Dimensions -->
                <div t-if="state.currentStep === 2" class="wizard-step">
                    <h5>Matrix Dimensions</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <label>Height Values</label>
                            <textarea class="form-control"
                                      t-model="state.matrix.heights_text"
                                      placeholder="1000,1200,1500,1800,2000"/>
                            <small class="form-text text-muted">Comma-separated height values</small>
                        </div>
                        <div class="col-md-6">
                            <label>Width Values</label>
                            <textarea class="form-control"
                                      t-model="state.matrix.widths_text"
                                      placeholder="600,800,1000,1200,1500"/>
                            <small class="form-text text-muted">Comma-separated width values</small>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Data Entry -->
                <div t-if="state.currentStep === 3" class="wizard-step">
                    <h5>Matrix Data Entry</h5>
                    <div class="data-entry-options">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary"
                                    t-on-click="() => this.setDataEntryMode('manual')">
                                <i class="fa fa-keyboard-o"/> Manual Entry
                            </button>
                            <button type="button" class="btn btn-outline-primary"
                                    t-on-click="() => this.setDataEntryMode('import')">
                                <i class="fa fa-upload"/> Import Data
                            </button>
                            <button type="button" class="btn btn-outline-primary"
                                    t-on-click="() => this.setDataEntryMode('sample')">
                                <i class="fa fa-magic"/> Generate Sample
                            </button>
                        </div>
                    </div>

                    <!-- Manual Entry Grid -->
                    <div t-if="state.dataEntryMode === 'manual'" class="manual-entry">
                        <t t-call="canbrax_configmatrix.MatrixDataGrid"/>
                    </div>

                    <!-- Import Option -->
                    <div t-if="state.dataEntryMode === 'import'" class="import-option">
                        <div class="upload-area">
                            <input type="file" class="form-control-file" accept=".csv,.xlsx"/>
                            <p class="text-muted">Upload CSV or Excel file with matrix data</p>
                        </div>
                    </div>

                    <!-- Sample Generation -->
                    <div t-if="state.dataEntryMode === 'sample'" class="sample-generation">
                        <div class="form-group">
                            <label>Base Price</label>
                            <input type="number" class="form-control" t-model="state.sampleConfig.basePrice"/>
                        </div>
                        <div class="form-group">
                            <label>Price Variation (%)</label>
                            <input type="number" class="form-control" t-model="state.sampleConfig.variation"/>
                        </div>
                        <button class="btn btn-primary" t-on-click="generateSampleData">
                            <i class="fa fa-magic"/> Generate Data
                        </button>
                    </div>
                </div>

                <!-- Step 4: Review -->
                <div t-if="state.currentStep === 4" class="wizard-step">
                    <h5>Review and Confirm</h5>
                    <div class="review-summary">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Matrix Information</h6>
                                <dl class="row">
                                    <dt class="col-sm-4">Name:</dt>
                                    <dd class="col-sm-8" t-esc="state.matrix.name"/>
                                    <dt class="col-sm-4">Category:</dt>
                                    <dd class="col-sm-8" t-esc="getCategoryName(state.matrix.category_id)"/>
                                    <dt class="col-sm-4">Dimensions:</dt>
                                    <dd class="col-sm-8">
                                        <t t-esc="state.matrix.heights.length"/> x <t t-esc="state.matrix.widths.length"/>
                                    </dd>
                                </dl>
                            </div>
                            <div class="col-md-6">
                                <h6>Data Summary</h6>
                                <dl class="row">
                                    <dt class="col-sm-4">Total Cells:</dt>
                                    <dd class="col-sm-8" t-esc="state.matrix.heights.length * state.matrix.widths.length"/>
                                    <dt class="col-sm-4">Filled Cells:</dt>
                                    <dd class="col-sm-8" t-esc="Object.keys(state.matrix.data).length"/>
                                    <dt class="col-sm-4">Completion:</dt>
                                    <dd class="col-sm-8">
                                        <t t-esc="Math.round((Object.keys(state.matrix.data).length / (state.matrix.heights.length * state.matrix.widths.length)) * 100)"/>%
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="wizard-footer">
                <button t-if="state.currentStep &gt; 1"
                        class="btn btn-secondary"
                        t-on-click="previousStep">
                    <i class="fa fa-arrow-left"/> Previous
                </button>
                <button t-if="state.currentStep &lt; 4"
                        class="btn btn-primary"
                        t-on-click="nextStep">
                    Next <i class="fa fa-arrow-right"/>
                </button>
                <button t-if="state.currentStep === 4"
                        class="btn btn-success"
                        t-on-click="createMatrix">
                    <i class="fa fa-check"/> Create Matrix
                </button>
            </div>
        </div>
    </t>

</templates>
